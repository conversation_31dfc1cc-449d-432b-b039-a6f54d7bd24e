var bodyParser = require('body-parser')
var fs = require('fs');
var path = require('path');
var cheerio = require('cheerio')
var express = require('express')
var jsonParser = bodyParser.json({ limit:1024*1024*20, type: 'application/json'})
var m = require("./middlewares_new.js")
var resources = require("./resources.js")
var querystring = require('querystring')
var router = express.Router()
var unirest = require('unirest')
var uaParser = require('ua-parser-js')
var Cookies = require( "cookies" )
var jwt = require('jsonwebtoken')
var swaggerUi = require('swagger-ui-express')
var generateSwaggerJSON = require('../cms/v3.0/api/helpers/swagger.helper.js')
var authenticate = require('../cms/v3.0/api/post/authenticate.js');
var apiRouter = require('../cms/v3.0/api/router.js');

const { serve } = require('inngest/express');
const inngest = require('./config.inngest.js');
const handlers = require('../src/utils/inngest/index.js');

function renderPageNotFound(req, res){
	res.status(404);
	res.sendFile('404.html', { root: __dirname + '/../cms/' + req.session.site.version + '/pages/error_pages/'});
}

function renderResourceNotFound(req, res){
	var error404 = cms.config.error.page404;
	error404 = error404.replace(/{req.headers.host}/g, req.headers.host);
	error404 = error404.replace(/{req.url}/g, req.url);
	res.status(404).send(error404);
}

function checkGetParams(req, res, next){
	if(typeof(req.params) === 'undefined'){
		res.status(422);
		res.write('Missing parameters: Expecting job queue object, but found nothing');
		res.end();
	}
	else{
		next();
	}
}


// override the basic express.static with custom version as we need to serve static assets
// through different version of cms, version will be stored in the session
express.static = require('./serve-static.js');


router.use(bodyParser.urlencoded({limit: '50mb', extended: true }))		// parse application/x-www-form-urlencoded
router.use(bodyParser.json({ limit:1024*1024*20, type:'application/json' }))						// parse application/json

// Swagger Documentation
router.use('/swagger', swaggerUi.serve);
router.get('/swagger', (req, res) => {
	const cookies = new Cookies(req, res);
	const accessToken = cookies.get('kAccTkn');
	const apiKey = req.query.apiKey || null;
	const isInternal = req.query.internal === 'true' || false;
	const swaggerJSON = generateSwaggerJSON(isInternal) || null;
	const swaggerOptions = {
		docExpansion: "list",
		deepLinking: false,
		displayRequestDuration: true,
		showMutatedRequest: true,
		filter: true,
		jsonEditor: true,
		syntaxHighlight: { theme: "agate" },
		layout: "BaseLayout"
	};

	if (!swaggerJSON) {
		return res.status(400).json({ status: { code: 400, message: "Not found. Please Ensure the Swagger JSON Exists." } }).end();
	} 

	if(accessToken) {
		var decoded = jwt.verify(accessToken, cms.config.jwtKey);
		if(decoded && decoded.kuser && decoded.kuser.kuemail && decoded.kuser.kuid){
			return swaggerUi.setup(swaggerJSON, { swaggerOptions: swaggerOptions })(req, res);
		}
	}
	else if(apiKey){
		var authenticateParam = {
			'request': req,
			'response': res,
			'apiKey': apiKey
	}
	authenticate.authenticateApi(authenticateParam, req)
		.then(() => swaggerUi.setup(swaggerJSON, { swaggerOptions: swaggerOptions })(req, res))
		.catch(() => {
			res.status(401).json({ status: { code: 401, message: "Not authorized. You are not authorized to access the requested resource" } }).end();
		});
	}
	else {
		res.status(401).json({ status: { code: 401, message: "Not authorized. You are not authorized to access the requested resource" } }).end();
	}
});

// Inngest middleware
router.use('/api/inngest', serve({ client: inngest, baseUrl: process.env.INNGEST_API_URL, functions: handlers }));

/**
 * https://stackoverflow.com/questions/18538537/time-requests-in-nodejs-express
 * req and response time tracker
 * Added by Anuraja May-04-2019
 * http://www.sheshbabu.com/posts/measuring-response-times-of-express-route-handlers/
 *
 * */

router.use(function (req, res, next) {

	//Added to stop logging in elastic temporarily
	next();
	return true;

	//To check in valid URL or malformed URL and return to avoid the error being displayed in the browser
	let err = false;
	try {
        decodeURIComponent(req.path)
    }
    catch(e) {
        err = e;
    }
    if (err){
        return renderResourceNotFound(req, res);
    }
	res.set('X-K-Server', cms.config.servername);
	if(req.path.match(/\/(css|js|images|fonts|updateelastic|keeplive|resources|socket.io)/) || req.path == '/'){
	//if(!req.path.match(/\/(api|workflow)/)){
		next();
		return true;
	}
	const startHrTime = process.hrtime();
	var reqId = new Date().getTime() + '.' + Math.floor(1000 + Math.random() * 9000);
	req['reqid'] = reqId;
	var params = req.body;
	var reqBody = {};
	if(typeof(params)=='object') {
		Object.keys(params).forEach(function(reqData){
			if(JSON.stringify(params[reqData]).length <= 1024 && !reqData.match(/apiKey/i)) reqBody[reqData] = params[reqData];
		})
		reqBody = JSON.stringify(reqBody);
	}else if (typeof(params)=='string') {
		if(params.length <= 1024) reqBody = params;
	}
	var reqQueryparams = req.query;
	var reqQuery = {};
	if(typeof(reqQueryparams)=='object') {
		Object.keys(reqQueryparams).forEach(function(reqQueryData){
			if(JSON.stringify(reqQueryparams[reqQueryData]).length <= 1024 && !reqQueryData.match(/apiKey/i)) reqQuery[reqQueryData] = reqQueryparams[reqQueryData];
		})
		reqQuery = JSON.stringify(reqQuery);
	}else if (typeof(reqQueryparams)=='string') {
		if(reqQueryparams.length <= 1024) reqQuery = reqQueryparams;
	}
	if(req && req.headers && req.headers.referer) reqQuery = 'Req URL :'+req.headers.referer +' '+ reqQuery
	var reqLogParam = {
		"reqid": req.reqid,
		"reqPath": req.path,
		"lastUpdated": new Date().getTime(),
		"elapsedTime": 0.0,
		"body": reqBody,
		"reqQuery": reqQuery,
		"servername": cms.config.servername
	};
	if (process.env.NODE_ENV == 'production') {
		updateElastic(reqLogParam);
	}
	res.on("finish", () => {
		/*const elapsedHrTime = process.hrtime(startHrTime);
		const elapsedTimeInMs = elapsedHrTime[0] * 1000 + elapsedHrTime[1] / 1e6;
		if(req.session && req.session!=undefined && req.session.kuser && req.session.kuser!=undefined && req.session.kuser.kuemail && req.session.kuser.kuemail != undefined){
			reqLogParam.usermail = req.session.kuser.kuemail;
		}else{
			reqLogParam.usermail = req.reqid;
		}
		reqLogParam.elapsedTime = Math.round(elapsedTimeInMs/1000*100)/100;
		reqLogParam.lastUpdated = new Date().getTime();*/
		// deleteElasticLog(reqLogParam);
	});
	next();
});


// check if requested domain is available/configured
// and if request is authorized before doing any further actions
/*router.all('*', function (req, res, next) {
	res.redirect('http://maintenance.kriyadocs.com');
});*/
router.all('*', [m.domainCheck, m.getUserInfo ,m.isAuthenticated], function (req, res, next) {
	next();
});

router.use(express.static('cms'));	// deliver static files
router.get('/resources/*', checkGetParams, resources.getResource);
router.get('/docs/*', resources.getDocs);

// display a file not found (404) error if the requested static file is not present on the server
router.get(['/css/*','/js/*','/fonts/*','/images/*'], function (req, res, next) {
	renderResourceNotFound(req, res);
});

// handle all api calls, GET/POST/PUT/DELETE/...
router.use('/api', apiRouter);

router.post('/workflow', function(req, res){
	var reqMethod = req.method.toLowerCase();
	// return code 400 as the expected input 'status' is not provided
	if (!req.body.status){
		res.status(400).json({status: {code: 400, message: "unexpected 'status' value"}});
		return false;
	}
	// PM: if status is a string, convert it into JSON object as the system expects JSON only
	var statusString = req.body.status;
	try{
		if (typeof(statusString) === 'string'){
			req.body.status = JSON.parse(statusString);
		}
	}
	catch(e){
		req.body.status = statusString;
	}

	try {
		var workflow = require('../cms/' + req.session.site.version + '/api/post/workflow.js');
		workflow.processRequest(req, res)
			.then(function(info){
				res.status(200).json({status: {code: 200, message: "data received", data: JSON.stringify(info)}});
				//res.send(info);
			})
			.catch(function(e) {
				console.log('workflow failed');
				res.status(400).json({status: {code: 400, message: e}});
			});
	}
	catch(e){
		res.status(404).json({status: {code: 404, message: "requested -  resource not found"}});
	}
});


// display article not ready to view page
router.get(['/not_ready', '/with_author'], function (req, res, next) {
	res.status(200);
	res.sendFile('index.html', { root: __dirname + '/../cms/' + req.session.site.version + '/pages' + req.url.toLowerCase() + '/'});
});

router.post('/uploadFiles', resources.uploadResource);
router.post('/getJobStatus', resources.getJobStatus);
router.get('/getS3UploadCredentials', resources.getS3UploadCredentials);

router.get('/*', function (req, res) {
	var domainName = req.headers.host;
	var pageName = req.url.toLowerCase();
	var userAgent = uaParser(req.headers['user-agent']);
    var userAgentString = JSON.stringify(userAgent, null, '  ');
	if (pageName == '/') {
		pageName = cms.config.domains[domainName].page.default ? cms.config.domains[domainName].page.default : 'welcome';
	}
	pageName = pageName.replace(/^\/([^\/\?]+).*$/gi, '$1');
	
	//if page name is welcome and session is available then redirect to the dashboard page
	//Added by jagan
	var cookies = new Cookies(req, res);
	var userObj = null;
	if(cookies.get('kAccTkn')){
		/*var accTkn = cookies.get('kAccTkn');
		try {
			var userObj = jwt.verify(accTkn, cms.config.jwtKey);
		}catch(e) {}*/
		if (req && req.headers && req.headers.kuser) {
			userObj = {
				kuser: req.headers.kuser
			};				
		}

		//Prevent opening other pages when session create by key access - jagan
		if(userObj && userObj.kuser && userObj.kuser.keyAccess && userObj.kuser.keyAccess.pageName.indexOf(pageName) < 0  && !req.query.key && pageName != "logout"){
			res.status(404);
			res.sendFile('404.html', { root: __dirname + '/../cms/' + req.session.site.version + '/pages/error_pages/'});
			return false;
		}
		
		if(userObj && userObj.kuser && pageName == 'welcome'){
			// Added by Anuraja to redirect new dasbaord for ppl and medwave
			var redirectTo = '/dashboard';
			res.redirect(redirectTo);
			return false;
		}
	}
	if(pageName == 'dashboard_new'){
		// Added by Anuraja to redirect new dasbaord for ppl and medwave
		var redirectTo = '/dashboard';
		res.redirect(redirectTo);
		return false;
	}
	// if the first part of the request url has a valid page name AND
	// the page has been configured previously
	if (cms.config.domains[domainName].page[pageName] > -1){
		try {
			var pageRenderer = require('../cms/' + req.session.site.version + '/pages/' + pageName + '/index.js');
			pageRenderer.renderPage(req, res);
			
            if (userObj && userObj.kuser) {
				var user = userObj.kuser;
				var fullName = user.kuname.first + ' ' + user.kuname.last;
				//TODO: Need to migrate to ELK
                expLogger(user.kuid + '|' + fullName + '|' + pageName + '|' + 'User agent:' + userAgentString, { 'logFileName': 'ua_logs', 'addDate': 'true', 'req': req });
			}else if(req && req.query && req.query.key && req.query.email && userAgentString){
				//TODO: Need to migrate to ELK
				expLogger(req.query.email + '|' + pageName+ '|' + 'User agent:' + userAgentString, { 'logFileName': 'ua_logs', 'addDate': 'true', 'req': req });
			}
		}
		catch (e) {
			logger.error(`${pageName} rendering failed...`, { reqData: extractReqData(req), err: e, fileName: "routers", pageName: pageName });
			renderPageNotFound(req, res);
		}
	}
	// if the request has a referer, it means that the request has come from the page after it loaded
	// and that the request does not have proper path to fetch the file, so just send a 404 for these requests
	else if (typeof(req.headers.referer) !== 'undefined'){
		renderResourceNotFound(req, res);
	}
	else {
		renderPageNotFound(req, res);
	}
});

function getDataUsingXpath(url) {
	return new Promise(function (resolve, reject) {
		unirest.post(url)
			.type('xml')
			.headers({
				'Accept': 'application/xml',
				'Content-Type': 'application/xml'
			})
			.end(response => {
				if (!response.body) {
					resolve('');
				} else if (response.error) {
					resolve('');
				} else {
					resolve(response.body);
				}
			});
	});
}

/**
 *
 * function to update elastic with req timing and url
 * @param {object} data
 */
function updateElastic(data) {
	var dataToPost = {
		"index": cms.config.elasticInfo.reqLogIndex,
		"table": cms.config.elasticInfo.reqLogTable,
		"postContent": true,
		"data": data,
		"id": data.reqid
	};
	var updateElasticLog = cms.config.elasticlog.url + cms.config.elastic.updateArticle + data.reqid	
		unirest.post(updateElasticLog)
			.type('json')
			.send(JSON.stringify(dataToPost))
			.headers({
				'Accept': 'application/json',
				'Content-Type': 'application/json'
			})
			.end(response => {
			})
}
/**
 * function to delete request logs elastic data
 * @param {*} data
 */
function deleteElasticLog(data){
	var dataToPost = {
		"index": cms.config.elasticInfo.reqLogIndex,
		"table": cms.config.elasticInfo.reqLogTable,
		"id": data.reqid,
		"processType" : "deleteDocument",
		"securityCode" : cms.config.elasticSecurityCode,
		"urlToPost": cms.config.elasticUrl+'/api/elasticprocess'
	};
	var updateElasticURL = cms.config.requestMonitor + '/deleterequest';
	var request = unirest("POST", updateElasticURL);
	request.headers({
		"cache-control": "no-cache",
		"content-type": "application/json"
	});
	request.send(dataToPost);
	request.end(function (response) {
	});
}
module.exports = router
