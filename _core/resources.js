var crypto = require('crypto')
	, formidable = require('formidable')
	, fs = require('fs')
	, path = require("path")
	, mkdirp = require('mkdirp')
	, unirest = require('unirest')
	, s3 = require('s3')
	, uuidv4 = require('uuid/v4')
	, sanitize = require("sanitize-filename");

const allowedFileExtensions = new Set([
	"csv",
	"htm",
	"html",
	"json",
	"md",
	"mdx",
	"rtf",
	"tex",
	"text",
	"textile",
	"txt",
	"xhtml",
	"xml",
	"xls",
	"xlsx",
	"pdf",
	"zip",
	"bmp",
	"exif",
	"eps",
	"gif",
	"ico",
	"jng",
	"jpeg",
	"jpg",
	"jfif",
	"jp2",
	"jps",
	"int",
	"indd",
	"tiff",
	"tif",
	"vtf",
	"ai",
	"eps",
	"svg",
	"sxd",
	"png",
	"psd",
	"raw",
	"webp",
	"wdp",
	"iff",
	"rgb",
	"rgba",
	"heic",
	"heif",
	"doc",
	"docx",
	"ppt",
	"pptx",
	"mp4",
	"mp3",
	"mp2",
	"mpeg",
	"wmv",
	"webm",
	"mov",
	"avi",
	"bib",
	"cls",
	"m4v",
	"eml",
	"msg",
	"R",
	"stl"
])

module.exports = {

	getResource: function(req, res){
		req.url = decodeURIComponent(req.url);
		if (!/localhost/.test(req.headers.host) && cms.config.resourceManager && cms.config.resourceManager.kriyadocsResources){
			res.redirect(cms.config.resourceManager.kriyadocsResources + req.url);
			res.end();
			return;
		}
		var downloadFromServer = false;
		if (req.query && req.query.method && req.query.method == 'server'){
			req.url = req.url.replace('?method=server', '');
			downloadFromServer = true;
		}
		var cmsVersion = req.session.site.version;
		var resourceDir = path.dirname(req.url);
		var resourceBucket = resourceDir.replace(/^[.\/]*resources\//, '');
		var relativePath = './_cache/resources/' + resourceBucket + '/' + path.basename(req.url);
		fs.access(relativePath, fs.constants.R_OK, (err) => {
			  if(err || downloadFromServer){
				if (downloadFromServer || err.code === "ENOENT") {
						var data =  {"data":
							{
								'Key'  : path.basename(req.url),
								'Bucket' :  resourceBucket
							}
						};
						downloadResource(data, cmsVersion)
							.then(function(resData){
							//Upated by Anuraja to download file into a stream to avoid the server hanging while read the large files
							fs.stat(resData, function (err, stat) {
								if (err) {
									res.status(404);
									res.write('ERROR : file read error in AWS')
									res.end();
									return true;
								}
								//Added by ANuraja to check if file is more than 500mb if so will not allow to download
								if (stat.size > 524288000) {
									res.status(404);
									res.write('ERROR : file size is more than 500 mb')
									res.end();
									return true;
								}
								const stream = fs.createReadStream(resData);
								if (/\.pdf$/.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'application/pdf'
									});
								} else if (/\.xml$/.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'application/xml'
									});
								} else if (/\.htm$/.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'text/html'
									});
								} else if (/\.(jpeg|jpg|png)$/i.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'image/jpeg'
									});
								} else if (/\.(svg)$/i.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'image/svg+xml'
									});
								} else if (/\.(js)$/i.test(req.url)) {
									res.writeHead(200, {
										'Content-Type': 'text/javascript'
									});
								} else {
									res.writeHead(200, {
										'Content-Type': 'application/octet-stream'
									});
								}
								stream.on("data", function (data) {
									res.write(data);
								})
								stream.on('end', (data) => {
									res.end();
								});
								stream.on('error', (err) => {
									res.status(404);
									res.write('ERROR : file read error in AWS')
									res.end();
								});
							})
							})
							.catch(function(err){
								res.status(404);
								res.write('ERROR : file read error in AWS')
								res.end();
							});
				}else{
					res.write('file not Exist retriving from AWS...');
					res.end();
				}
			} else {
				fs.stat(relativePath, function (err, stat) {
					if (err) {
						res.status(404);
						res.write('ERROR : file read error in AWS')
						res.end();
						return true;
					}
					//Added by ANuraja to check if file is more than 500mb if so will not allow to download
					if (stat.size > 524288000) {
						res.status(404);
						res.write('ERROR : file size is more than 500 mb')
						res.end();
						return true;
					}
					const stream = fs.createReadStream(relativePath)
					if (/\.pdf$/.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'application/pdf'
						});
					} else if (/\.xml$/.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'application/xml'
						});
					} else if (/\.htm$/.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'text/html'
						});
					} else if (/\.(jpeg|jpg|png)$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'image/jpeg'
						});
					} else if (/\.(svg)$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'image/svg+xml'
						});
					} else if (/\.(js)$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'text/javascript'
						});
					} else {
						res.writeHead(200, {
							'Content-Type': 'application/octet-stream'
						});
					}
					stream.on("data", function (data) {
						res.write(data);
					})
					stream.on('end', () => {
						res.end();
					});
					stream.on('error', (err) => {
						res.status(404);
						res.write('ERROR : file read error in AWS')
						res.end();
					});
				})
			}
		});
	},

	getDocs: function(req, res){
		req.url = decodeURIComponent(req.url);
		var cmsVersion = req.session.site.version;
		if ((req.url == '/docs') || (req.url == '/docs/')){
			req.url = './docs/index.html';
		}
		else {
			req.url = '.' + req.url;
		}
		fs.access(req.url, fs.constants.R_OK, (err) => {
			if(err){
				res.status(404);
				res.write('ERROR : file not found')
				res.end();
				return true;
			}
			else {
				fs.stat(req.url, function (err, stat) {
					if (err) {
						res.status(404);
						res.write('ERROR : file not found')
						res.end();
						return true;
					}
					//Added by ANuraja to check if file is more than 5mb if so will not allow to download
					if (stat.size > 5242880) {
						res.status(404);
						res.write('ERROR : file size is more than 5 mb')
						res.end();
						return true;
					}
					if (/\.pdf$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'application/pdf'
						});
					}
					else if (/\.xml$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'application/xml'
						});
					}
					else if (/\.html?$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'text/html'
						});
					}
					else if (/\.(jpeg|jpg|png)$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'image/jpeg'
						});
					}
					else if (/\.svg$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'image/svg+xml'
						});
					}
					else if (/\.js$/i.test(req.url)) {
						res.writeHead(200, {
							'Content-Type': 'text/javascript'
						});
					}
					else {
						res.writeHead(200, {
							'Content-Type': 'application/octet-stream'
						});
					}
					const stream = fs.createReadStream(req.url);
					stream.on("data", function (data) {
						res.write(data);
					})
					stream.on('end', () => {
						res.end();
					});
					stream.on('error', (err) => {
						res.status(404);
						res.write('ERROR : file not found')
						res.end();
					});
				})
			}
		});
	},

	// old method used to upload images, which again uploads to resource manager and get back -NOT IN USE NOW -JAI
	uploadResource: function(req, res) {
		// create an incoming form object
		var FormData = require('form-data');
		var request = require('request');
		var form = new formidable.IncomingForm();
		var customerDetails = {};
		// specify that we want to allow the user to upload multiple files in a single request
		form.multiples = true;
		form.parse(req, function(err, fields, files) {
		}).on('field', function(name, field) {
			req.body[name] = field;
		}).on('file', function(field, file) {
			fileName = file.name;
			var formData = new FormData();
			var inputInfo = {
				"article" : {
					"cmsID" : req.body.doi,
					"filePath": ""				/* path where the XML is present on eXist-db */
				},
				"customer" : req.body.customer,
				"project" : req.body.project,
				"requestType": "single",
				"fileExtn": file.name.replace(/^.*\./, ''),
				"files" : [
					{
					  "name": file.name,
					  "type": "image",
					  "processingInfo": {
						"format": "tif",
						"dpi": "300",
						"colorprofile": "",
						"colorspace": "cmyk"
					  }
					}
				]
			};
			formData.append('input-info',JSON.stringify(inputInfo));
			formData.append('file',fs.createReadStream(file['path']),file['name']);
			unirest.post(cms.config.resourceManager.upload)
				.headers({'Content-Type': 'multipart/form-data'})
				.field('input-info', JSON.stringify(inputInfo)) // Form field
				.attach('file', fs.createReadStream(file['path'])) // Attachment
				.end(function (response) {
					if (IsJsonString(response.body)){
						var status = JSON.parse(response.body);
						if (status.ids){
							res.send(status.ids);
							res.end();
						}else{
							res.status(404);
							res.send('Failed');
							res.end();
						}
					} else {
						logger.error(`Failed to upload figure: ${fileName}`, { reqData: extractReqData(req), fileName: "resources" });
						res.status(404);
						res.send('Failed');
						res.end();
					}
				});
		});
		form.on('error', function(err) {
			//console.log('An error has occured: \n' + err);
		});
		form.on('end', function() {
			//res.end(fileName);
		});
	},

	getJobStatus: function(req, res) {
		jobID = req.body.jobID;
		var cmsVersion = req.session.site.version;
		unirest.post(cms.config.resourceManager.jobStatus)
		.headers({'Accept': 'application/json', 'Content-Type': 'application/json'})
		.send({"data":{"id":jobID}})
		.end(function (resp) {
			if (resp.body && resp.body.results){
				var imageStatus = resp.body.results[0].status;
				var percentage = resp.body.results[0].percent + 20;
				var percent = "Uploading: " + percentage + '%';
				if (imageStatus == "completed" || percentage > 90){
					var inputInfo = {
						"article" : {
							"cmsID" : req.body.doi,
							"filePath": ""
						},
						"customer" : req.body.customer,
						"project" : req.body.project,
					};
					var data =  {
						"data" :{
							'Key'  : jobID + '.jpg',
							'Bucket' :  inputInfo.customer + '/' + inputInfo.article.cmsID
						}
					};
					downloadResource(data, cmsVersion)
						.then(function(resData){
							resData = resData.replace('./_cache', '');
							res.send({'src': resData});
							res.end();
						})
						.catch(function(err){
							res.status(404);
							res.write('Failed')
							res.end();
						});
				}else{
					res.send(percent);
					res.end();
				}
			}else{
				res.send('Failed');
				res.end();
			}
		});
	},

	getS3UploadCredentials: function(req, res) {
		if (req.query.filename !== undefined && req.query.filename !== null) {
			let fileExtension = getFileExtension(req.query.filename)

			// check if the extension is present in the allowed list of the extensions
			// if (!allowedFileExtensions.has(fileExtension)) {
			// 	res.status(400).send("Invalid file extension!");
			// 	return
			// }

			// sanitize the file name 
			req.query.filename = sanitizeFilename(req.query.filename)

			var filename = uuidv4() + '/' + req.query.filename;//uuid.v4() + path.extname(request.query.filename);
			var cmsVersion = req.session.site.version;

			// configuration values needed for generating upload parameters
			var awsS3Config = {
				"bucket": cms.config[cmsVersion].aws.resource.resourcesBucket,
				"access_key": cms.config[cmsVersion].aws.resource.accessKeyId,
				"secret_key": cms.config[cmsVersion].aws.resource.secretAccessKey,
				"region": cms.config[cmsVersion].aws.resource.region,
				"acl": "private",                                                 // to allow the uploaded file to be publicly accessible
				"x-amz-algorithm": "AWS4-HMAC-SHA256",                              // algorithm used for signing the policy document
				success_action_status: "201"                                        // to return an XML object to the browser detailing the file state
			};

			var result = getS3Credentials(awsS3Config, filename);
			result.apiURL = cms.config[cmsVersion].aws.resource.apiURL;
			res.json(result);
		}else{
			res.status(400).send("A Valid filename Is Needed!");
		}
	}
}

//https://stackoverflow.com/questions/3710204/how-to-check-if-a-string-is-a-valid-json-string-in-javascript-without-using-try
function IsJsonString(str) {
	try {
		JSON.parse(str);
	} catch (e) {
		return false;
	}
	return true;
}

function downloadResource(data, cmsVersion){
	return new Promise(function(resolve, reject) {
		var pth = './_cache/resources/' + data.data.Bucket + '/' + data.data.Key
		var client = s3.createClient({
						s3RetryCount: 3,
						multipartUploadThreshold: 20971520,
						s3Options:{
							"accessKeyId": cms.config[cmsVersion].aws.accessKeyId,
							"secretAccessKey": cms.config[cmsVersion].aws.secretAccessKey,
							"region": cms.config[cmsVersion].aws.region,
						}
					});
		var credentials = {
			"Bucket": cms.config[cmsVersion].aws.defaultBucket + "/" +  data.data.Bucket,
			"Key": data.data.Key,
		}

		params = {
			localFile: path.resolve(pth),
				// default false, whether to remove s3 objects,that have no corresponding local file.
			s3Params: credentials
		};
		operation = client.downloadFile(params)
		operation.on('error', function(err) {
			reject("unable to download", err.stack);
		});
		operation.on('progress', function() {
			//console.log("progress = ", Math.ceil((operation.progressAmount/operation.progressTotal)*100) +"%");
		});
		operation.on('end', function() {
			resolve(pth);
		});
	});
}

function downloadResource_old(data){
	return new Promise(function(resolve, reject) {
		unirest.post(cms.config.resourceManager.getResource)
			.headers({'Accept': 'application/json', 'Content-Type': 'application/json'})
			.send(data)
			.end(function(resData){
				var bucket = data.data.Bucket;
				var key = data.data.Key;
				if(resData.error){
					reject('ERROR : unable write File in dir');
				}else{
					try {
						var buf = new Buffer(resData.body, "base64");
						var pth = './_cache/resources/' + bucket + '/' + key
						mkdirp(path.dirname(pth), function (err) {
							if (err) {
								reject('ERROR : unable write File in dir');
							}
							else{
								fs.writeFile(pth, buf, function(err){
									if(err) {
										reject('ERROR : Unable to download File');
									}else{
										resolve(pth);
									}
								});
							}
						});
					}
					catch(e){
						reject('ERROR : unable write File in dir');
					}
				}
			});
	});
}

function getS3Credentials(config, filename) {
	var params = getS3Parameters(config, filename);

	var result = {
		upload_url: "https://" + config.bucket + ".s3.amazonaws.com",
		params: params
	};

	return result;
}

// Returns the parameters that need to be send with the AWS' upload API
function getS3Parameters(config, filename) {

	var date = new Date().toISOString();

	// create date string for the current date
	var dateString = date.substr(0, 4) + date.substr(5, 2) + date.substr(8, 2);

	// create upload credentials
	var credential = config.access_key + "/" + dateString + "/" + config.region + "/s3/aws4_request";

	// create policy
	var policy = {
		expiration: new Date((new Date).getTime() + (1 * 60 * 1000)).toISOString(),         // to set the time after which upload will no longer be allowed using this policy
		conditions: [
			{ bucket: config.bucket },
			{ key: filename },                                          // filename with which the uploaded file will be saved on s3
			{ acl: config.acl },
			{ success_action_status: config.success_action_status },
//            ["content-length-range", 0, 1000000],                       // optional: to specify the minimum and maximum upload limit
			{ "x-amz-algorithm": config["x-amz-algorithm"] },
			{ "x-amz-credential": credential },
			{ "x-amz-date": dateString + "T000000Z" }
		]
	};

	// base64 encode policy
	var policyBase64 = new Buffer(JSON.stringify(policy)).toString('base64');

	// create signature with policy, aws secret key & other scope information
	var dateKey = createHmacDigest('AWS4' + config.secret_key, dateString);
	var dateRegionKey = createHmacDigest(dateKey, config.region);
	var dateRegionServiceKey = createHmacDigest(dateRegionKey, 's3');
	var signingKey = createHmacDigest(dateRegionServiceKey, 'aws4_request');

	// sign policy document with the signing key to generate upload signature
	var xAmzSignature = createHmacDigest(signingKey, policyBase64).toString('hex');

	// create upload parameters
	return {
		key: filename,
		acl: config.acl,
		success_action_status: config.success_action_status,
		policy: policyBase64,
		'x-amz-algorithm': config["x-amz-algorithm"],
		'x-amz-credential': credential,
		'x-amz-date': dateString + "T000000Z",
		'x-amz-signature': xAmzSignature
	};
}

function createHmacDigest(key, string) {
	var hmac = crypto.createHmac('sha256', key);
	hmac.write(string);
	hmac.end();
	return hmac.read();
}

function getFileExtension(filename) {
	return filename.substring(filename.lastIndexOf('.') + 1);
}

function sanitizeFilename(filename) {
	return sanitize(filename)
}
