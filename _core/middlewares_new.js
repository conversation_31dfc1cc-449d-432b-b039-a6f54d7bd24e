var jwt = require('jsonwebtoken')
var Cookies = require('cookies')
var URL = require('url')
var authenticate = require('../cms/v3.0/api/post/authenticate');
const { decrypt } = require("../cms/v3.0/api/config/helpers");

module.exports = {

	authorizeAPI: function (req, res, next) {
		// if incoming request is for authorizing the user, allow request without authorization
		if ((req.path == '/welcome') || (req.path == '/') || (req.method == 'POST' && req.path == '/api/authenticate')) {
			next();
		}
		/*
		*	/api requests should be accompanied by an apiKey variable.
		*		case 1: if a<PERSON><PERSON><PERSON> has an entry on the global cms.userData object. If present, check if expiry time is ahead of current time
		*					if yes, the request can be authorized to proceed. Add user details from object to req.sesssion variable.
		*		case 2: if a<PERSON><PERSON><PERSON> is not present in userData object or if a<PERSON><PERSON><PERSON> authentication has got past expiry time call api authenticate method.
		*					If present in db, store user details in userData object and req.session and authorize the request
		*		case 3: reject the request as unauthorized
		*/
		else if (typeof (req.session.kuser) != 'object') {
		}

		next();
	},

	isAuthenticated: function (req, res, next) {
		var domainName = req.headers.host;
		var pageName = req.url.toLowerCase().replace(/^\/([^\/\?]+)\/?.*$/gi, '$1');
		if (req.session && req.session.site && req.session.site.protocol && req.session.site.details && req.session.site.details.host) {
			req.siteUrl = req.session.site.protocol + "://" + req.session.site.details.host;
		}

		// If the req is not a page request and if the request is for a resource like /css, /js, etc, there will be a http referer, use that for page name
		if (typeof (req.headers.referer) !== 'undefined' && !cms.config.domains[domainName].page[pageName]) {
			//pageName = req.headers.referer.replace(/https?:\/\/(.*?)(\/.*)$/gi, '$2');
			//pageName = pageName.replace(/^\/([^\/]+)\/?.*$/gi, '$1');
      		pageName = req.headers.referer.replace(/https?:\/\/(.*?)(\/.*?)(\?.*?)?$/gi, '$2');
			pageName = pageName.replace(/^\/([^\/]+)[\/\?]?.*$/gi, '$1');
			//if the referer has redirect_uri and redirectPage.host is in config
			//then change the domain name and page name
			var refURL = URL.parse(req.headers.referer, true);
			if (refURL.query.returnUrl) {
				refURL = URL.parse(refURL.query.returnUrl, true);
				if (refURL.query['redirect_uri']) {
					//decode the redirect_uri and parse as url
					var redirectURL = URL.parse(refURL.query['redirect_uri'], true);
					var redirectPage = redirectURL.pathname;
					redirectPage = redirectPage.replace(/^\/|\/$/gi, '');
					//check domain is in config
					if (cms.config.domains[redirectURL.host]) {
						pageName = redirectPage;
					}
				}
			}
		}


		if (pageName == '/') {
			pageName = cms.config.domains[domainName].page.default ? cms.config.domains[domainName].page.default : 'welcome';
		}

		/*var cookieString = req.headers.cookie;
		var kuserObject = {};
		if (cookieString){
			cookieString = cookieString.replace(/=/g, '": "')
			cookieString = '{"' + cookieString.replace(/;\s+/g, '", "') + '"}';
			kuserObject = JSON.parse(cookieString)
		}*/

		//Get the access tocken from cookie if token is not in cookie then set session kuser is undefined - jagan
		var kuserObject = {};
		var cookies = new Cookies(req, res);
		var accessToken = cookies.get('kAccTkn');

		//if ((typeof(kuserObject) != 'undefined') && (typeof(kuserObject.kAccTkn) != 'undefined')) {
		if (accessToken) {
			try {
				var decoded = jwt.verify(accessToken, cms.config.jwtKey);
			}
			catch (e) {

			}
			if (decoded) {
				req.api_key = decoded.kuser.kapikey || decoded.kuser.kuid;
				kuserObject = decoded;
			}else {
				// delete kuserObject;
				kuserObject = undefined; // set kuserObject to undefined if token is invalid
			}
			//}else if ((typeof(kuserObject) != 'undefined') && (typeof(kuserObject.kAccTkn) == 'undefined')) {
		} else {
			req.session.kuser = undefined;
		}

		// if the req session has kuser object
		if ((typeof (req.session.kuser) === 'object')) {
			next();
		}
		// if there is a cookie named 'kAccTkn' then verify the token to authenticate the request
		else if (			
			(typeof (kuserObject?.kuser) === 'object') &&
			(
				typeof (kuserObject?.kuser?.kuid) === 'string' || 
				typeof (kuserObject?.kuser?.kuid) === 'number'
			)
		) {
			req.session.kuser = kuserObject.kuser;
			next();
		}
		// if the request path starts with /fonts and the http referer / pageName is css then 'fonts' is being called by css file which has already been granted access, so allow /fonts request
		else if ((pageName == 'css') && (/^\/(fonts\/|css\/fontawesome|css\/libs\/font\/)/i.test(req.path))) {
			next();
		} else if (pageName == 'resources') {
			next();
		} else if (req.query.key && validateKey(req.query.key, ['email','customer','project','doi'])) {
			next();
		}
		/*
		* if /api request is made through browser session then authentication will done using user info in the session
		* else
		*	/api requests should be accompanied by an apiKey variable.
		*		case 1: allow if the request to authenticate an user with username and password
		*		case 2: reject the request as unauthorized if no apiKey is provided
		*		case 3: if apiKey has an entry on the global cms.userData object. If present, check if expiry time is ahead of current time
		*					if yes, the request can be authorized to proceed. Add user details from object to req.sesssion variable.
		*		case 4: if apiKey is not present in userData object or if apiKey authentication has got past expiry time call api authenticate method.
		*					If present in db, store user details in userData object and req.session and authorize the request
		*       case 5: If page access level is 0 and api name is addjob or signoff then allow the request
		*/
		else if (/^\/api\//i.test(req.path)) {
			var apiKey = req.headers['x-kd-apikey'];
			if (!apiKey && req.query.apiKey) {
				apiKey = req.query.apiKey;
			} else if (!apiKey && req.body.apiKey) {
				apiKey = req.body.apiKey;
			}
			// case 1
			if (req.method == 'POST' && req.path == '/api/authenticate') {
				next();
			}
			else if(req.method == 'POST' && req.path == '/api/webhooklistener') {
				next()
			}
			else if(req.method == 'POST' && req.path == '/api/publishsocketmessage') {
				next()
			}
			// case 5
			else if(cms.config.domains[domainName].page[pageName] == 0 && (req.path == '/api/addjob' || req.path == '/api/signoff' || /getsubmission|savesubmission|jobstatus|extractmeta/.test(req.path))){
				next();
			}
			//case 6 - allow geteditoretting api if the page have 0 accesslevel
			else if(cms.config.domains[domainName].page[pageName] == 0 && req.path == '/api/geteditorsettings'){
				next();
			}
			else if(req.method == 'POST' && req.path == '/api/readrorDetails') {
				next()
			}
			// case 2
			else if (!apiKey) {
				res.status(401).json({ status: { code: 401, message: "Not authorized. You have not provided credentials to access the requested resource" } }).end();
			}
			// case 3
			else if ((typeof (cms.userData[apiKey]) != 'undefined') && (cms.userData[apiKey].kauthExpDate > new Date())) {
				//authenticating using previous request and the previous authorization has not expired
				if (!req.session.kuser){
					req.session.kuser = cms.userData[apiKey];
				}
				req.api_key = apiKey;
				next();
			}
			// case 4
			else {
				var authenticateParam = {
					'request': req,
					'response': res,
					'apiKey': apiKey
				}
				authenticate.authenticateApi(authenticateParam, req)
					.then((response) => {
						next();
					})
					.catch((e) => {
						res.status(401).json({ status: { code: 401, message: "Not authorized. You are not authorized to access the requested resource" } }).end();
					});
			}
		}
		else if (req.method == 'POST' && (req.path == '/workflow')) {
			next();
		}
		else if (req.method == 'GET' && (req.path == '/api/removearticle')) {
			next();
		}
		// if the page has been marked as public, access-level = 0
		else if (cms.config.domains[domainName].page[pageName] == 0) {
			//page marked as public
			next();
		}
		// if the page has not been configured and authentication is not available then say 'not authorized'
		else if (typeof (cms.config.domains[domainName].page[pageName]) === 'undefined') {
			var error401 = cms.config.error.page401;
			var error401 = error401.replace(/{req.headers.host}/g, req.headers.host);
			var error401 = error401.replace(/{req.url}/g, req.url);
			res.status(401).send(error401);
		}
		else {

			var reqName = req.url.toLowerCase().replace(/^\/([^\/\?]+)\/?.*$/gi, '$1');
			if (reqName == "js" || reqName == "css" || reqName == "fonts" || reqName == "resources" || reqName == "images") {
				res.status(403);
				res.end();
			} else {
				if (req.url) {
					var pageUrl = (typeof (req.headers.referer) !== 'undefined') ? req.headers.referer : req.url;
					var urlObj = URL.parse(pageUrl, true);
					var pathName = urlObj.pathname;
					pathName = pathName.replace(/^\/|\/$/gi, '');
					//Set the last access page to redirect while relogin if sesscion expired
					//Don't set welcome in session and don't set other domains in this cookies
					if (pathName != "welcome" && (!urlObj.host || urlObj.host == domainName)) {
						var proto = req.secure ? 'https' : 'http';
						var cookiesValue = {
							'pageName': pageName,
							'domain': domainName,
							'url': pageUrl,
							'protocol': proto
						}
						//Set the current page details in cookie to redirect when login
						cookiesValue = jwt.sign(cookiesValue, cms.config.jwtKey, { expiresIn: '24h' });
						res.cookie('kLPgTkn', cookiesValue, { expires: new Date(Date.now() + 1800000), httpOnly: true, secure: true, sameSite: "strict" });
					}
				}
				var pageName = cms.config.domains[domainName].page.default ? cms.config.domains[domainName].page.default : 'welcome';
				//res.status(401);
				res.redirect('/' + pageName);
			}
		}
	},

	/*
	* middleware to check if the requested domain is valid and has been configured
	*
	* 	if the domain has been configured then proceed using next() function
	*	else show a 404
	*
	*/
	domainCheck: function (req, res, next) {
		/*
		* query eXist-db to check if the requested site has been defined
		*
		* @return
		*	boolean - true if defined false otherwise
		*/
		function isDomainConfigured(domainName, req) {
			// if domainName is present in config.domains object, domain requested has been configured
			if (typeof (cms.config.domains[domainName]) !== 'undefined') {
				// store information into session
				req.session.site = {
					name: domainName,
					configured: true,
					version: cms.config.domains[domainName].version,
					customer: cms.config.domains[domainName].name,
					defaultpage: cms.config.domains[domainName].page.default,
					details: cms.config.domains[domainName],
					protocol: req.secure ? 'https' : 'http'
				}
				return true;
			}
			// domain requested has not been configured
			else {
				req.session.site = {
					name: domainName,
					configured: false,
					version: 'v3.0'
				}
				return false;
			}
		}
		var reqSession = req.session.site;
		var domainName = req.headers.host;

		// if the requested host name is not in session,
		// check if requested domain has been configured
		if (isDomainConfigured(domainName, req)) {
			next();
		}
		// if the requested host name is present in the session and if the host name is present in the cms.config.domain
		else if ((typeof (reqSession) !== 'undefined') && (reqSession.name == domainName) && (reqSession.configured) && (typeof (cms.config.domains[domainName]) !== 'undefined')) {
			next();
		}
		else {
			res.status(404);
			res.sendFile('404.html', { root: __dirname + '/../cms/' + req.session.site.version + '/pages/error_pages/' });
		}
	},	

	getUserInfo: function(req, res, next){		
		try {
			var pageName = req.url.toLowerCase().replace(/^\/([^\/\?]+)\/?.*$/gi, '$1');
			if (pageName.match(/^(css|js|libs|resources|fonts)$/)) {
				next();
				return true;
			}

			var cookies  = new Cookies(req, res);
			var accessToken = cookies.get('kAccTkn');
			if(accessToken){	
				var decoded = jwt.verify(accessToken, cms.config.jwtKey);
				if (decoded){
					req.headers.kuser = decoded.kuser;
					var userId =""
					if(!decoded.kuser.kuemail && decoded.kuser.korcid){
						userId = decoded.kuser.korcid
					}else{
						userId = decoded.kuser.kuemail
					}
					var param = {
						"index": cms.config.elasticInfo.usersIndex,
						"table": cms.config.elasticInfo.usersTable,
						'query': '_id:(' + userId + ')',
						'from': 0,
						'size': 1,
						data:{}
					}
					var user = "";
					var authURL = cms.config.elasticUrl + cms.config.elastic.getusers;				
					param["data"]["sortOrder"] = [
						"replaceArrayInQueryTemplate",
						{
							"name.first.keyword": {
								"order": "asc"
							}
						}
					]	
					authenticate.postData(param, authURL)
						.then(function(res){
							user = res.body.body.hits.hits[0]._source;
							var userRoles = user.roles;
							if (userRoles && userRoles[0] && userRoles[0]['customer-name'] == 'all') {
								user['roleAccess'] = 'all';
							}

							return authenticate.getCustomers(userRoles, req);
						})
						.then(function(userCustomerRoles){
							//TODO Temporary change: filtering out roles with access-level "author" or "reviewer in production"
							if (process.env.NODE_ENV == 'production' && !checkAllowedCustomers(userCustomerRoles)) {
								userCustomerRoles = userCustomerRoles.filter(
									role => !/^(author|reviewer)$/.test(role['access-level'])
								);
							}
							user.roles = JSON.parse(JSON.stringify(userCustomerRoles));
							var rolesObj = {};
							if (user.roles.length > 0) { // for elastic
								for (var r = 0; r < user.roles.length; r++) {
									var customerObj = user.roles[r];
									rolesObj[customerObj['customer-name']] = customerObj;
									//delete the customer name in role object
									delete rolesObj[customerObj['customer-name']]['customer-name'];
								}
							} else if (user.roles && user.roles['customer-name']) {
								rolesObj[user.roles['customer-name']] = user.roles;
								//delete the customer name in role object
								delete rolesObj[user.roles['customer-name']]['customer-name'];
							}
							let currentRole = cookies.get('currentRole');
							if(currentRole){
								currentRole = decodeURIComponent(currentRole);
								let decryptedUser = JSON.parse(decrypt(currentRole))
								rolesObj[decryptedUser['customer-name']] = decryptedUser;
								delete rolesObj[decryptedUser['customer-name']]['customer-name'];
							}
							req.headers.kuser = {
								kuid: user.apikey,
								kuname: user.name,
								kuemail: user.email,
								kapikey: user.apikey,
								kuroles: rolesObj,
								kuroleaccess: userCustomerRoles
							};
							if(user.orcid){
								req.headers.kuser['korcid'] = user.orcid
							}
							if(user.roleAccess){
								req.headers.kuser['kurolesAccess'] = user.roleAccess;
							}
							next();
						})
						.catch(function(err){
							next();
							//Error - Getting  user info failed
						});
				} else {
					next();
					//Error - Invalud cookie
				}
			}else{
				next();
				//Error - Cookie was expired, So not found
			}
		}
		catch(e){
			next();
			//Error - Coing break error
		}		
	}
}

/**
 * 
 * @param {String} encencryptedkey Key to decrypt and check
 * @returns 
 */
function validateKey(encencryptedkey,mandatoryKeys){
	try{
		let decryptedKey = decrypt(encencryptedkey).split("&");
		let presentKeys = 0;
		for(let mKey of decryptedKey){
			let currentKey = mKey.split("=");
			if(mandatoryKeys.includes(currentKey[0]) && currentKey[0] && currentKey[1] != '') {
				presentKeys++;
			}
		}
		if(presentKeys == mandatoryKeys.length){
			return true;
		}
		return false;
	}
	catch(e){
		return false;
	}
}

function checkAllowedCustomers (customer) {
  const allowedCustomers = customer.some(item => item["customer-name"] === process.env.SHOW_RESEARCHER_DASHBOARD)
  return allowedCustomers;
};