/**
 * Shared Test Utilities
 * 
 * This file contains shared utilities and helper functions used across
 * all test files, extracted from api.test.js to avoid code duplication.
 */

const request = require('supertest');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Parse command line arguments
 * @param {Array} argv - Process arguments
 * @returns {Object} Parsed arguments object
 */
function processRuntimeArguments(argv) {
    const args = {};
    
    for (let i = 0; i < argv.length; i++) {
        const arg = argv[i];
        if (arg.startsWith('--')) {
            const [key, value] = arg.substring(2).split('=');
            if (value !== undefined) {
                // Handle boolean values
                if (value === 'true') args[key] = true;
                else if (value === 'false') args[key] = false;
                else args[key] = value;
            } else {
                args[key] = true;
            }
        }
    }
    
    return args;
}

/**
 * Reads all JSON configuration files from the API config directory
 * @returns {Array} Array of configuration filenames
 */
function readConfigFiles() {
    if (fs.existsSync(process.env.SUPERTEST_API_CONFIG_PATH)) {
        return fs.readdirSync(process.env.SUPERTEST_API_CONFIG_PATH, { recursive: true })
            .filter(file => file.endsWith('.json'));
    } else {
        return [];
    }
}

/**
 * Gets test files filtered by category
 * @param {string} category - Single category to filter by
 * @param {string} categories - Comma-separated categories to filter by
 * @returns {Array} Array of configuration filenames that match the categories
 */
function getFilesByCategory(category, categories) {
    const targetCategories = [];
    
    if (category) {
        targetCategories.push(category);
    }
    
    if (categories) {
        targetCategories.push(...categories.split(',').map(cat => cat.trim()));
    }
    
    if (targetCategories.length === 0) {
        return [];
    }
    
    const allFiles = readConfigFiles();
    const matchingFiles = [];
    
    for (const configFile of allFiles) {
        try {
            const filePath = path.join(process.env.SUPERTEST_API_CONFIG_PATH, configFile);
            const configJSONString = fs.readFileSync(filePath, 'utf8');
            const configJSON = JSON.parse(configJSONString);
            
            // Check if any test in this file matches the target categories
            const hasMatchingCategory = configJSON.some(test => 
                test.category && targetCategories.includes(test.category)
            );
            
            if (hasMatchingCategory) {
                matchingFiles.push(configFile);
            }
        } catch (error) {
            console.warn(`Warning: Could not parse ${configFile} for category filtering:`, error.message);
            // Continue with other files
        }
    }
    
    console.log(`Found ${matchingFiles.length} files matching categories: ${targetCategories.join(', ')}`);
    return matchingFiles;
}

/**
 * Replace placeholders in test configuration
 * @param {Object} iterator - Test configuration object
 * @returns {Object} Updated configuration with placeholders replaced
 */
function replacePlaceholders(iterator) {
    // Create a deep copy to avoid modifying the original
    const updated = JSON.parse(JSON.stringify(iterator));
    
    // Replace {APIKEY} placeholder in URL
    if (updated.url && updated.url.includes('{APIKEY}')) {
        updated.url = updated.url.replace('{APIKEY}', process.env.APIKEY);
    }
    
    // Replace other common placeholders as needed
    // Add more placeholder replacements here if needed
    
    return updated;
}

/**
 * Make HTTP request using either supertest (for app) or axios (for HTTP)
 * @param {Object} iterator - Test configuration
 * @param {Object|string} target - Either Express app instance or base URL
 * @returns {Promise<Object>} Response object
 */
async function makeRequest(iterator, target) {
    const method = iterator.method?.toLowerCase() || 'get';
    const url = iterator.url;
    const data = iterator.data || {};
    
    if (typeof target === 'string') {
        // HTTP request using axios
        const config = {
            method,
            url: `${target}${url}`,
            headers: {
                'x-kd-apikey': process.env.APIKEY,
                'Content-Type': 'application/json'
            }
        };
        
        if (method !== 'get' && method !== 'head') {
            config.data = data;
        } else if (Object.keys(data).length > 0) {
            config.params = data;
        }
        
        try {
            const response = await axios(config);
            return {
                status: response.status,
                text: typeof response.data === 'string' ? response.data : JSON.stringify(response.data),
                body: response.data
            };
        } catch (error) {
            if (error.response) {
                return {
                    status: error.response.status,
                    text: typeof error.response.data === 'string' ? error.response.data : JSON.stringify(error.response.data),
                    body: error.response.data
                };
            }
            throw error;
        }
    } else {
        // Supertest request using app instance
        let req = request(target)[method](url);
        
        // Add headers
        req = req.set('x-kd-apikey', process.env.APIKEY);
        
        // Add data based on method
        if (method !== 'get' && method !== 'head') {
            req = req.send(data);
        } else if (Object.keys(data).length > 0) {
            req = req.query(data);
        }
        
        const response = await req;
        return {
            status: response.status,
            text: response.text,
            body: response.body
        };
    }
}

/**
 * Initialize app and server for testing
 * @param {Object} args - Parsed command line arguments
 * @returns {Promise<Object>} Object containing app and server instances
 */
async function initializeApp(args) {
    let app = null;
    let server = null;
    
    if (args.useApp) {
        try {
            // Import the app from server.js
            const config = { ENV: ".env", PORT: process.env.PORT };
            app = require('../server.js');
            console.log('Using Express app instance');
        } catch (error) {
            console.error('Error importing app:', error);
            throw error;
        }
    }
    
    return { app, server };
}

module.exports = {
    processRuntimeArguments,
    readConfigFiles,
    getFilesByCategory,
    replacePlaceholders,
    makeRequest,
    initializeApp
};
