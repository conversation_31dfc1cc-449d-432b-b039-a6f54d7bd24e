/**
 * XML Upload Utilities
 * 
 * This file contains XML upload functionality extracted from api.test.js
 * to avoid code duplication in endpoint-specific test files.
 */

const request = require('supertest');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Uploads XML files to the test environment
 * @param {Object} args - Parsed command line arguments
 * @param {Object} server - Server instance (if using app mode)
 * @returns {Promise<string>} Result message
 */
async function uploadXml(args = {}, server = null) {
    let doiArray = [];
    
    if (!fs.existsSync(process.env.SUPERTEST_XML_INPUT_PATH)) {
        console.log("Input directory not found");
        return 'No files to upload';
    }
    
    const files = fs.readdirSync(process.env.SUPERTEST_XML_INPUT_PATH);
    const xmlFiles = files.filter(file => file.endsWith('.xml'));
    
    if (xmlFiles.length === 0) {
        console.log("No XML files found in input directory");
        return 'No XML files to upload';
    }
    
    console.log(`Found ${xmlFiles.length} XML files to upload`);
    
    for (const file of xmlFiles) {
        const filePath = path.join(process.env.SUPERTEST_XML_INPUT_PATH, file);
        
        // Extract metadata from filename or use defaults
        const fileNameParts = file.replace('.xml', '').split('_');
        const iterator = {
            path: filePath,
            customer: fileNameParts[0] || 'test-customer',
            project: fileNameParts[1] || 'test-project',
            doi: fileNameParts[2] || `test-doi-${Date.now()}`
        };
        
        doiArray.push(iterator.doi);
        
        try {
            const form = new FormData();
            form.append('xmlFile', fs.createReadStream(iterator.path));
            form.append('customer', iterator.customer);
            form.append('project', iterator.project);
            form.append('comment', iterator.doi);
            
            let response;
            
            // Use different upload methods based on whether we're using the app or HTTP
            if (args.useApp && server) { 
                // Use supertest with app instance
                response = await new Promise((resolve, reject) => {
                    request(server)
                        .post(`/api/uploadxml`)
                        .attach('xmlFile', iterator.path)
                        .field('customer', iterator.customer)
                        .field('project', iterator.project)
                        .field('comment', iterator.doi)
                        .set(form.getHeaders())
                        .end((err, res) => {
                            if (err) {
                                reject(err);
                            } else {
                                resolve(res);
                            }
                        });
                });
                
                if (response.status !== 200) {
                    console.error(`Upload failed for ${file}: ${response.status}`);
                    console.error(`Error response: ${response.text}`);
                }
            } else {
                // Use axios for HTTP requests              
                response = await axios.post(
                    `${process.env.INSTANCE_URL}/api/uploadxml`, 
                    form, 
                    { 
                        headers: {
                            ...form.getHeaders(), 
                            'x-kd-apikey': process.env.APIKEY
                        }
                    }
                );
                
                console.log(`Upload response status: ${response.status}`);
                if (response.status !== 200) {
                    console.error(`Upload failed for ${file}: ${response.status}`);
                    console.error(`Error response: ${response.data}`);
                }
            }
            
            console.log(`✅ Successfully uploaded: ${file}`);
            
        } catch (error) {
            console.error(`❌ Error uploading ${file}:`, error.message);
            // Continue with other files instead of failing completely
        }
    }
    
    console.log(`XML upload completed. Uploaded ${xmlFiles.length} files.`);
    return `Uploaded ${xmlFiles.length} XML files: ${doiArray.join(', ')}`;
}

/**
 * Check if XML upload should be performed
 * @param {Object} args - Parsed command line arguments
 * @param {boolean} isFirstEndpoint - Whether this is the first endpoint being tested
 * @param {boolean} isMultipleEndpoints - Whether multiple endpoints are being tested
 * @returns {boolean} Whether to upload XML files
 */
function shouldUploadXml(args, isFirstEndpoint = true, isMultipleEndpoints = false) {
    // Skip upload if explicitly disabled
    if (args.skipUpload) {
        return false;
    }
    
    // For single endpoint tests, always upload
    if (!isMultipleEndpoints) {
        return true;
    }
    
    // For multiple endpoint tests, only upload for the first endpoint
    return isFirstEndpoint;
}

/**
 * Setup XML upload for test execution
 * @param {Object} args - Parsed command line arguments
 * @param {Object} server - Server instance (if using app mode)
 * @param {boolean} isFirstEndpoint - Whether this is the first endpoint being tested
 * @param {boolean} isMultipleEndpoints - Whether multiple endpoints are being tested
 * @returns {Promise<string>} Upload result message
 */
async function setupXmlUpload(args, server = null, isFirstEndpoint = true, isMultipleEndpoints = false) {
    if (shouldUploadXml(args, isFirstEndpoint, isMultipleEndpoints)) {
        console.log('🔄 Starting XML upload...');
        const result = await uploadXml(args, server);
        console.log('✅ XML upload completed');
        return result;
    } else {
        console.log('⏭️  Skipping XML upload');
        return 'XML upload skipped';
    }
}

module.exports = {
    uploadXml,
    shouldUploadXml,
    setupXmlUpload
};
