<?xml version="1.0" encoding="UTF-8"?><custom-config><header><body><div class="kriyaMenuBlock row"><div class="row col s12 kriyaMenuContainer"><span class="kriyaMenuLogo" data-name="customerLogo"><img class="kriyaLogo" src="../images/kriyadocsTrademark.png" style="width: 100%; height: 40px!important; padding-top: 5px;"/></span><span data-name="SaveNotice" class="save-notice"/><span class="time-notice"/><span class="userProfile pull-right hidden"><span class="username" data-user-id="cde4c89b-e452-4ba5-b493-01c691033b72" data-user-email="<EMAIL>">xml</span></span><ul data-name="ExportPDF-AHPrintPDF-Options" id="export-pdf-print" class="dropdown-content export" data-project=" kriya "><li data-page="review_content" data-channel="menu" data-topic="export" data-processtype="ahformatter" data-event="click" data-message="{'funcToCall': 'exportToPDF'}">Export PDF</li><li data-page="review_content" data-processtype="ahformatter" data-message="{'click':{'funcToCall': 'exportToPDF', 'param':{'type': 'print'},'channel':'menu','topic':'export'}}">Export Print PDF</li></ul></div><div class="row col s12 kriyaSubMenuContainer navDivContent" data-name="commonOptions"><span class="btnGroupContainer hidden"><span class="btn-group cutCopyPaste"><span class="btn-sub-group"><span data-name="MenuBtnUndo" class="mnuButtons disabled" data-tooltip="Undo" data-shortcut="ctrl+z" data-context="body"><i class="icon-rotateleft">​</i></span><span data-name="MenuBtnRedo" class="mnuButtons disabled" data-tooltip="Redo" data-shortcut="ctrl+y" data-mac-shortcut="ctrl+shift+z" data-context="body"><i class="icon-rotateright">​</i></span></span><span class="btn-sub-group kriyaMenuControl"><span class="kriyaMenuBtn mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'showHideMenu'}"><i class="icon-content_cut">​</i><i class="icon-arrow_drop_down"/></span><span class="kriyaMenuItems"><span data-name="MenuBtnCut" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'cut'},'channel':'menu','topic':'edit'}}" data-tooltip="Cut"><i class="icon-content_cut">​</i></span><span data-name="MenuBtnCopy" class="mnuButtons" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'copy'},'channel':'menu','topic':'edit'}}" data-tooltip="Copy"><i class="icon-copy">​</i></span><span data-name="MenuBtnPasteText" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'paste'},'channel':'menu','topic':'edit'}}" data-tooltip="Paste as text"><i class="icon-pastetext">​</i></span><span data-name="MenuBtnPasteHTML" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'paste', 'argument':'html'},'channel':'menu','topic':'edit'}}" data-tooltip="Paste as HTML"><i class="icon-paste2">​</i></span></span></span></span></span><span class="btnGroupContainer hidden"><span class="btn-group formatGroup"><span class="btn-sub-group kriyaMenuControl"><span class="kriyaMenuBtn mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'showHideMenu'}"><i class="icon-bold">​</i><i class="icon-arrow_drop_down"/></span><span class="kriyaMenuItems"><span data-name="MenuBtnBold" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'bold'},'channel':'menu','topic':'edit'}}" data-tooltip="Bold" data-menu-style="bold"><i class="icon-bold">​</i></span><span data-name="MenuBtnItalic" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'italic'},'channel':'menu','topic':'edit'}}" data-tooltip="Italic" data-menu-style="italic"><i class="icon-italic">​</i></span><span data-name="MenuBtnUnderline" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'underline'},'channel':'menu','topic':'edit'}}" data-tooltip="Underline" data-menu-style="underline"><i class="icon-underline">​</i></span><span data-name="MenuBtnSup" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'superscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Superscript" data-menu-style="superscript"><i class="icon-superscript">​</i></span><span data-name="MenuBtnSub" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'subscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Subscript" data-menu-style="subscript"><i class="icon-subscript">​</i></span></span>                        </span><span class="btn-sub-group kriyaMenuControl"><span class="kriyaMenuBtn mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'showHideMenu'}"><i class="icon-format_color_text">​</i><i class="icon-arrow_drop_down"/></span><span class="kriyaMenuItems"><span data-name="MenuBtnTextColor" data-context-btn="true" class="mnuButtons dropdown-button" data-activates="para-text-color-picker" data-tooltip="Text Color"><i class="icon-format_color_text">​</i></span><span data-name="MenuBtnBckColor" data-context-btn="true" class="mnuButtons dropdown-button" data-activates="para-bk-color-picker" data-tooltip="Background Color"><i class="icon-paint-brush">​</i></span><span data-name="MenuBtnCase" data-context-btn="true" class="mnuButtons dropdown-button" data-activates="change-content-case" data-tooltip="Change Case"><i class="icon-case">Aa</i></span><ul data-name="MenuBtnCaseDropDown" id="change-content-case" class="dropdown-content dropdown-list"><li data-channel="menu" data-topic="style" data-event="click" data-message="{'funcToCall': 'changeCase', 'param':{'toCaseOption' : 'upperCase'}}">UPPERCASE</li><li data-channel="menu" data-topic="style" data-event="click" data-message="{'funcToCall': 'changeCase', 'param':{'toCaseOption' : 'lowerCase'}}">lowercase</li><li data-channel="menu" data-topic="style" data-event="click" data-message="{'funcToCall': 'smallCaps', 'param':{'name': 'span', 'type': 'inline', 'class': 'jrnlSmallCaps sty cts-2'}}" style="font-variant: small-caps;">Small Caps</li><li data-channel="menu" data-topic="style" data-event="click" data-message="{'funcToCall': 'changeCase', 'param':{'toCaseOption' : 'sentenceCase'}}">Sentence case</li><li data-channel="menu" data-topic="style" data-event="click" data-message="{'funcToCall': 'changeCase', 'param':{'toCaseOption' : 'titleCase'}}">Title Case</li></ul></span></span></span></span><span class="btnGroupContainer hidden"><span class="btn-group"><span class="btn-sub-group alignmentGroup kriyaMenuControl"><span class="kriyaMenuBtn mnuButtons hidden" data-class-selector=" jrnlHead1 " data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'showHideMenu'}"><i class="icon-paragraph-left">​</i><i class="icon-arrow_drop_down"/></span><span class="kriyaMenuItems"><span data-name="MenuBtnAlignLeft" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'content', 'param':{'command': 'left'}}" data-tooltip="Align Left"><i class="icon-paragraph-left">​</i></span><span data-name="MenuBtnAlignCenter" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'content', 'param':{'command': 'center'}}" data-tooltip="Align Center"><i class="icon-paragraph-center">​</i></span><span data-name="MenuBtnAlignRight" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'content', 'param':{'command': 'right'}}" data-tooltip="Align Right"><i class="icon-paragraph-right">​</i></span><span data-name="MenuBtnJustify" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'content', 'param':{'command': 'justify'}}" data-tooltip="Justify"><i class="icon-paragraph-justify">​</i></span></span></span><span data-name="MenuBtnUL" data-context-btn="true" data-menu-style="insertUnorderedList" class="dropdown-button mnuButtons" data-tooltip="Bullet List" data-activates="unorder-list-dropdown"><i class="icon-list">​</i><i class="icon-arrow_drop_down"/></span><ul data-name="MenuBtnULDropDown" id="unorder-list-dropdown" class="dropdown-content dropdown-list"><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'disc'},'channel':'menu','topic':'edit'}}">Default</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'circle'},'channel':'menu','topic':'edit'}}">Circle</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'disc'},'channel':'menu','topic':'edit'}}">Disc</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'square'},'channel':'menu','topic':'edit'}}">Square</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'check-box'},'channel':'menu','topic':'edit'}}">Check Box</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'endash'},'channel':'menu','topic':'edit'}}">En dash</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'emdash'},'channel':'menu','topic':'edit'}}">Em dash</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'greater-than'},'channel':'menu','topic':'edit'}}">Greater Than</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'none'},'channel':'menu','topic':'edit'}}">None</li></ul><span data-name="MenuBtnNL" data-context-btn="true" data-menu-style="insertOrderedList" class="dropdown-button mnuButtons" data-tooltip="Numbered List" data-activates="order-list-dropdown"><i class="icon-list-numbered">​</i><i class="icon-arrow_drop_down"/></span><ul data-name="MenuBtnNLDropDown" id="order-list-dropdown" class="dropdown-content dropdown-list"><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'decimal'},'channel':'menu','topic':'edit'}}">Default</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'lower-alpha'},'channel':'menu','topic':'edit'}}">Lower Alpha</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'lower-greek'},'channel':'menu','topic':'edit'}}">Lower Greek</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'lower-roman'},'channel':'menu','topic':'edit'}}">Lower Roman</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'upper-alpha'},'channel':'menu','topic':'edit'}}">Upper Alpha</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertOrderedList','argument':'upper-roman'},'channel':'menu','topic':'edit'}}">Upper Roman</li><li data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'insertUnorderedList','argument':'none'},'channel':'menu','topic':'edit'}}">None</li></ul><span data-name="MenuBtnIncIndent" data-context-btn="true" class="mnuButtons disabled" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'indent'},'channel':'menu','topic':'edit'}}" data-tooltip="Increase Indent"><i class="icon-indent-increase">​</i></span><span data-name="MenuBtnDecIndent" data-context-btn="true" class="mnuButtons disabled" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'outdent'},'channel':'menu','topic':'edit'}}" data-tooltip="Decrease Indent"><i class="icon-indent-decrease">​</i></span></span></span><span class="btnGroupContainer hidden"><span class="btn-group"><span data-name="MenuBtnSpellCheck" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="spellCheck" data-event="click" data-message="{'funcToCall': 'checkDocument'}" data-tooltip="Spell Check"><i class="icon-spell-check">​</i></span><span data-name="MenuBtnWordCount" data-context-btn="true" class="mnuButtons hidden" data-channel="menu" data-topic="insert" data-event="click" data-message="{'funcToCall': 'wordCount', 'param': {'method': 'ADD', 'type': 'COMMENT', 'value': ''}}" data-tooltip="Word Count"><i class="icon-count">​</i></span><span data-name="MenuBtnSplChar" data-context-btn="true" class="mnuButtons" data-channel="menu" data-topic="insert" data-event="click" data-message="{'funcToCall': 'newSpecialChar'}" data-tooltip="Insert Special Character"><i class="icon-omega">​</i></span><span data-name="MenuBtnTrackChanges" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'showHideTrackChanges','channel':'menu','topic':'track_changes'}}" data-tooltip="Hide/Show Track Changes" data-track-filter-publisher="author"><i class="icon-eye">​</i></span></span>   </span><span class="btnGroupContainer hidden" data-class-selector=" jrnlTblBody "><span class="btn-group"><span data-context-btn="true" class="mnuButtons dropdown-button" data-activates="bk-color-picker" data-tooltip="Table Background Color"><i class="material-icons">format_color_fill</i></span><span data-name="MenuBtnTextColor" data-context-btn="true" class="mnuButtons dropdown-button" data-activates="text-color-picker" data-tooltip="Table Text Color"><i class="icon-format_color_text">​</i></span></span></span><span data-name="MenuBtnSearch" class="nav-action-icon search-icon" data-page="review_content" data-channel="menu" data-topic="findReplace" data-event="click" data-message="{'funcToCall': 'resetPanel', 'param': 'findSection'}" data-context="body" data-tooltip="Search"><i class="material-icons">search</i></span><span data-name="MenuBtnHelp" class="nav-action-icon help-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'helpDivNode'}" data-tooltip="Help"><i class="material-icons">help</i></span><span data-name="MenuBtnSearch" class="nav-action-icon index-icon" id="indexIcon" data-page="review_content" data-channel="menu" data-topic="insert" data-event="click" data-message="{'funcToCall': 'indexTerm'}" data-context="body" data-tooltip="Index"><i class="material-icons">stars</i></span><span data-name="MenuBtnFolder" class="nav-action-icon data-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'dataDivNode'}" data-tooltip="Additional files"><i class="material-icons">folder</i></span><span data-name="MenuBtnTables" class="nav-action-icon table-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'tableDivNode'}" data-tooltip="Tables"><i class="icon-table"/></span><span data-name="MenuBtnFigures" class="nav-action-icon fig-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'figureDivNode'}" data-tooltip="Figures"><i class="icon-image"/></span><span data-name="MenuBtnRef" class="nav-action-icon ref-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'refDivNode'}" data-tooltip="References"><i class="icon-book2"/></span><span data-name="MenuBtnChanges" class="nav-action-icon change-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'changesDivNode'}" data-tooltip="Changes"><i class="icon-file-text2"><i class="icon-pencil" style="position: absolute;font-size: 60% !important;left: 55%;top: 30%;"/></i></span><span data-name="MenuBtnNotifications" class="nav-action-icon notes-icon active" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'queryDivNode'}" data-tooltip="Queries"><i class="material-icons">notifications</i></span><span data-name="MenuBtnTOC" class="nav-action-icon info-icon" data-page="review_content" data-channel="menu" data-topic="edit" data-event="click" data-message="{'funcToCall': 'toggleNavPane', 'param': 'infoDivNode'}" data-tooltip="Table of contents"><i class="material-icons">list</i></span></div></div></body></header><components><div class="templates" autocomplete="off" autocorrect="off" autocapitalize="off" spellcheck="false"><div data-type="popUp" data-component="jrnlArtType_edit" data-name="jrnlArtType_edit" class="hidden z-depth-2" data-pop-size="pop-sm" data-content-variable="kriya.config.content.article_type" data-role="publisher,typesetter,preeditor"><div data-input-editable="true" style="top: 7%; margin-bottom: 6%; width:70%;"><span class="popup-menu"><a class="btn-floating btn-small com-close" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}" data-tooltip="Close"><i class="close material-icons">close</i></a></span><div class="row"><div class="col s6"><div class="popupHead">Choose article type</div></div><div class="col s2 componentMenuItems" style="user-select: none;pointer-events: none;color: #a99b9b;"><span data-name="MenuBtnBold" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'bold'},'channel':'menu','topic':'edit'}}" data-tooltip="Bold" data-menu-style="bold" data-tooltip-id="c122f90d-0a10-576a-fd78-511c56264766"><i class="icon-bold">​</i></span><span data-name="MenuBtnItalic" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'italic'},'channel':'menu','topic':'edit'}}" data-tooltip="Italic" data-menu-style="italic" data-tooltip-id="045de8e0-5efd-924a-0867-c784b06796b4"><i class="icon-italic">​</i></span><span data-name="MenuBtnUnderline" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'underline'},'channel':'menu','topic':'edit'}}" data-tooltip="Underline" data-menu-style="underline" data-tooltip-id="7745f440-7631-7981-8527-950c3bf62b5f"><i class="icon-underline">​</i></span><span data-name="MenuBtnSup" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'superscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Superscript" data-menu-style="superscript" data-tooltip-id="7def3905-4b36-15c2-3a89-8b1a5a46aa16"><i class="icon-superscript">​</i></span><span data-name="MenuBtnSub" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'subscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Subscript" data-menu-style="subscript" data-tooltip-id="67217f2c-e38c-6eef-4a57-b2962e759a94"><i class="icon-subscript">​</i></span></div><div class="col s4"><div class="historyHead">Change History<div class="historyViewController row"><div class="defaultview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Default</div><div class="expandview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Expand</div></div></div></div></div><div class="row"><div class="col s8" data-wrapper="true"><div class="row"><div class="input-field col s12"><div data-input="true" type="text" data-class="jrnlArtType" data-selector="//*[@class='jrnlPubDetails']/*[contains(@class,'jrnlArtType ')]|//div[@class='front']/*[contains(@class,'jrnlArtType ')]" data-type="htmlComponent" data-save-type="saveDropdown" class="text-line" data-validate="required" data-message="{'click':{'funcToCall': 'showDropDownList','channel':'components','topic':'PopUp'}}" data-formating="true"/><div data-name="jrnlArtType_options" data-event="click" data-channel="components" data-topic="PopUp" data-message="{'funcToCall': 'changeSelectedValue'}"><ul class="dropdown-content dropdown-list"><li>Case Report</li><li>Clinical Trial</li><li>Commentary</li><li>Editorial</li><li>Meta-Analysis</li><li>Research Paper</li><li>Perspective</li><li>Protocol</li><li>Review Article</li><li>Technical Report</li><li>Visual Abstract</li></ul></div></div></div></div><div class="col s4 historyTab"/></div></div></div><div data-type="popUp" data-component="jrnlSubject_edit" data-name="jrnlSubject_edit" class="hidden z-depth-2" data-pop-size="pop-sm" data-content-variable="kriya.config.content.article_type" data-role="publisher,typesetter,preeditor"><div data-input-editable="true" style="top: 7%; margin-bottom: 6%; width:70%;"><span class="popup-menu"><a class="btn-floating btn-small com-close" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}" data-tooltip="Close"><i class="close material-icons">close</i></a></span><div class="row"><div class="col s6"><div class="popupHead">Choose category</div></div><div class="col s2 componentMenuItems" style="user-select: none;pointer-events: none;color: #a99b9b;"><span data-name="MenuBtnBold" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'bold'},'channel':'menu','topic':'edit'}}" data-tooltip="Bold" data-menu-style="bold" data-tooltip-id="c122f90d-0a10-576a-fd78-511c56264766"><i class="icon-bold">​</i></span><span data-name="MenuBtnItalic" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'italic'},'channel':'menu','topic':'edit'}}" data-tooltip="Italic" data-menu-style="italic" data-tooltip-id="045de8e0-5efd-924a-0867-c784b06796b4"><i class="icon-italic">​</i></span><span data-name="MenuBtnUnderline" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'underline'},'channel':'menu','topic':'edit'}}" data-tooltip="Underline" data-menu-style="underline" data-tooltip-id="7745f440-7631-7981-8527-950c3bf62b5f"><i class="icon-underline">​</i></span><span data-name="MenuBtnSup" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'superscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Superscript" data-menu-style="superscript" data-tooltip-id="7def3905-4b36-15c2-3a89-8b1a5a46aa16"><i class="icon-superscript">​</i></span><span data-name="MenuBtnSub" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'subscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Subscript" data-menu-style="subscript" data-tooltip-id="67217f2c-e38c-6eef-4a57-b2962e759a94"><i class="icon-subscript">​</i></span></div><div class="col s4"><div class="historyHead">Change History<div class="historyViewController row"><div class="defaultview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Default</div><div class="expandview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Expand</div></div></div></div></div><div class="row"><div class="col s8" data-wrapper="true"><div class="row"><div class="input-field col s12"><div data-input="true" type="text" data-class="jrnlSubject" data-selector="//*[@class='jrnlPubDetails']/*[contains(@class,'jrnlSubject ')]|//div[@class='front']/*[contains(@class,'jrnlSubject ')]" data-type="htmlComponent" data-save-type="saveDropdown" class="text-line" data-validate="required" data-message="{'click':{'funcToCall': 'showDropDownList','channel':'components','topic':'PopUp'}}" data-formating="true"/><div data-name="jrnlSubject_options" data-event="click" data-channel="components" data-topic="PopUp" data-message="{'funcToCall': 'changeSelectedValue'}"><ul class="dropdown-content dropdown-list"><li data-value="+ Add Section head" data-remove-data="true">None</li><li>Workplace</li><li>Environment</li><li>Exposure Assessment</li><li>Methodology</li><li>Editorial</li><li>Commentary</li><li>Review</li><li>World at work</li><li>PostScript</li></ul></div></div></div></div><div class="col s4 historyTab"/></div></div></div><div data-type="popUp" data-component="jrnlKeyword_edit" data-name="jrnlKeyword_edit" class="hoverInline hidden z-depth-2 bottom" style="top: 7%; margin-bottom: 6%; width:70%;" data-class="jrnlKeyword"><div data-input-editable="true" style="top: 7%; margin-bottom: 6%; width:70%;"><span class="popup-menu"><a class="btn-floating btn-small com-close" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}" data-tooltip="Close"><i class="close material-icons">close</i></a></span><div class="row"><div class="col s6"><div class="popupHead" data-pop-type="new"><span>Insert </span><span class="jrnlKwdLabel" data-selector="ancestor::*[@class='jrnlKwdGroup']//*[@class='jrnlKeywordHead']" data-type="htmlComponent" data-save-type="false"/></div><div class="popupHead" data-pop-type="edit"><span>Edit </span><span class="jrnlKwdLabel" data-selector="ancestor::*[@class='jrnlKwdGroup']//*[@class='jrnlKeywordHead']" data-type="htmlComponent" data-save-type="false"/></div></div><div class="col s2 componentMenuItems" style="user-select: none;pointer-events: none;color: #a99b9b;"><span data-name="MenuBtnBold" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'bold'},'channel':'menu','topic':'edit'}}" data-tooltip="Bold" data-menu-style="bold" data-tooltip-id="c122f90d-0a10-576a-fd78-511c56264766"><i class="icon-bold">​</i></span><span data-name="MenuBtnItalic" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'italic'},'channel':'menu','topic':'edit'}}" data-tooltip="Italic" data-menu-style="italic" data-tooltip-id="045de8e0-5efd-924a-0867-c784b06796b4"><i class="icon-italic">​</i></span><span data-name="MenuBtnUnderline" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'underline'},'channel':'menu','topic':'edit'}}" data-tooltip="Underline" data-menu-style="underline" data-tooltip-id="7745f440-7631-7981-8527-950c3bf62b5f"><i class="icon-underline">​</i></span><span data-name="MenuBtnSup" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'superscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Superscript" data-menu-style="superscript" data-tooltip-id="7def3905-4b36-15c2-3a89-8b1a5a46aa16"><i class="icon-superscript">​</i></span><span data-name="MenuBtnSub" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'subscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Subscript" data-menu-style="subscript" data-tooltip-id="67217f2c-e38c-6eef-4a57-b2962e759a94"><i class="icon-subscript">​</i></span></div><div class="col s4 historyHeadDiv"><div class="historyHead">Change History<div class="historyViewController row"><div class="defaultview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Default</div><div class="expandview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Expand</div></div></div></div></div><div class="row"><div class="col s8" data-wrapper="true"><div class="row"><div class="input-field col s12"><div data-input="true" data-formating="true" class="kriya-chips text-line" type="text" data-class="jrnlKeyword" data-selector="." data-type="htmlComponent" data-event="paste" data-message="{'funcToCall': 'cleanupOnPaste'}"/><div data-name="jrnlKeyword_options"><ul class="dropdown-content dropdown-list"><li>Abdomen</li><li>Accelerometer</li><li>Accident</li><li>Acclimatised</li><li>Achilles</li><li>ACL</li><li>Adaptations of skeletal muscle to exercise and altered neuromuscular activity</li><li>Adolescent</li><li>Aerobic fitness</li><li>Aging</li><li>Altitude</li><li>American football</li><li>Anabolic steroids</li><li>Anaemia</li><li>Ankle</li><li>Anorexia</li><li>Anterior cruciate ligament</li><li>Antioxidants</li><li>Anxiety</li><li>App</li><li>Archery</li><li>Arm</li><li>Artery</li><li>Arthroscopy</li><li>Assessing physical training modalities in enhancing sports performance</li><li>Assessing physiological demands of physical activity</li><li>Assessing validity and reliability of test of physiological parameters</li><li>Asthma</li><li>Athlete</li><li>Athletics</li><li>Australian football</li><li>Automatic external defibrillator</li><li>Back injuries</li><li>Badminton</li><li>Baseball</li><li>Basketball</li><li>Beach</li><li>Behaviour</li><li>Biathlon</li><li>Biochemistry</li><li>Biochemistry and molecular biology of skeletal muscle proteins</li><li>Biology of skeletal muscle</li><li>Biomechanics</li><li>Biopsy</li><li>Blog</li><li>Blood</li><li>BMX</li><li>Bobsleigh</li><li>Body</li><li>Bodybuilding</li><li>Body composition</li><li>Body image</li><li>Bone</li><li>Bone density/endocrine status</li><li>Bone metabolism</li><li>Bone mineral density</li><li>Bowling</li><li>Boxing/Kick Boxing</li><li>Brain</li><li>Breast</li><li>Budo</li><li>Bulimia</li><li>Calf / calves</li><li>Cancer</li><li>Canoe</li><li>Carbohydrates</li><li>Cardiology</li><li>Cardiology physiology</li><li>Cardiology prevention</li><li>Cardiovascular</li><li>Cardiovascular epidemiology</li><li>Cartilage</li><li>Cellular and molecular aspects of the effects of anabolic-androgenic steroids</li><li>Cervical spine</li><li>Child</li><li>Children</li><li>Children's health and exercise</li><li>Children's injuries</li><li>Children and exercise</li><li>Cholesterol</li><li>Chronic</li><li>Clinical case histories</li><li>Clinical haematology</li><li>Collagen</li><li>Collar bone</li><li>Communicable disease</li><li>Community</li><li>Concentric</li><li>Concussion</li><li>Confidentiality</li><li>Consensus</li><li>Consensus statement</li><li>Contact sports</li><li>Core stability</li><li>Cramp</li><li>Creatine</li><li>Cricket</li><li>Crocket</li><li>Cross-country skiing</li><li>Cycling</li><li>Death</li><li>Defibrillator</li><li>Dehydration</li><li>Dentistry</li><li>Depression</li><li>Dermatology</li><li>Diabetes</li><li>Diagnosis</li><li>Disability</li><li>Disclosure</li><li>Diuretic</li><li>Diver</li><li>Diving</li><li>DNA</li><li>DOMS</li><li>Doping</li><li>Drug control</li><li>Drug use</li><li>Dying</li><li>Early childhood malnutrition on physical performance</li><li>Eating disorders</li><li>Eccentric</li><li>Ecological</li><li>Ecology</li><li>Economics</li><li>Education</li><li>Effectiveness</li><li>Elbow</li><li>Elderly people</li><li>Elite performance</li><li>Endocrine</li><li>Endurance</li><li>Energy</li><li>Energy expenditure</li><li>Energy intake methodology</li><li>Energy regulation during lactation</li><li>Energy stores,  energy delivery and substrate utilisation during exercise</li><li>Environment</li><li>Epidemiology</li><li>Equestrian</li><li>Ethics</li><li>Evaluation</li><li>Evidence-based</li><li>Evidence based review</li><li>Exercise</li><li>Exercise and/or caloric restriction effects on body weight/composition</li><li>Exercise physiology</li><li>Exercise rehabilitation</li><li>Exercises</li><li>Exercise testing</li><li>Exertion studies</li><li>Eye</li><li>Face</li><li>Facebook</li><li>Facial bones</li><li>Fall</li><li>Fat</li><li>Fatigue</li><li>Fat percentage</li><li>Feasibility</li><li>Female</li><li>Female athlete triad</li><li>Fencing</li><li>Fever</li><li>Fibre</li><li>Fibre disarray</li><li>Fibromyalgia</li><li>Field hockey</li><li>Figure skating</li><li>Finger</li><li>First aid</li><li>Fit</li><li>Fitness testing</li><li>Fluid balance</li><li>Food intake/body weight regulation</li><li>Foot</li><li>Football</li><li>Forearm</li><li>Forensic</li><li>Fracture</li><li>Fragile</li><li>Frail</li><li>Freestyle skiing</li><li>Functional consequences of malnutrition including dietary protein, energy and iron deficiency</li><li>Gait analysis</li><li>Gender</li><li>Genetics</li><li>Genetic testing</li><li>Glucose</li><li>Golf</li><li>Groin</li><li>Growth</li><li>Gymnastics</li><li>Gynaecology</li><li>Haematology</li><li>Hair</li><li>Hamstring</li><li>Hand</li><li>Handball</li><li>Head</li><li>Health promotion</li><li>Heart</li><li>Heart disease</li><li>Heat</li><li>Heat acclimatisation</li><li>Heat stress</li><li>Helmet</li><li>Herpes</li><li>Hip</li><li>Hockey</li><li>Homeostatis</li><li>Horse racing</li><li>Hyperbaric Medicine</li><li>Hyperbaric medicine</li><li>Hypohydration</li><li>Hypohydration</li><li>Ice hockey</li><li>Illness</li><li>Immune function</li><li>Immune system</li><li>Immunology</li><li>Implementation</li><li>Indoor</li><li>Infection</li><li>Injuries</li><li>Injury</li><li>Interactions between food intake and physical activity/exercise</li><li>Intervention</li><li>Intervention effectiveness</li><li>Intervention efficacy</li><li>Intestines</li><li>Iron metabolism</li><li>Isokinetics</li><li>Issues related to taping and bracing</li><li>Javelin</li><li>Jet lag</li><li>Judo</li><li>Kayak</li><li>Kidney</li><li>Knee</li><li>Knee ACL</li><li>Knee injuries</li><li>Knee surgery</li><li>Knowledge translation</li><li>Korfball</li><li>Lacrosse</li><li>Lactate anaerobic</li><li>Landing impact</li><li>Laryngeal</li><li>Leg</li><li>Ligament</li><li>Lipids</li><li>Liver</li><li>Longevity</li><li>Lower limb quadrant related to injuries</li><li>Lower limb surgery</li><li>Lowever extremity</li><li>Luge</li><li>Lumbar spine</li><li>Lungs</li><li>Male</li><li>Marathon</li><li>Martial Arts</li><li>Maturation</li><li>Measurement</li><li>Medical Ethics</li><li>Medical statistician</li><li>Medicine</li><li>Mental</li><li>Meta-analysis</li><li>Metabolism</li><li>Method</li><li>Methodological</li><li>Miniscal pathology</li><li>Modern pentathlon</li><li>Molecular biochemistry</li><li>Molecular muscle physiology</li><li>Motor sports</li><li>Mountain</li><li>Mountain biking</li><li>Mouth</li><li>MRI</li><li>Muscle</li><li>Muscle damage/injuries</li><li>Muscle imbalance</li><li>Muscle injury and inflammation</li><li>Muscle metabolism</li><li>Nail</li><li>Neck</li><li>Netball</li><li>Neurology</li><li>Neuromuscular</li><li>Non-communicable disease</li><li>Nose</li><li>Nutrition</li><li>Obesity</li><li>Old</li><li>Olympics</li><li>Oncology</li><li>Optometry</li><li>Orthopaedics</li><li>Osteoarthritis</li><li>Osteoporosis</li><li>Outdoor</li><li>Outdoor Medicine</li><li>Overtraining</li><li>Overtraining</li><li>Overuse</li><li>Pelvic</li><li>Pelvic floor</li><li>Pelvis</li><li>Performance</li><li>Pharmacology</li><li>Physical activity</li><li>Physical activity promotion in primary care</li><li>Physical fitness</li><li>Physical stress</li><li>Physician</li><li>Physiology</li><li>Physiotherapist</li><li>Physiotherapy</li><li>Plasma</li><li>Platelet-Rich Plasma</li><li>Podcast</li><li>Podiatry</li><li>Polo</li><li>Position statement</li><li>Post olympic reviews</li><li>Post-pradial lipaemia</li><li>Power</li><li>Power output</li><li>Pregnancy</li><li>Pre-hospital trauma care</li><li>Prevention</li><li>Primary school</li><li>Prospective</li><li>Protection</li><li>Protein</li><li>Psychiatry</li><li>Psychology</li><li>Public health</li><li>Pulmonary</li><li>Quadriceps</li><li>Qualitative</li><li>Quality</li><li>Quality of life</li><li>Quantitative</li><li>Questionnaire</li><li>Radiograph</li><li>Radiography</li><li>Radiology</li><li>Randomised controlled trial</li><li>Recovery</li><li>Recreational trainers</li><li>Recurrent</li><li>Rehabilitation</li><li>Reliability</li><li>Research</li><li>Respiratory</li><li>Resuscitation</li><li>Retrospective</li><li>Review</li><li>Rib</li><li>Rib cage</li><li>Risk factor</li><li>Rock climbing</li><li>Roller hockey</li><li>Rowing</li><li>Rugby</li><li>Running</li><li>Running shoes</li><li>Rupture</li><li>Sailing</li><li>Scapula</li><li>School</li><li>Scuba diving</li><li>Secondary school</li><li>Sedentary</li><li>Senior</li><li>Sexual harassment</li><li>Shin</li><li>Shockwave</li><li>Shooting</li><li>Shoulder</li><li>Sitting time</li><li>Skate board</li><li>Skating</li><li>Skeletal muscle</li><li>Skeleton</li><li>Skiing</li><li>Ski jumping</li><li>Sleep</li><li>Snowboarding</li><li>Soccer</li><li>Social media</li><li>Society</li><li>Sociology</li><li>Softball</li><li>Soft tissue</li><li>Soluble transferring receptor</li><li>Speed</li><li>Speed skating</li><li>Spine</li><li>Spleen</li><li>Spondylolysis</li><li>Sport</li><li>Sport and exercise psychology</li><li>Sport climbing</li><li>Sporting injuries</li><li>Sporting organisation</li><li>Sports</li><li>Sports &amp; exercise medicine</li><li>Sports analysis in different types of sports</li><li>Sports and nutrition</li><li>Sports medicine</li><li>Sports physiotherapy</li><li>Sports rehabilitation programs</li><li>Sprain</li><li>Sprint</li><li>Statistics</li><li>Steroids</li><li>Strength isometric isokinetic</li><li>Stress</li><li>Stress fracture</li><li>Stress response to exercise and adaptations of stress proteins to exercise training</li><li>Study</li><li>Suicide</li><li>Supplements</li><li>Supplements</li><li>Surgery</li><li>Surveillance</li><li>Survival</li><li>Sweat</li><li>Sweating</li><li>Swimming</li><li>Table tennis</li><li>Taekwondo</li><li>Taping and bracing</li><li>Teeth</li><li>Television</li><li>Tendinopathy</li><li>Tendinosis</li><li>Tendon</li><li>Tennis</li><li>Tennis elbow</li><li>Testing</li><li>Thermoregulation</li><li>Thigh</li><li>Thoracic spine</li><li>Thorax</li><li>Toe</li><li>Training</li><li>Trampoline</li><li>Trauma</li><li>Travel</li><li>Treatment</li><li>Triathlon</li><li>Twins</li><li>Twitter</li><li>Ultrasound</li><li>University</li><li>Upper extremity</li><li>Urinary incontinence</li><li>Urology</li><li>Vaccination</li><li>Validation</li><li>Validity</li><li>Vein</li><li>Vessel</li><li>Violence</li><li>Viral</li><li>Virus</li><li>Volleyball</li><li>Walking</li><li>Water polo</li><li>Water skiing</li><li>Water sports</li><li>Weight lifting</li><li>Weight loss</li><li>Well-being</li><li>Wheelchair</li><li>Women</li><li>Wrist</li><li>Young</li></ul></div></div></div></div><div class="col s4 historyTab"/></div></div></div><div data-type="popUp" data-component="jrnlContinueList" data-name="jrnlContinueList" class="hoverInline hidden z-depth-2 bottom" style="min-width:25%"><i class="material-icons arrow-denoter">arrow_drop_down</i><div class="popupHead" data-pop-type="new">Enter Start Value</div><span class="popup-menu"><a class="btn-floating btn-small com-close" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}" data-tooltip="Close"><i class="close material-icons" id="closebtn">close</i></a></span><div class="row" style="max-height:200px;overflow:none;"><div data-input="true" style="margin-bottom:5px;min-height:1.5rem;border-bottom:1px solid #9e9e9e;"/></div><div class="row"><span class="btn btn-medium blue lighten-2" style="font-weight: 600;padding: 8px 12px;" data-channel="components" data-topic="general" data-event="click" data-message="{'click':{'funcToCall': 'content', 'param':{'command': 'continueList','argument':'updateStartValue'},'channel':'menu','topic':'edit'}}">Save</span><span class="btn btn-medium grey lighten-4 close-query" style="margin-left: 8px;color: #505050;font-weight: 600;padding: 8px 12px;" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}">Cancel</span></div></div><div data-type="popUp" data-component="jrnlAuthorGroup_edit" data-name="jrnlAuthorGroup_edit" class="hidden z-depth-2" data-class="jrnlAuthorGroup" data-group="jrnlAuthors" data-id-prefix="contrib"><div data-input-editable="true" style="top: 7%; margin-bottom: 6%;"><span class="popup-menu"><a class="btn-floating btn-small com-close" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'closePopUp'}" data-tooltip="Close"><i class="close material-icons" id="closebtn">close</i></a></span><div class="row"><div class="col s6"><div class="popupHead" data-pop-type="new">Insert Author</div><div class="popupHead" data-pop-type="edit">Edit Author</div></div><div class="col s2 componentMenuItems" style="user-select: none;pointer-events: none;color: #a99b9b;"><span data-name="MenuBtnBold" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'bold'},'channel':'menu','topic':'edit'}}" data-tooltip="Bold" data-menu-style="bold" data-tooltip-id="c122f90d-0a10-576a-fd78-511c56264766"><i class="icon-bold">​</i></span><span data-name="MenuBtnItalic" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'italic'},'channel':'menu','topic':'edit'}}" data-tooltip="Italic" data-menu-style="italic" data-tooltip-id="045de8e0-5efd-924a-0867-c784b06796b4"><i class="icon-italic">​</i></span><span data-name="MenuBtnUnderline" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'underline'},'channel':'menu','topic':'edit'}}" data-tooltip="Underline" data-menu-style="underline" data-tooltip-id="7745f440-7631-7981-8527-950c3bf62b5f"><i class="icon-underline">​</i></span><span data-name="MenuBtnSup" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'superscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Superscript" data-menu-style="superscript" data-tooltip-id="7def3905-4b36-15c2-3a89-8b1a5a46aa16"><i class="icon-superscript">​</i></span><span data-name="MenuBtnSub" data-context-btn="true" class="mnuButtons" data-message="{'click':{'funcToCall': 'formatting', 'param':{'command': 'subscript'},'channel':'menu','topic':'edit'}}" data-tooltip="Subscript" data-menu-style="subscript" data-tooltip-id="67217f2c-e38c-6eef-4a57-b2962e759a94"><i class="icon-subscript">​</i></span></div><div class="col s4"><div class="historyHead">Change History<div class="historyViewController row"><div class="defaultview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Default</div><div class="expandview col s6" data-message="{'click':{'funcToCall': 'collapseReply','channel':'components','topic':'PopUp'}}">Expand</div></div></div></div></div><div class="row"><div class="col s8" data-wrapper="true" data-overflow="true"><div data-name="editable-orcid-section" class="row orcid-section" data-if-selector=".//*[@class='jrnlAuthor']"><div class="input-field col m12"><span data-class="jrnlOrcid" data-selector=".//*[@class='jrnlOrcid']" data-validate="orcid" data-check-field="jrnlOrcid" data-type="htmlComponent" data-data-type="text" class="text-line" type="text" data-parent="jrnlAuthorGroup"/><label>Orcid</label></div></div><div class="flex author-section" data-if-selector=".//*[@class='jrnlAuthor']"><div class="input-field" data-name="author-prefix"><span class="text-line" data-data-type="text" data-class="jrnlPrefix" data-selector=".//*[@class='jrnlPrefix']" data-type="htmlComponent" type="text"/><label>Prefix</label></div><div class="input-field"><span data-class="jrnlGivenName" data-data-type="text" data-selector=".//*[@class='jrnlGivenName']" data-variables="$ID==.//*[@class='jrnlAuthor']/@id" data-type="htmlComponent" class="text-line" type="text" data-channel="components" data-topic="general" data-event="keyup" data-message="{'funcToCall': 'checkCollabAuthor'}" data-trim-space="true"/><label>Given name</label></div><div class="input-field"><span class="text-line" data-data-type="text" data-class="jrnlSurName" data-selector=".//*[@class='jrnlSurName']" data-variables="$ID==.//*[@class='jrnlAuthor']/@id" data-type="htmlComponent" data-validate="required" type="text" data-channel="components" data-topic="general" data-event="keyup" data-message="{'funcToCall': 'checkCollabAuthor'}" data-trim-space="true"/><label>Surname</label></div></div><div class="row role-section" data-name="role-section"><div class="collection-header"><label for="role-field">Role:</label></div><ul class="collection role-holder" data-type="nonLinkedObject"><li data-template="true" data-class="jrnlRole" data-type="htmlComponent" type="text" data-selector=".//*[@class='jrnlRole']" data-parent="jrnlAuthorGroup" data-set-attribute="data-rid" class="collection-item dismissable display-table" data-append-button="deleteLink" data-message="{'click':{'funcToCall': 'editAuthorRole','param' : {'component':'jrnlAuthorRole_edit'},'channel':'components','topic':'general'}}"/></ul></div><div class="row author-section" data-name="jrnlOnBehalfOf" data-if-selector=".//*[@class='jrnlAuthor']"><div class="input-field col s12"><span class="text-line" data-data-type="text" data-class="jrnlOnBehalfOf" data-selector=".//*[@class='jrnlOnBehalfOf']" data-append="true" data-type="htmlComponent" type="text"/><label>On behalf of</label></div></div><div class="row" data-name="deceased-comp"><div class="collection-header"><input type="checkbox" class="filled-in" id="deceased-checkbox" data-class="jrnlDeceased" data-note-class="jrnlDeceased" data-selector=".//*[@class='jrnlDeceased'][not(@data-track='del')]" data-type="deceasedCheckBox" data-save-type="setDeceasedAuthor" data-validate="requiredField" data-check-field="jrnlDeceased"/><label for="deceased-checkbox" style="margin-top:10px">Deceased: </label><span class="jrnlDeceased text-line" data-class="jrnlDeceased" data-selector=".//*[@data-fn-type='deceased']//*[@class='jrnlFNText']//p" data-type="htmlComponent" data-save-type="setDeceasedAuthor"/></div></div><div class="row author-section" data-if-selector=".//*[@class='jrnlAuthor']" data-name="biography" data-message="{'click':{'funcToCall': 'openInsertLink' ,'channel':'components','topic':'general'},'focusout':{'funcToCall': 'openInsertLink' ,'channel':'components','topic':'general'}}"><div class="input-field col s12"><span class="text-line" data-class="jrnlBio" data-formating="true" data-selector=".//*[@class='jrnlBio']" data-append="true" data-type="htmlComponent" type="text"/><label>Author Bio</label><div class="fileList" style="margin: 10px;text-align:left;"><span data-class="jrnlFigure" type="objFile" data-type="AuthorBioImage" data-selector=".//*[@class='jrnlFigure']"/></div></div></div><div class="row collab-section" data-if-selector=".//*[@class='jrnlCollaboration']"><div class="input-field col s12"><span class="text-line" data-class="jrnlCollaboration" data-selector=".//*[@class='jrnlCollaboration']" data-type="htmlComponent" type="text" data-channel="components" data-topic="general" data-event="keyup" data-message="{'funcToCall': 'checkCollabAuthor'}" data-validate="required" data-trim-space="true"/><label>Collaboration</label></div></div><div class="row" data-name="corresp-email-comp" data-override="true"><div class="col s12"><input type="checkbox" class="filled-in" id="corresp-checkbox" data-class="jrnlCorrRef" data-note-class="jrnlCorrAff" data-selector=".//*[@class='jrnlCorrRef'][not(@data-track='del')]" data-type="checkBoxComponent" data-validate="requiredField" data-save-type="setCorrespondingAuthor" data-check-field="jrnlEmail"/><label for="corresp-checkbox" style="margin-top:10px">Corresponding: </label><div class="input-field"><span class="text-line" data-data-type="text" type="text" data-selector="//*[@data-id=.//*[@data-ref-type='corresp'][not(@data-track='del')]/@data-rid]|.//*[@class='jrnlEmail']" data-class="jrnlEmail" data-type="htmlComponent" data-email="true" data-parent="jrnlAuthorGroup"/><label for="email">Email</label></div><div class="jrnlCorAddress text-line" contenteditable="false" data-class="jrnlCorAddress" data-selector="//*[@class='jrnlCorrAff'][@data-id='$RID']/*[@class='jrnlCorAddress']" data-type="htmlComponent" data-variables="$RID==.//*[@class='jrnlCorrRef'][not(@data-track='del')]/@data-rid" type="text" data-channel="components" data-topic="PopUp" data-event="click" data-message="{'funcToCall': 'editSubPopUp', 'param' : {'component':'jrnlCorrAff_edit'}}" data-sethtml="false"/></div></div><div class="row"><div class="input-field col s3"><label for="equal-contrib-field">Equal contributor with:</label></div><div class="input-field col s9"><div data-type="inputTags" type="inputTags" id="equal-contrib-field" data-class="jrnlEqContribRef" data-selector="//*[@class='jrnlEqContribRef'][@data-ref-type='equal'][not(contains(@data-track, 'del'))][contains(@data-rid,'$RID')]/parent::span[not(contains(@id,'$ID'))]/span[@class='jrnlAuthor' or @class='jrnlCollaboration']" data-alt-tmp-selector="//*[@data-element-template='true']//*[@tmp-class='jrnlEqContrib'][@tmp-data-fn-type='equal']" data-variables="$RID==.//*[@class='jrnlEqContribRef'][@data-ref-type='equal']/@data-rid,$ID==./@id" data-suggestion="//*[@id='contentDivNode']//*[@class='jrnlAuthors']//*[not(contains(@class,'activeElement')) and not(contains(@data-track, 'del'))]//span[@class='jrnlAuthor']|//*[@id='contentDivNode']//*[@class='jrnlAuthors']//*[not(contains(@class,'activeElement')) and not(contains(@data-track, 'del'))]//span[@class='jrnlCollaboration']" data-id-prefix="equal-contrib" data-save-type="addEqualContrib" data-fn-type="equal" data-tmp-selector="//*[@data-element-template='true']//*[@tmp-class='jrnlEqContrib'][@tmp-data-fn-type='equal']"><span data-template="true" class="input-tags chip orange lighten-1"><i class="close material-icons">close</i></span><div data-input="true" class="kriya-chips" data-source="tags"/></div></div></div><div class="row" style="margin: 12px 0px !important;" data-name="first-author"><div class="input-field col s3"><label for="first-authorship-field">First authorship with:</label></div><div class="input-field col s9"><div data-type="inputTags" type="inputTags" id="first-authorship-field" data-class="jrnlEqContribRef" data-selector="//*[@class='jrnlEqContribRef'][@data-ref-type='first-authorship'][contains(@data-rid,'$RID')]/parent::span[not(contains(@id,'$ID'))]/span[@class='jrnlAuthor']" data-variables="$RID==.//*[@class='jrnlEqContribRef'][@data-ref-type='first-authorship']/@data-rid,$ID==./@id" data-suggestion="//*[@id='contentDivNode']//*[@class='jrnlAuthors']//*[not(contains(@class,'activeElement')) and not(contains(@data-track, 'del'))]//span[@class='jrnlAuthor']" data-id-prefix="first-authorship" data-save-type="addEqualContrib" data-fn-type="first-authorship" data-tmp-selector="//*[@data-element-template='true']//*[@tmp-class='jrnlEqContrib'][@tmp-data-fn-type='first-authorship']"><span data-template="true" class="input-tags chip orange lighten-1"><i class="close material-icons">close</i></span><div data-input="true" class="kriya-chips" data-source="tags"/></div></div></div><div class="row" style="margin: 12px 0px 12px 0px !important;" data-name="senior-author"><div class="input-field col s3"><label for="senior-authorship-field">Senior authorship with:</label></div><div class="input-field col s9"><div data-type="inputTags" type="inputTags" id="senior-authorship-field" data-class="jrnlEqContribRef" data-selector="//*[@class='jrnlEqContribRef'][@data-ref-type='senior-authorship'][contains(@data-rid,'$RID')]/parent::span[not(contains(@id,'$ID'))]/span[@class='jrnlAuthor']" data-variables="$RID==.//*[@class='jrnlEqContribRef'][@data-ref-type='senior-authorship']/@data-rid,$ID==./@id" data-suggestion="//*[@id='contentDivNode']//*[@class='jrnlAuthors']//*[not(contains(@class,'activeElement')) and not(contains(@data-track, 'del'))]//span[@class='jrnlAuthor']" data-id-prefix="senior-authorship" data-save-type="addEqualContrib" data-fn-type="senior-authorship" data-node-value="&amp;sect;" data-tmp-selector="//*[@data-element-template='true']//*[@tmp-class='jrnlEqContrib'][@tmp-data-fn-type='senior-authorship']"><span data-template="true" class="input-tags chip orange lighten-1"><i class="close material-icons">close</i></span><div data-input="true" class="kriya-chips" data-source="tags"/></div></div></div><div class="row aff-section"><div class="collection-header"><label for="affiliation-field">Affiliations:</label></div><ul class="collection" type="linkedObjects" id="affiliation-field" data-selector=".//*[@class='jrnlAffRef'][not(@data-track='del')]" data-class="jrnlAffRef" data-type="getLinkedObjects" data-parent-container="//*[@class='jrnlAuthorsGroup']//*[@class='jrnlAffGroup']" data-source-selector="//*[@class='jrnlAff'][@id][@data-id]" data-id-prefix="aff" data-source="jrnlAff" data-reorder-link="true"><li data-template="true" class="collection-item dismissable display-table"><span data-input="true" style="padding-left:1.7rem;"/></li></ul></div><div class="row pa-section" data-name="present-address"><div class="collection-header"><label for="present-add-field">Present address:</label></div><ul class="collection" data-selector=".//*[@class='jrnlPresAddRef']" data-class="jrnlPresAddRef" type="linkedObjects" id="present-add-field" data-type="getLinkedObjects" data-source-selector="//*[@class='jrnlPresAdd'][@id][@data-id]" data-id-prefix="pa" data-source="jrnlPresAdd" data-reorder-link="true"><li data-template="true" class="collection-item dismissable display-table"><span data-input="true" style="padding-left:1.7rem;"/></li></ul></div><div class="contrib-section" data-name="contrib-elife"><div class="row collection-header">Contribution:</div><div class="row contrib-lists"><div class="row"><div class="col m6"><div class="row"><input type="checkbox" class="filled-in" id="conceptualization" data-selector=".//*[@class='jrnlConRef'][@data-con-type='conceptualization']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="conceptualization">Conceptualization</label></div><div class="row"><input type="checkbox" class="filled-in" id="data-curation" data-selector=".//*[@class='jrnlConRef'][@data-con-type='data-curation']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="data-curation">Data curation</label></div><div class="row"><input type="checkbox" class="filled-in" id="formal-analysis" data-selector=".//*[@class='jrnlConRef'][@data-con-type='formal-analysis']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="formal-analysis">Formal analysis</label></div><div class="row"><input type="checkbox" class="filled-in" id="funding-acquisition" data-selector=".//*[@class='jrnlConRef'][@data-con-type='funding-acquisition']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="funding-acquisition">Funding acquisition</label></div><div class="row"><input type="checkbox" class="filled-in" id="investigation" data-selector=".//*[@class='jrnlConRef'][@data-con-type='investigation']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="investigation">Investigation</label></div><div class="row"><input type="checkbox" class="filled-in" id="methodology" data-selector=".//*[@class='jrnlConRef'][@data-con-type='methodology']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="methodology">Methodology</label></div><div class="row"><input type="checkbox" class="filled-in" id="project-administration" data-selector=".//*[@class='jrnlConRef'][@data-con-type='project-administration']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="project-administration">Project administration</label></div></div><div class="col m6"><div class="row"><input type="checkbox" class="filled-in" id="resources" data-selector=".//*[@class='jrnlConRef'][@data-con-type='resources']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="resources">Resources</label></div><div class="row"><input type="checkbox" class="filled-in" id="software" data-selector=".//*[@class='jrnlConRef'][@data-con-type='software']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="software">Software</label></div><div class="row"><input type="checkbox" class="filled-in" id="supervision" data-selector=".//*[@class='jrnlConRef'][@data-con-type='supervision']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="supervision">Supervision</label></div><div class="row"><input type="checkbox" class="filled-in" id="validation" data-selector=".//*[@class='jrnlConRef'][@data-con-type='validation']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="validation">Validation</label></div><div class="row"><input type="checkbox" class="filled-in" id="visualization" data-selector=".//*[@class='jrnlConRef'][@data-con-type='visualization']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="visualization">Visualization</label></div><div class="row"><input type="checkbox" class="filled-in" id="writing-original-draft" data-selector=".//*[@class='jrnlConRef'][@data-con-type='writing-original-draft']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="writing-original-draft">Writing – original draft</label></div><div class="row"><input type="checkbox" class="filled-in" id="writing-review-and-editing" data-selector=".//*[@class='jrnlConRef'][@data-con-type='writing-review-and-editing']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote"/><label for="writing-review-and-editing">Writing – review and editing</label></div></div></div><div class="row"><input type="checkbox" class="filled-in" id="other" data-selector=".//*[@class='jrnlConRef'][@data-con-type='other']" data-class="jrnlConRef" data-source="jrnlConFN" data-type="checkBoxComponent" data-save-type="setFootnote" data-validate="requiredField" data-check-field="otherContrib"/><label for="other">Other</label><div class="input-field"><span class="text-box" data-class="otherContrib" type="text"/></div></div></div></div><div class="row group-author-section" data-name="group-author" data-if-selector=".//*[@class='jrnlCollaboration']"><div class="collection-header">Group Authors: <AUTHORS>