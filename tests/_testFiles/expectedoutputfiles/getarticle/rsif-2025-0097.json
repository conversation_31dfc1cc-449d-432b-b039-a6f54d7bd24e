[{"_index": "dev-customers", "_id": "bmj", "_score": 1.0076046, "_source": {"projects": [{"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/aim/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/aim/jobTemplate.xml", "name": "acupmed", "fullName": "AIM", "tableSetterCSS": "/css/table_setter/customers/bmj/acupmed/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/annrheumdis/jobTemplate.xml", "fullName": "Annals of the Rheumatic Diseases", "proofConfig-journal-id": "ard", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/annrheumdis/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/annrheumdis/annrheumdis_proofConfig.xml", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/annrheumdis/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/archdischild/jobTemplate.xml", "fullName": "Archives of Disease in Childhood", "proofConfig-journal-id": "adc", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/archdischild/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/archdischild/archdischild_proofConfig.xml", "name": "archdischild", "tableSetterCSS": "/css/table_setter/customers/bmj/archdischild/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bcr/jobTemplate.xml", "fullName": "BMJ Case Reports", "proofConfig-journal-id": "bmjcasereports", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bcr/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "proofConfig": "/config/bcr/bcr_proofConfig.xml", "name": "bcr", "tableSetterCSS": "/css/table_setter/customers/bmj/bcr/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bjophthalmol/jobTemplate.xml", "fullName": "BJOPHTHALMOL", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bjophthalmol/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "name": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "customcomponentTemplate": "/config/default/_config_review_content_bjo.xml", "tableSetterCSS": "/css/table_setter/customers/bmj/bjophthalmol/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bjsports/jobTemplate.xml", "fullName": "BJSPORTS", "proofConfig-journal-id": "bjsm", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bjsports/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/bjsports/bjsports_proofConfig.xml", "name": "bjsports", "tableSetterCSS": "/css/table_setter/customers/bmj/bjsports/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "j<PERSON><PERSON><PERSON>@uw.edu"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/default/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjacademic/jobTemplate.xml", "name": "bmjacademic", "fullName": "THEBMJ", "tableSetterCSS": "/css/table_setter/customers/bmj/default/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "styleTemplate": "/default/styleTemplate_thebmj.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjdrc/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjdrc/jobTemplate.xml", "name": "bmjdrc", "fullName": "BMJ Open Diabetes Research &amp; Care", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjdrc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjgast/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjgast/jobTemplate.xml", "name": "bmjgast", "fullName": "BMJ Open Gastroenterology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjgast/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<PERSON><PERSON><PERSON>@uhs.nhs.uk; RM.<PERSON><PERSON>@btinternet.com"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjgh/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjgh/jobTemplate.xml", "name": "bmjgh", "fullName": "BMJ Global Health", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjgh/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "<PERSON><PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjhci/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/bmjhci/jobTemplate.xml", "name": "bmjhci", "fullName": "BMJ Health &amp; Care Informatics", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjhci/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjinnov/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjinnov/jobTemplate.xml", "name": "b<PERSON><PERSON><PERSON>", "fullName": "BMJ Innovations", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjinnov/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjmedicine/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/bmjmedicine/jobTemplate.xml", "name": "bmjmedicine", "fullName": "BMJ Medicine", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjmedicine/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjment/jobTemplate.xml", "fullName": "BMJ Mental Health", "proofConfig-journal-id": "ebmh", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ebmental/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjment/bmjment_proofConfig.xml", "name": "bmjment", "tableSetterCSS": "/css/table_setter/customers/bmj/ebmental/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "andrea.c<PERSON><PERSON><EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjmilitary/jobTemplate.xml", "fullName": "BMJ Military Health", "proofConfig-journal-id": "bmjmilitary", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jramc/indesignAutoPageConfig.js", "pe": "<PERSON>", "name": "bmjmilitary", "tableSetterCSS": "/css/table_setter/customers/bmj/jramc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "Editor", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjno/jobTemplate.xml", "fullName": "BMJ Neurology Open", "proofConfig-journal-id": "bmjno", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjno/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proofConfig": "/config/bmjno/bmjno_proofConfig.xml", "name": "bmjno", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjno/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjnph/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/bmjnph/jobTemplate.xml", "name": "bmjnph", "fullName": "BMJ Nutrition, Prevention &amp; Health", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjnph/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjopen/jobTemplate.xml", "fullName": "BMJ Open", "proofConfig-journal-id": "bm<PERSON><PERSON>", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjopen/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjopen/bmjopen_proofConfig.xml", "name": "bm<PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjopen/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjophth/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjophth/jobTemplate.xml", "name": "bm<PERSON><PERSON><PERSON>", "fullName": "BMJ Open Ophthalmology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjophth/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjoq/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/bmjoq/jobTemplate.xml", "name": "bmjoq", "fullName": "BMJ Open Quality", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjoq/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjos/jobTemplate.xml", "fullName": "BMJ Open Science", "proofConfig-journal-id": "bmjos", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjos/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjos/bmjos_proofConfig.xml", "name": "bmjos", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjos/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjph/jobTemplate.xml", "fullName": "BMJ Public Health", "proofConfig-journal-id": "bmjph", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjph/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjph/bmjph_proofConfig.xml", "name": "bmjph", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjph/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjpo/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jobTemplate": "/bmjpo/jobTemplate.xml", "name": "bmjpo", "fullName": "BMJ Paediatrics Open", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjpo/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjqs/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjqs/jobTemplate.xml", "name": "bmjqs", "fullName": "BMJ Quality &amp; Safety", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjqs/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; eric.<PERSON>@uth.tmc.edu"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjresp/jobTemplate.xml", "fullName": "BMJ Open Respiratory Research", "proofConfig-journal-id": "bmjopenrespres", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjresp/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "name": "bmjresp", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjresp/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjsem/jobTemplate.xml", "fullName": "BMJSEM", "proofConfig-journal-id": "bmjosem", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjsem/indesignAutoPageConfig.js", "pe": "<PERSON>", "name": "bmjsem", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjsem/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjsit/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/bmjsit/jobTemplate.xml", "name": "bmj<PERSON>t", "fullName": "BMJ Surgery, Interventions, &amp; Health Technologies", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjsit/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON>", "parameters": {"publishing-email": "<EMAIL>", "editor-name": "Editors", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>; <EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjspcare/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjspcare/jobTemplate.xml", "name": "bmjspcare", "fullName": "BMJ Supportive &amp; Palliative Care", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjspcare/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "declan.<PERSON>@atriumhealth.org"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjsrh/jobTemplate.xml", "fullName": "BMJSRH", "proofConfig-journal-id": "bmjsrh", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjsrh/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/bmjsrh/bmjsrh_proofConfig.xml", "name": "bmjsrh", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjsrh/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjstel/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjstel/jobTemplate.xml", "name": "bmjstel", "fullName": "BMJSTEL", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjstel/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/conmed/jobTemplate.xml", "fullName": "Considerations in Medicine", "proofConfig-journal-id": "conmed", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/conmed/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/conmed/conmed_proofConfig.xml", "name": "conmed", "tableSetterCSS": "/css/table_setter/customers/bmj/conmed/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/dtb/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/dtb/jobTemplate.xml", "name": "dtb", "fullName": "Drug and Therapeutics Bulletin", "tableSetterCSS": "/css/table_setter/customers/bmj/dtb/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "<PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>; dphi<PERSON><PERSON><PERSON>@bmj.com"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjebm/jobTemplate.xml", "fullName": "BMJ Evidence-Based Medicine", "proofConfig-journal-id": "ebm", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjebm/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjebm/bmjebm_proofConfig.xml", "name": "bmjebm", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjebm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "juan<PERSON><PERSON>@bmj.com"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/ebmental/jobTemplate.xml", "fullName": "Evidence-Based Mental Health", "proofConfig-journal-id": "ebmh", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ebmental/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/ebmental/ebmental_proofConfig.xml", "name": "eb<PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/ebmental/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/ebnurs/jobTemplate.xml", "fullName": "Evidence Based Journals", "proofConfig-journal-id": "ebn", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ebnurs/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/ebnurs/ebnurs_proofConfig.xml", "name": "ebnurs", "tableSetterCSS": "/css/table_setter/customers/bmj/ebnurs/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/edpract/jobTemplate.xml", "fullName": "Archives of Disease in Childhood: Education &amp; Practice Edition", "proofConfig-journal-id": "eap", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/edpract/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/edpract/edpract_proofConfig.xml", "name": "edpract", "tableSetterCSS": "/css/table_setter/customers/bmj/edpract/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; Neelam<PERSON>@gstt.nhs.uk"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/ejhpharm/jobTemplate.xml", "fullName": "European Journal of Hospital Pharmacy", "proofConfig-journal-id": "ejhp", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ejhpharm/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/ejhpharm/ejhpharm_proofConfig.xml", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/ejhpharm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/emermed/jobTemplate.xml", "fullName": "Emergency Medicine Journal", "proofConfig-journal-id": "emj", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/emermed/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/emermed/emermed_proofConfig.xml", "name": "emermed", "tableSetterCSS": "/css/table_setter/customers/bmj/emermed/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<PERSON><PERSON>@ucsf.edu"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/esmoopen/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/esmoopen/jobTemplate.xml", "name": "esmoopen", "fullName": "ESMO Open", "tableSetterCSS": "/css/table_setter/customers/bmj/esmoopen/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/fetalneonatal/jobTemplate.xml", "fullName": "Archives of Disease in Childhood: Fetal and Neonatal Edition", "proofConfig-journal-id": "fnn", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/fetalneonatal/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/fetalneonatal/fetalneonatal_proofConfig.xml", "name": "fetalneonatal", "tableSetterCSS": "/css/table_setter/customers/bmj/fetalneonatal/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; Neelam<PERSON>@gstt.nhs.uk"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/flgastro/jobTemplate.xml", "fullName": "Frontline Gastroenterology", "proofConfig-journal-id": "fg", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/flgastro/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/flgastro/flgastro_proofConfig.xml", "name": "flgastro", "tableSetterCSS": "/css/table_setter/customers/bmj/flgastro/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "<PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/fmch/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "jobTemplate": "/fmch/jobTemplate.xml", "name": "fmch", "fullName": "Family Medicine and Community Health", "tableSetterCSS": "/css/table_setter/customers/bmj/fmch/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/gpsych/jobTemplate.xml", "fullName": "GPSYCH", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/gpsych/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "name": "gpsych", "customcomponentTemplate": "/config/default/_config_review_content_gpsych.xml", "tableSetterCSS": "/css/table_setter/customers/bmj/gpsych/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "Editor", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/gutjnl/jobTemplate.xml", "fullName": "GUT", "proofConfig-journal-id": "gut", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/gutjnl/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/gutjnl/gutjnl_proofConfig.xml", "name": "<PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/gutjnl/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/heartasia/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/heartasia/jobTemplate.xml", "name": "heartasia", "fullName": "HEART ASIA", "tableSetterCSS": "/css/table_setter/customers/bmj/heartasia/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/heartjnl/jobTemplate.xml", "fullName": "Heart", "proofConfig-journal-id": "heart", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/heartjnl/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/heartjnl/heartjnl_proofConfig.xml", "name": "heartjnl", "tableSetterCSS": "/css/table_setter/customers/bmj/heartjnl/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/ihj/jobTemplate.xml", "fullName": "Integrated Healthcare Journal", "proofConfig-journal-id": "ihj", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ihj/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proofConfig": "/config/ihj/ihj_proofConfig.xml", "name": "ihj", "tableSetterCSS": "/css/table_setter/customers/bmj/ihj/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/ijgc/jobTemplate.xml", "fullName": "International Journal of Gynecologic Cancer", "styleTemplate": "/default/styleTemplate_ijgc.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/ijgc/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/ijgc/ijgc_proofConfig.xml", "name": "ijgc", "tableSetterCSS": "/css/table_setter/customers/bmj/ijgc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/injuryprev/jobTemplate.xml", "fullName": "Injury Prevention", "proofConfig-journal-id": "ip", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/injuryprev/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/injuryprev/injuryprev_proofConfig.xml", "name": "<PERSON><PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/injuryprev/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/default/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/inpractice/jobTemplate.xml", "name": "inpractice", "fullName": "INPRACTICE", "tableSetterCSS": "/css/table_setter/customers/bmj/inpract/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/jclinpath/jobTemplate.xml", "fullName": "Journal of Clinical Pathology", "proofConfig-journal-id": "jcp", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jclinpath/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/jclinpath/jclinpath_proofConfig.xml", "name": "jc<PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/jclinpath/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jech/indesignAutoPageConfig.js", "pe": "<PERSON>", "jobTemplate": "/jech/jobTemplate.xml", "name": "jech", "fullName": "Journal of Epidemiology &amp; Community Health", "tableSetterCSS": "/css/table_setter/customers/bmj/jech/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<PERSON><PERSON>@glasgow.ac.uk; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/familyplanning/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/jfphrc/jobTemplate.xml", "name": "jfphrc", "fullName": "JFPHRC", "tableSetterCSS": "/css/table_setter/customers/bmj/jfp/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/jim/jobTemplate.xml", "fullName": "Journal of Investigative Medicine", "proofConfig-journal-id": "jim", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jim/indesignAutoPageConfig.js", "pe": "Tasnia Nizam", "proofConfig": "/config/jim/jim_proofConfig.xml", "name": "jim", "tableSetterCSS": "/css/table_setter/customers/bmj/jim/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/jisakos/jobTemplate.xml", "fullName": "Journal of ISAKOS", "proofConfig-journal-id": "<PERSON><PERSON><PERSON>", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jisakos/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/jisakos/jisakos_proofConfig.xml", "name": "<PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/jisakos/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"editor-name": "<PERSON><PERSON><PERSON> Blank<PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "fullName": "Journal of Immunotherapy for Cancer", "proofConfig-journal-id": "jitc", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jitc/indesignAutoPageConfig.js", "pe": "Tasnia Nizam", "proofConfig": "/config/jitc/jitc_proofConfig.xml", "name": "jitc", "tableSetterCSS": "/css/table_setter/customers/bmj/jitc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/jmedgenet/jobTemplate.xml", "fullName": "JMEDGENET", "proofConfig-journal-id": "jmg", "styleTemplate": "/default/styleTemplate_jmedgenet.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jmedgenet/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/jmedgenet/jmedgenet_proofConfig.xml", "name": "jmedgenet", "tableSetterCSS": "/css/table_setter/customers/bmj/jmedgenet/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jnnp/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/jnnp/jobTemplate.xml", "name": "jnnp", "fullName": "Journal of Neurology, Neurosurgery, and Psychiatry", "tableSetterCSS": "/css/table_setter/customers/bmj/jnnp/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "k<PERSON><EMAIL>; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jramc/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/jramc/jobTemplate.xml", "name": "jramc", "fullName": "Journal of the Royal Army Medical Corps", "tableSetterCSS": "/css/table_setter/customers/bmj/jramc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"editor-name": "Editor", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/leader/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/leader/jobTemplate.xml", "name": "leader", "fullName": "BMJ Leader", "tableSetterCSS": "/css/table_setter/customers/bmj/leader/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/lupus/jobTemplate.xml", "fullName": "Lupus Science &amp; Medicine", "proofConfig-journal-id": "lupus", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/lupus/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "name": "lupus", "tableSetterCSS": "/css/table_setter/customers/bmj/lupus/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "r.<PERSON><PERSON>@amc.nl; <EMAIL>; <PERSON><PERSON>@nyulangone.org"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/medethics/jobTemplate.xml", "fullName": "Journal of Medical Ethics", "proofConfig-journal-id": "jme", "styleTemplate": "/default/styleTemplate_medethics.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/medethics/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/medethics/medethics_proofConfig.xml", "name": "medethics", "tableSetterCSS": "/css/table_setter/customers/bmj/medethics/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/medhum/jobTemplate.xml", "fullName": "Medical Humanities", "proofConfig-journal-id": "mh", "styleTemplate": "/default/styleTemplate_medhum.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/medhum/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/medhum/medhum_proofConfig.xml", "name": "medhum", "tableSetterCSS": "/css/table_setter/customers/bmj/medhum/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/neurintsurg/jobTemplate.xml", "fullName": "Journal of NeuroInterventional Surgery", "proofConfig-journal-id": "jnis", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/neurintsurg/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/neurintsurg/neurintsurg_proofConfig.xml", "name": "neurints<PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/neurintsurg/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/oemed/jobTemplate.xml", "fullName": "Occupational and Environmental Medicine", "proofConfig-journal-id": "oem", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/oemed/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proofConfig": "/config/oemed/oemed_proofConfig.xml", "name": "oemed", "tableSetterCSS": "/css/table_setter/customers/bmj/oemed/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "h.<PERSON><PERSON>@uu.nl"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/openhrt/jobTemplate.xml", "fullName": "Open Heart", "proofConfig-journal-id": "openheart", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/openhrt/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/openhrt/openhrt_proofConfig.xml", "name": "openhrt", "tableSetterCSS": "/css/table_setter/customers/bmj/openhrt/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/postgradmedj/jobTemplate.xml", "fullName": "Postgraduate Medical Journal", "proofConfig-journal-id": "pmj", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/postgradmedj/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/postgradmedj/postgradmedj_proofConfig.xml", "name": "postgradmedj", "tableSetterCSS": "/css/table_setter/customers/bmj/postgradmedj/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/practneurol/jobTemplate.xml", "fullName": "Practical Neurology", "proofConfig-journal-id": "pn", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/practneurol/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/practneurol/practneurol_proofConfig.xml", "name": "practneurol", "tableSetterCSS": "/css/table_setter/customers/bmj/practneurol/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "editor-name": "<PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/rapm/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "jobTemplate": "/rapm/jobTemplate.xml", "name": "<PERSON>m", "fullName": "Regional Anesthesia &amp; Pain Medicine", "tableSetterCSS": "/css/table_setter/customers/bmj/rapm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/rmdopen/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/rmdopen/jobTemplate.xml", "name": "rmdopen", "fullName": "RMD Open", "tableSetterCSS": "/css/table_setter/customers/bmj/rmdopen/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "editor-name": "<PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/sextrans/jobTemplate.xml", "fullName": "Sexually Transmitted Infections", "proofConfig-journal-id": "sti", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/sextrans/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "proofConfig": "/config/sextrans/sextrans_proofConfig.xml", "name": "sextrans", "tableSetterCSS": "/css/table_setter/customers/bmj/sextrans/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "bmj.editor.amger<PERSON>@gmail.com"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/svn/jobTemplate.xml", "fullName": "Stroke and Vascular Neurology", "proofConfig-journal-id": "svn", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/svn/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/svn/svn_proofConfig.xml", "name": "svn", "tableSetterCSS": "/css/table_setter/customers/bmj/svn/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/thoraxjnl/jobTemplate.xml", "fullName": "Thor<PERSON>", "proofConfig-journal-id": "thorax", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/thoraxjnl/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/thoraxjnl/thoraxjnl_proofConfig.xml", "name": "thorax<PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/thoraxjnl/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/tobaccocontrol/jobTemplate.xml", "fullName": "Tobacco Control", "proofConfig-journal-id": "tc", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/tobaccocontrol/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "proofConfig": "/config/tobaccocontrol/tobaccocontrol_proofConfig.xml", "name": "tobaccocontrol", "tableSetterCSS": "/css/table_setter/customers/bmj/tobaccocontrol/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"pe-email": "<EMAIL>", "publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/tsaco/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/tsaco/jobTemplate.xml", "name": "tsaco", "fullName": "Trauma Surgery &amp; Acute Care Open", "tableSetterCSS": "/css/table_setter/customers/bmj/tsaco/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate_tsaco.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/vetrec/jobTemplate.xml", "fullName": "Veterinary Record", "proofConfig-journal-id": "veterinaryrecord", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/vetrec/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "proofConfig": "/config/vetrec/vetrec_proofConfig.xml", "name": "vetrec", "tableSetterCSS": "/css/table_setter/customers/bmj/vetrec/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/vetreccr/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/vetreccr/jobTemplate.xml", "name": "vetreccr", "fullName": "Veterinary Record Case Reports", "tableSetterCSS": "/css/table_setter/customers/bmj/vetreccr/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/vetreco/jobTemplate.xml", "fullName": "Veterinary Record Open", "proofConfig-journal-id": "vetreco", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/vetreco/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "proofConfig": "/config/vetreco/vetreco_proofConfig.xml", "name": "vetreco", "tableSetterCSS": "/css/table_setter/customers/bmj/vetreco/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"editor-name": "<PERSON><PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/wjps/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/wjps/jobTemplate.xml", "name": "wjps", "fullName": "World Journal of Pediatric Surgery", "tableSetterCSS": "/css/table_setter/customers/bmj/wjps/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "editor-name": "<PERSON><PERSON>", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "s<PERSON><PERSON><PERSON>@zju.edu.cn; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjonc/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "jobTemplate": "/bmjonc/jobTemplate.xml", "name": "bmjon<PERSON>", "fullName": "BMJ Oncology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjonc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/egastro/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON><PERSON>", "jobTemplate": "/egastro/jobTemplate.xml", "name": "egastro", "fullName": "eGastroenterology", "tableSetterCSS": "/css/table_setter/customers/bmj/egastro/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "editor-name": "Editor", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "bo-goran.er<PERSON><PERSON>@ki.se; j<PERSON><PERSON>@jhmi.edu; <EMAIL>; e<PERSON><EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/gocm/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>", "jobTemplate": "/gocm/jobTemplate.xml", "name": "gocm", "fullName": "Gynecology and Obstetrics Clinical Medicine", "tableSetterCSS": "/css/table_setter/customers/bmj/gocm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "editor-name": "Editor", "publisher-email": "<EMAIL>", "editor-email": "<EMAIL>", "eic-email": "wang<PERSON><PERSON><PERSON><PERSON>@pkuph.edu.cn; <EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjdhai/jobTemplate.xml", "fullName": "BMJ Digital Health &amp; AI", "proofConfig-journal-id": "bm<PERSON><PERSON><PERSON>", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjdhai/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjdhai/bmjdhai_proofConfig.xml", "name": "bm<PERSON><PERSON><PERSON>", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjdhai/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/bmjccgg/jobTemplate.xml", "fullName": "BMJ Connections Clinical Genetics and Genomics", "proofConfig-journal-id": "bmjccgg", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjccgg/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/bmjccgg/bmjccgg_proofConfig.xml", "name": "bmjccgg", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjccgg/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjconc/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjconc/jobTemplate.xml", "name": "bmjconc", "fullName": "BMJ Connections Oncology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjconc/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "jobTemplate": "/jmepb/jobTemplate.xml", "fullName": "JME Practical Bioethics", "proofConfig-journal-id": "jmepb", "styleTemplate": "/default/styleTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/jmepb/indesignAutoPageConfig.js", "pe": "<PERSON>", "proofConfig": "/config/jmepb/jmepb_proofConfig.xml", "name": "jmepb", "tableSetterCSS": "/css/table_setter/customers/bmj/jmepb/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publishing-email": "<EMAIL>", "publisher-email": "<EMAIL>", "eic-email": "<EMAIL>; <EMAIL>"}, "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjimm/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjimm/jobTemplate.xml", "name": "bmjimm", "fullName": "BMJ Immunology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjimm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}, {"workflowTemplate": "/default/workflowTemplate.xml", "tableSetterConfig": "/js/review_content/config/customer/bmj/bmjcimm/indesignAutoPageConfig.js", "pe": "<PERSON><PERSON>, <PERSON>", "jobTemplate": "/bmjcimm/jobTemplate.xml", "name": "bmjcimm", "fullName": "BMJ Connections Immunology", "tableSetterCSS": "/css/table_setter/customers/bmj/bmjcimm/table-config.css", "componentTemplate": "/config/default/_config_review_content.xml", "pm": "<PERSON><PERSON><PERSON>", "parameters": {"publisher-email": "<EMAIL>"}, "styleTemplate": "/default/styleTemplate.xml", "customCSS": "/css/review_content/customers/bmj/content-style.css"}]}}]