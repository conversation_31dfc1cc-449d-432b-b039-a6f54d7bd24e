/**
 * Main API Test Suite
 * 
 * This is the refactored version of api.test.js that uses shared utilities
 * and handles XML upload logic for multiple endpoint testing.
 */

const fs = require('fs');
const path = require('path');
const { 
    processRuntimeArguments, 
    readConfigFiles, 
    getFilesByCategory,
    initializeApp 
} = require('./test-utils');
const { setupXmlUpload } = require('./xml-upload-utils');
const { createTestSuite } = require('./test-execution-utils');
require('dotenv').config();

// Global variables
let app;
let server;

// Parse command line arguments
const args = processRuntimeArguments(process.argv);

// Determine which config files to run
let configFiles = [];

if (args.testFile) {
    // If testFile argument is provided, use that instead
    configFiles = [args.testFile];
} else if (args.testFiles) {
    // If testFiles argument is provided (comma-separated), use those
    configFiles = args.testFiles.split(',').map(file => file.trim());
} else if (args.category || args.categories) {
    // If category/categories argument is provided, filter files by category
    configFiles = getFilesByCategory(args.category, args.categories);
} else {
    // Read all configuration files from the API config directory
    configFiles = readConfigFiles();
}

// Create setup info to display
const setupInfo = {
    configFiles: configFiles.length,
    useApp: args.useApp || false,
    skipUpload: args.skipUpload || false,
    testFile: args.testFile || 'all',
    category: args.category || 'all'
};

console.log('Test Setup Configuration:', setupInfo);

// Test setup
beforeAll(async () => {
    try {
        // Initialize app if needed
        const appSetup = await initializeApp(args);
        app = appSetup.app;
        server = appSetup.server;
        
        // Upload XML files (only for the first endpoint when running multiple)
        const isMultipleEndpoints = configFiles.length > 1;
        await setupXmlUpload(args, server, true, isMultipleEndpoints);
        
    } catch (error) {
        console.error("Error in test setup:", error);
        throw error;
    }
});

// Process each configuration file and create test suites
console.log(`Found ${configFiles.length}}`);
for (const configFile of configFiles) {
    const filePath = path.join(process.env.SUPERTEST_API_CONFIG_PATH, configFile);
    
    let configJSON;
    try {
        const configJSONString = fs.readFileSync(filePath, 'utf8');
        configJSON = JSON.parse(configJSONString);
    } catch (e) {
        console.error(`Error parsing JSON from file ${filePath}:`, e);
        continue; // Skip this file if parsing fails
    }
    console.log(`Creating test suite for ${configFile}`);
    // Create a test suite for each config file
    createTestSuite(configFile, configJSON, args.useApp && server ? server : process.env.INSTANCE_URL);
}
