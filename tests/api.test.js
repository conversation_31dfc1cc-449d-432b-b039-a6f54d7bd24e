const request = require('supertest');
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs'); 
const path = require('path');
const libxmljs = require('libxmljs');
require('dotenv').config();

const Ajv = require('ajv');
const ajv = new Ajv({ 
  strict: true, 
  allowUnionTypes: true // 👈 allow union types in "type"
});

// Global variables
let app;
let server;

/**
 * Supertest Testing Framework - Command Line Options:
 * ------------------------------------------
 * --testFile=path/to/file.json    Specify test configuration file to run
 * --testFiles=file1.json,file2.json  Specify multiple test files (comma-separated)
 * --category=CategoryName         Run all tests in a specific category
 * --categories=Cat1,Cat2          Run tests in multiple categories (comma-separated)
 * --skipUpload=true               Skip XML upload
 * --useApp=true                   Use Express app instance directly instead of HTTP requests
 *
 * Examples:
 * ------------------------------------------
 * npm test -- tests/api.test.js
 * npm test -- tests/api.test.js --testFile=get/getarticledata.json
 * npm test -- tests/api.test.js --testFiles=get/getarticledata.json,post/authenticate.json
 * npm test -- tests/api.test.js --category=Authentication
 * npm test -- tests/api.test.js --categories=Authentication,Articles
 * npm test -- tests/api.test.js --skipUpload=true
 * npm test -- tests/api.test.js --useApp=true
 * 
 * Below are the mandatory params in .env file:
 * ------------------------------------------
 * INSTANCE_URL - The base URL of the API
 * APIKEY - The API key for authentication
 * SUPERTEST_API_CONFIG_PATH - The path to the api-config directory
 * SUPERTEST_BASE_PATH - The current working directory
 * SUPERTEST_XML_INPUT_PATH - These are the xml files that are uploaded to the test environment
 */

// Parse command line arguments
const args = processRuntimeArguments(process.argv);
// Get the app instance if useApp is true
if (args.useApp) {
    try {
        // Import the app from server.js
        app = require('../server.js');
        console.log('Using Express app instance');
    } catch (error) {
        console.error('Error importing Express app:', error.message);
    }
}

/**
 * Before running any tests:
 * 1. Start the server if useApp is true
 * 2. Upload all XML files if skipUpload is false
 * This ensures the test environment is properly set up with required test data
 */
beforeAll(async () => {
    try {
        // Step 1: Start the server if useApp is true
        if (args.useApp && app) {
            server = app
            // .listen(process.env.PORT, () => {
            //     // console.log(`Server connection key: ${server._connectionKey}`);
            // });
        }
        // Step 2: Upload XML files if skipUpload is false
        if (!args.skipUpload) {
            let uploadResult = await uploadXml();
            // console.log(uploadResult);
        }
    } catch (error) {
        // console.error("Error in test setup:", error);
        throw error; // Let Jest know the setup failed
    }
});

let configFiles = [];

if (args.testFile) {
    // If testFile argument is provided, use that instead
    configFiles = [args.testFile];
} else if (args.testFiles) {
    // If testFiles argument is provided (comma-separated), use those
    configFiles = args.testFiles.split(',').map(file => file.trim());
} else if (args.category || args.categories) {
    // If category/categories argument is provided, filter files by category
    configFiles = getFilesByCategory(args.category, args.categories);
} else {
    // Read all configuration files from the API config directory
    configFiles = readConfigFiles();
}

// Create setup info to display at the beginning of test execution
const setupInfo = `Preparing to run ${configFiles.length} test file(s): ${configFiles.join(', ')}
${args.skipUpload ? '⚠️ XML upload will be SKIPPED' : '✅ XML upload will be performed'}`;

// console.log(setupInfo);

// Process each configuration file and generate test cases
for (let configFile of configFiles) {
    let filePath = path.join(process.env.SUPERTEST_API_CONFIG_PATH, configFile);
    var configJSONString = fs.readFileSync(filePath, 'utf8');
    try{
    var configJSON = JSON.parse(configJSONString);
    }catch(e){
        console.error(`Error parsing JSON from file ${filePath}:`, e);
        continue; // Skip this file if parsing fails
    }
    
    // Create a test suite for each config file
    describe(`/ Validate ${configFile} API`,  () => {
        for (let iterator of configJSON) {
            it(`${iterator.message}`, async () => {
                if (iterator.encode == true) {
                    if (iterator.data.content) {
                        iterator.data.content = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
                        iterator.data.content = encodeURIComponent(iterator.data.content)
                    } else if (iterator.data.fileData) {
                        iterator.data.fileData = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.fileData, 'utf8');
                        iterator.data.fileData = encodeURIComponent(iterator.data.fileData)
                    }
                } else if (iterator.encode == false) {
                    if (/applyhouserules/g.test(iterator.url))
                        iterator.data.content = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
                    else
                        iterator.data.data = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
                }
                // iterator.url = iterator.url.replace('{APIKEY}', process.env.APIKEY)
                iterator = replacePlaceholders(iterator)
                let response;
                
                // Use app or INSTANCE_URL based on the parameter
                try{
                    response = await makeRequest(iterator, args.useApp && server ? server : process.env.INSTANCE_URL);
                }catch(e){
                    console.log(e,"Request Failed");
                }
                console.log({url:iterator.url, status: response.status, iterator}, response.text)
                const errors = [];
                try {
                    expect(response.status).toEqual(Number(iterator.status))
                } catch (e) {
                    errors.push(e);
                }
                if (iterator.expectedOutput) {
                    if (typeof iterator.expectedOutput === typeof response.text) {
                        try {
                            let responseText = response.text.replace(/[\n\r\t]+/g, '').replace(/<li id="[0-9]+"/g, '<li');
                            expect(responseText).toEqual(iterator.expectedOutput.replace(/[\n\r\t]+/g, ''))
                        } catch (e) {
                            errors.push(e);
                        }
                    }
                    else {
                        try {
                            expect(response.text).toBe(JSON.stringify(iterator.expectedOutput))
                        } catch (e) {
                            errors.push(e);
                        }
                    }
                } else if (iterator.fileLocation && iterator.fileLocation.includes('.html')) {
                    if (iterator.fileLocation.includes('applyhouserules') || iterator.fileLocation.includes('structurecontent')) {
                        response.text = response.text.replace(/Job ID: (.*?)(Table|\<res|\n|\s)/g, 'Job ID: Test-1234567890$2');
                        response.text = response.text.replace(/\<span class\=(\"|\&quot\;)query\-time(\"|\&quot\;)\>(.*?)\<\/span\>/g, '<span class="query-time">Nov 02 1996 (06:12)</span>');
                        response.text = response.text.replace(/\sdata\-date\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, ' data-date="Nov 02 1996 (06:12)"');
                        response.text = response.text.replace(/data\-time\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, 'data-time="Test-time"');
                        response.text = response.text.replace(/\sid\=(\"|\&quot\;)(\d.*?)(\"|\&quot\;)/g, ' id="Test-id"');
                        response.text = response.text.replace(/data\-cid\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, 'data-cid="Test-cid"');
                        response.text = response.text.replace(/\sdata-rid=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, ' data-rid="test-12345678"');
                        response.text = response.text.replace(/\stitle="Inserted(.*?)(\"|\&quot\;)/g, ' title="Inserted by cross-ref - test-date"');
                        response.text = response.text.replace(/\stitle="Inserted(.*?)(\"|\&quot\;)/g, ' data-rid="test-12345678"')
                        response.text = response.text.replace(/\stitle="Delete(.*?)(\"|\&quot\;)/g, ' title="Deleted by cross-ref - test-date"')
                    }
                    let responseText = response.text.replace(/[\n\r\t]+/g, '').replace(/ id="idm[0-9]+"/g, '');
                    if (iterator.outputfileLocation) {
                        await fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, responseText, {
                            mode: 0o755
                        });
                    }
                    let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8').replace(/[\n\r\t]+/g, '').replace(/ id="idm[0-9]+"/g, '');
                    try {
                        expect(file.trim()).toEqual(responseText.trim())
                    } catch (e) {
                        errors.push(e);
                    }
                } else if(iterator.fileLocation && iterator.fileLocation.includes('.json')){
                    response.text = response.text.replace(/\"took\":\d+\,/g, '');
                    let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8');
                    try {
                        expect(JSON.parse(file.trim())).toEqual(JSON.parse(response.text.trim()))
                    } catch (e) {
                        errors.push(e);
                    }
                } else if (iterator.fileLocation) {
                    if (iterator.outputfileLocation) {
                        let responseText = response.text.replace(/[\n\r\t]+/g, '').replace(/ id="idm[0-9]+"/g, '');
                        await fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, responseText, {
                            mode: 0o755
                        });
                    }
                    let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8').replace(/[\n\r\t]+/g, '').replace(/ id="idm[0-9]+"/g, '');
                    try {
                        expect(file.trim()).toEqual(responseText.trim())
                    } catch (e) {
                        errors.push(e);
                    }
                } else if (iterator.param) {
                    // Parse the response once to avoid repeated parsing
                        let parsedResponse = JSON.parse(response.text);
                        let dataToValidate;

                        // Extract the portion of the JSON to validate based on key path
                        if (iterator.param && iterator.param['$schema']) {
                            
                            if (iterator.param.key) {
                                const keyPath = Array.isArray(iterator.param.key) ? iterator.param.key : iterator.param.key.split('.');
                                delete iterator.param.key;
                                // Navigate through the object using the key path
                                console.log(keyPath)
                                console.log(parsedResponse)
                                parsedResponse = keyPath.reduce((obj, key) => obj?.[key], parsedResponse);

                            }
                            
                            const schema = iterator.param; // Use the param schema from your config
                            // Compile the schema
                            const validate = ajv.compile(schema);
                            // Validate the response
                            const valid = validate(parsedResponse);
                            console.log(valid, "Validating response with schema");
                            if (!valid) {
                                console.log('Validation errors:', validate.errors);
                            } else {
                                console.log('==========Response is valid!===========');
                            }
                            expect(valid).toBe(true);
                        } else if (iterator.param && iterator.param.key) {
                            // Handle dot notation for nested paths
                            const keyPath = Array.isArray(iterator.param.key) ? iterator.param.key : iterator.param.key.split('.');
                            
                            // Navigate through the object using the key path
                            dataToValidate = keyPath.reduce((obj, key) => obj?.[key], parsedResponse);
                        } else {
                            // If no key is specified, validate the entire response
                            dataToValidate = parsedResponse;
                        }

                        // Convert to array if not already (for consistent processing)
                        if (!Array.isArray(dataToValidate)) {
                            dataToValidate = [dataToValidate];
                        }

                        // Display the structure we're validating
                        // console.log('Validating JSON structure:', JSON.stringify(dataToValidate, null, 2));

                        // Check all specified keys exist in the response
                        if (iterator.param && iterator.param.parameters && iterator.param.parameters.length > 0) {
                            for (const obj of dataToValidate) {
                                for (const param of iterator.param.parameters) {
                                    // Simple key check
                                    if (typeof param.name === 'string' && !param.name.includes('.')) {
                                        // Check if the key exists in the object
                                        try {
                                            expect(obj).toHaveProperty(param.name);
                                            // Check the value if specified
                                            if (param.hasOwnProperty('value')) {
                                                try {
                                                    expect(obj[param.name]).toEqual(param.value);
                                                    // console.log(`✓ Value match for ${param.name}: ${param.value}`);
                                                } catch (e) {
                                                    // console.log(`✗ Value mismatch for ${param.name}. Expected: ${param.value}, Got: ${obj[param.name]}`);
                                                    errors.push(e);
                                                }
                                            }
                                        } catch (e) {
                                            // console.log(`✗ Missing key: ${param.name}`);
                                            errors.push(e);
                                        }
                                    } 
                                    // Nested key check with dot notation
                                    else {
                                        const keyPath = typeof param.name === 'string' ? param.name.split('.') : param.name;
                                        let current = obj;
                                        let valid = true;

                                        // Navigate through the nested structure
                                        for (let i = 0; i < keyPath.length; i++) {
                                            if (current === undefined || current === null) {
                                                valid = false;
                                                break;
                                            }
                                            current = current[keyPath[i]];
                                        }

                                        try {
                                            expect(valid && current !== undefined).toBe(true);
                                            // console.log(`✓ Found nested key: ${keyPath.join('.')}`);
                                            
                                            // Check the value if specified
                                            if (param.hasOwnProperty('value')) {
                                                try {
                                                    expect(current).toEqual(param.value);
                                                    // console.log(`✓ Value match for ${keyPath.join('.')}: ${param.value}`);
                                                } catch (e) {
                                                    // console.log(`✗ Value mismatch for ${keyPath.join('.')}. Expected: ${param.value}, Got: ${current}`);
                                                    errors.push(e);
                                                }
                                            }
                                        } catch (e) {
                                            // console.log(`✗ Missing nested key: ${keyPath.join('.')}`);
                                            errors.push(e);
                                        }
                                    }
                                }
                            }
                        }
                } else if (iterator.xpath_name) {
                    var doc = libxmljs.parseXml(response.text);
                    if (iterator.xpath_name.remove_notes) {
                        for (const element of iterator.xpath_name.remove_notes) {
                            let matchElements = doc.find(element);
                            for (matchElement of matchElements) {
                                matchElement.text("");
                            }
                        }
                    }
                    var article = doc.root().toString();
                    if (iterator.outputfileLocation) {
                        await fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, article, {
                            mode: 0o755
                        });
                    }
                    let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.xpath_name.fileLocation, 'utf8');
                    try {
                        expect(file.trim()).toEqual(article.trim())
                    } catch (e) {
                        errors.push(e);
                    }
                }
                if (errors.length > 0) {
                    throw new Error(errors.map(e => e.message).join('\n'));
                }
            })
            // break;
        }
    });
}

/**
 * Reads all JSON configuration files from the API config directory
 * @returns {Array} Array of configuration filenames
 */
function readConfigFiles() {
    if (fs.existsSync(process.env.SUPERTEST_API_CONFIG_PATH)) {
        return fs.readdirSync(process.env.SUPERTEST_API_CONFIG_PATH, { recursive: true })
            .filter(file => file.endsWith('.json'));
    } else {
        return [];
    }
}

/**
 * Gets test files filtered by category
 * @param {string} category - Single category to filter by
 * @param {string} categories - Comma-separated categories to filter by
 * @returns {Array} Array of configuration filenames that match the categories
 */
function getFilesByCategory(category, categories) {
    const targetCategories = [];

    if (category) {
        targetCategories.push(category);
    }

    if (categories) {
        targetCategories.push(...categories.split(',').map(cat => cat.trim()));
    }

    if (targetCategories.length === 0) {
        return [];
    }

    const allFiles = readConfigFiles();
    const matchingFiles = [];

    for (const configFile of allFiles) {
        try {
            const filePath = path.join(process.env.SUPERTEST_API_CONFIG_PATH, configFile);
            const configJSONString = fs.readFileSync(filePath, 'utf8');
            const configJSON = JSON.parse(configJSONString);

            // Check if any test in this file matches the target categories
            const hasMatchingCategory = configJSON.some(test =>
                test.category && targetCategories.includes(test.category)
            );

            if (hasMatchingCategory) {
                matchingFiles.push(configFile);
            }
        } catch (error) {
            console.warn(`Warning: Could not parse ${configFile} for category filtering:`, error.message);
            // Continue with other files
        }
    }

    // console.log(`Found ${matchingFiles.length} files matching categories: ${targetCategories.join(', ')}`);
    return matchingFiles;
}

/**
 * Uploads XML files to the test environment
 * @returns {Promise} Resolves when all uploads are complete
 */
async function uploadXml() {
    let doiArray = [];
    if (!fs.existsSync(process.env.SUPERTEST_XML_INPUT_PATH)) {
        // console.log("Input directory not found");
        return 'No files to upload';
    }

    const xmlFiles = fs.readdirSync(process.env.SUPERTEST_XML_INPUT_PATH, { recursive: true, withFileTypes: true })
        .filter(item => item.isFile() && item.name.endsWith('.xml'))
        .map(file => {
            // Get the relative path from XML_INPUT_PATH
            const relativePath = path.relative(process.env.SUPERTEST_XML_INPUT_PATH, file.path);
            const parts = relativePath.split(path.sep);
            
            return {
                path: path.join(file.path, file.name),
                customer: parts[0],                    // First directory after XML_INPUT_PATH
                project: parts[1],                     // Second directory
                doi: parts[2]                          // Third directory (DOI)
            };
        })
        .filter(file => file.customer && file.project && file.doi); // Ensure all parts exist

    // console.log(`Found ${xmlFiles.length} XML files to upload`);
    
    // Use a for loop instead of Promise.all for easier debugging
    for (let i = 0; i < xmlFiles.length; i++) {
        const iterator = xmlFiles[i];
        doiArray.push(iterator.doi);
        
        try {
            // console.log(`Uploading file ${i+1}/${xmlFiles.length}: ${iterator.path}`);
            // console.log(`Customer: ${iterator.customer}, Project: ${iterator.project}, DOI: ${iterator.doi}`);
            // Use SuperTest with the app instance
            const form = new FormData();
            form.append('xmlFile', fs.createReadStream(iterator.path));
            form.append('customer', iterator.customer);
            form.append('project', iterator.project);
            form.append('comment', iterator.doi);
            // Use different upload methods based on whether we're using the app or HTTP
            if (args.useApp && server) { 
                // Use a synchronous approach with await for easier debugging
                const response = await new Promise((resolve, reject) => {
                    request(server)
                        .post(`/api/uploadxml`)
                        .attach('xmlFile', iterator.path)
                        .field('customer', iterator.customer)
                        .field('project', iterator.project)
                        .field('comment', iterator.doi)
                        .set(form.getHeaders())
                        .set('x-kd-apikey', process.env.APIKEY)
                        .end((err, res) => {
                            if (err) {
                                // console.error(`Error uploading file: ${err.message}`);
                                reject(err);
                            } else {
                                // console.log(`Upload successful, status: ${res.status}`);
                                resolve(res);
                            }
                        });
                });
                
                // console.log(`Upload response status: ${response.status}`);
                if (response.status !== 200) {
                    // console.error(`Error response: ${response.text}`);
                }
            } else {
                // Use axios for HTTP requests              
                const response = await axios.post(
                    `${process.env.INSTANCE_URL}/api/uploadxml`, 
                    form, 
                    { headers: {...form.getHeaders(), 'x-kd-apikey': process.env.APIKEY}}
                );
                
                // console.log(`Upload response status: ${response.status}`);
            }
            
            // console.log(`Successfully uploaded file ${i+1}/${xmlFiles.length}`);
        } catch (error) {
            // console.error(`Failed to upload file ${i+1}/${xmlFiles.length}: ${iterator.path}`);
            // console.error(`Error: ${error.message}`);
            if (error.response) {
                // console.error(`Response status: ${error.response.status}`);
                // console.error(`Response data: ${JSON.stringify(error.response.data)}`);
            }
            throw new Error(`Upload error for file ${iterator.path}: ${error.message}`);
        }
    }
    
    return 'All uploads successful';
}

/**
 * Replaces placeholders in an object with corresponding values available in environment variables
 * @param {Object} obj - The object to replace placeholders in
 * @returns {Object} The object with placeholders replaced
 */
function replacePlaceholders(obj) {
    if (typeof obj === 'string') {
      // Replace all {KEY} patterns in the string
      return obj.replace(/\{(\w+)\}/g, (_, key) => process.env[key] ?? `{${key}}`);
    } else if (Array.isArray(obj)) {
      return obj.map(item => replacePlaceholders(item));
    } else if (obj && typeof obj === 'object') {
      return Object.fromEntries(
        Object.entries(obj).map(([k, v]) => [k, replacePlaceholders(v)])
      );
    }
    return obj;
}

/**
 * Processes runtime arguments from the command line
 * @returns {Object} An object containing the parsed arguments
 */
function processRuntimeArguments(params) {
    let args = {};
    params.slice(2).forEach(arg => {
        const [key, value] = arg.split('=');
        if (key.startsWith('--')) {
            const cleanKey = key.replace(/^--/, '');
            args[cleanKey] = value === 'true' ? true :
                value === 'false' ? false :
                    value || true; // Handle flags without values
        }
    });
    return args;
}

/**
 * Makes an HTTP request with supertest based on test configuration
 * @param {Object} testConfig - Configuration for the test
 * @param {string} baseUrl - Base URL or server instance to test against
 * @returns {Promise<Object>} - The response from the request
 */
async function makeRequest(testConfig, baseUrl) {
    const maxRetries = 1;//process.env.MAX_RETRIES || 3;
    const retryDelay = process.env.RETRY_DELAY || 2000;
    const method = (testConfig.method || 'get').toLowerCase();
    
    // Sleep function for delay between retries
    const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    
    let attempt = 0;
    while (attempt < maxRetries) {
        attempt++;
        try {
            // Create request based on HTTP method
            let req;
            switch (method) {
                case 'get':
                    req = request(baseUrl).get(testConfig.url);
                    break;
                case 'post':
                    req = request(baseUrl).post(testConfig.url);
                    break;
                case 'put':
                    req = request(baseUrl).put(testConfig.url);
                    break;
                case 'delete':
                    req = request(baseUrl).delete(testConfig.url);
                    break;
                default:
                    req = request(baseUrl).get(testConfig.url);
            }
            req.set('x-kd-apikey', process.env.APIKEY);
            // Add headers if specified
            if (testConfig.headers) {
                Object.entries(testConfig.headers).forEach(([key, value]) => {
                    req.set(key, value);
                });
            }
            // Handle FormData if specified
            if (testConfig.FormData === true) {
                // Handle the legacy format (data.content and other specific fields)
                if (testConfig.data) {
                    // Handle file upload if content is specified
                    if (testConfig.data.content) {
                        const filePath = process.env.SUPERTEST_BASE_PATH + testConfig.data.content;
                        // Use the specified fieldName if available, otherwise default to 'xmlfile'
                        const fieldName = testConfig.fieldName || 'xmlfile';
                        req.attach(fieldName, filePath);
                    }
                    // Add all other data fields dynamically
                    Object.entries(testConfig.data).forEach(([field, value]) => {
                        // Skip 'content' as it's already handled
                        if (field !== 'content') {
                            req.field(field, value);
                        }
                    });
                }
                // Process formFields if present (newer, more explicit format)
                if (testConfig.formFields) {
                    Object.entries(testConfig.formFields).forEach(([field, value]) => {
                        if (value && typeof value === 'object' && value.type === 'file') {
                            // Handle file field
                            const filePath = process.env.SUPERTEST_BASE_PATH + value.path;
                            req.attach(field, filePath);
                        } else {
                            // Handle regular field
                            req.field(field, value);
                        }
                    });
                }
            } else if (testConfig.data) {
                // For non-GET requests with data, send as JSON payload
                req.send(testConfig.data);
            }
            
            // Execute the request
            const response = await req;
            
            // If we get a 502 Bad Gateway and have retries left, retry after delay
            if (response.status === 502 && attempt <= maxRetries) {
                // console.log(`Received 502 response (attempt ${attempt}/${maxRetries}), retrying in ${retryDelay}ms...`);
                await sleep(retryDelay);
                continue;
            }
            return response;
        } catch (err) {
            if (attempt > maxRetries) throw err;
            // console.log(`Request ${testConfig.url} failed (attempt ${attempt}/${maxRetries}): ${err.message}, retrying in ${retryDelay}ms...`);
            await sleep(retryDelay);
        }
    }
}


// Close the server after tests complete
afterAll(async () => {
    if (server) {
        // Force-close any connections
        await new Promise((resolve, reject) => {
            server.close((err) => {
                if (err) {
                    // console.error('Error closing server:', err);
                    reject(err);
                }
                // console.log('Test server closed');
                resolve();
            });
        });
    }
});