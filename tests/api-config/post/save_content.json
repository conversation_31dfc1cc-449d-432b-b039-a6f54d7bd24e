[{"message": "Save content", "url": "/api/save_content", "FormData": true, "headers": {"referer": "{INSTANCE_URL}"}, "formFields": {"file": {"type": "file", "path": "/_testFiles/inputfiles/post/save_content/section_head_content.xml"}, "customer": "rs", "project": "rsos", "doi": "rsos-2024-10201"}, "method": "post", "status": "200", "params": true, "param": {"parameters": [{"name": "code", "type": "string"}, {"name": "message", "type": "array"}]}, "category": "Articles", "public": false, "hidden": false}, {"message": "Delete content in a paragraph", "url": "/api/save_content", "FormData": true, "headers": {"referer": "{INSTANCE_URL}"}, "formFields": {"file": {"type": "file", "path": "/_testFiles/inputfiles/post/save_content/delete_section_paragraph_content.xml"}, "customer": "rs", "project": "rsos", "doi": "rsos-2024-10201", "action": "focusout-key_execute"}, "method": "post", "status": "200", "params": true, "param": {"parameters": [{"name": "code", "type": "string"}, {"name": "message", "type": "array"}]}, "category": "Articles", "public": false, "hidden": false}, {"message": "Delete equation in a paragraph", "url": "/api/save_content", "FormData": true, "headers": {"referer": "{INSTANCE_URL}"}, "formFields": {"file": {"type": "file", "path": "/_testFiles/inputfiles/post/save_content/delete_equation.xml"}, "customer": "rs", "project": "rsos", "doi": "rsos-2024-10201", "action": "generate-action-queries_execute"}, "method": "post", "status": "200", "params": true, "param": {"parameters": [{"name": "code", "type": "string"}, {"name": "message", "type": "array"}]}, "category": "Articles", "public": false, "hidden": false}, {"message": "Insert paragraph before section head 2.2", "url": "/api/save_content", "FormData": true, "headers": {"referer": "{INSTANCE_URL}"}, "formFields": {"file": {"type": "file", "path": "/_testFiles/inputfiles/post/save_content/insert_paragraph_before_section_head.xml"}, "customer": "rs", "project": "rsos", "doi": "rsos-2024-10201", "action": "keyup-event_execute"}, "method": "post", "status": "200", "params": true, "param": {"parameters": [{"name": "code", "type": "string"}, {"name": "message", "type": "array"}]}, "category": "Articles", "public": false, "hidden": false}]