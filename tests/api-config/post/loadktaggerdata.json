[{"message": "Get empty response as the expected payload is not specified", "status": "400", "url": "/api/loadktaggerdata", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"error": "one or more of mandatory inputs has not been provided, please check if you have provided project, doi,presubmijobid and/or customer details"}, "category": "System", "public": false, "hidden": false}, {"message": "POST loadktaggerdata with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/loadktaggerdata/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"error": "one or more of mandatory inputs has not been provided, please check if you have provided project, doi,presubmijobid and/or customer details"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]