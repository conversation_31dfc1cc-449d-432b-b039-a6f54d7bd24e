[{"message": "content structure the article and save it", "status": "204", "url": "/api/contentstructuring/", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "", "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error code when authentication is not given", "status": "401", "url": "/api/contentstructuring", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST contentstructuring with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 204)", "status": "204", "url": "/api/contentstructuring/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June", "notesData": {"content": [{"content": "\n MESSAGE: undefined", "created": "2025-07-24 10:43:23"}], "doi": "bjo-test-27June", "customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "noteType": "workflow", "logging": false}}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]