[{"message": "Missing required parameters: event, eventParam", "status": 400, "url": "/api/triggerevent", "data": {"customer": "ppl", "project": "bsr", "doi": "bsr-2025-50609"}, "params": true, "param": {"parameters": [{"name": "status", "value": 400}, {"name": "message", "value": "Missing required parameters: event, eventParam, customer, project, doi"}]}, "method": "post", "category": "System"}, {"message": "Missing required parameters: customer, project, doi", "status": 400, "url": "/api/triggerevent", "data": {"event": "STAGE_MOVEMENT", "eventParam": "Article in review"}, "params": true, "param": {"parameters": [{"name": "status", "value": 400}, {"name": "message", "value": "Missing required parameters: event, eventParam, customer, project, doi"}]}, "method": "post", "category": "System"}, {"message": "Missing all required fields", "status": 400, "url": "/api/triggerevent", "data": {}, "params": true, "param": {"parameters": [{"name": "status", "value": 400}, {"name": "message", "value": "Missing required parameters: event, eventParam, customer, project, doi"}]}, "method": "post", "category": "System"}, {"message": "Failed to fetch article data from Elastic.", "status": 400, "url": "/api/triggerevent", "data": {"event": "STAGE_MOVEMENT", "eventParam": "Author resubmission", "data": {"customer": "ppl", "project": "bsr", "doi": "bssr-2025-50609"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 400}, {"name": "message", "value": "Failed to fetch article data from Elastic."}]}, "method": "post", "category": "System"}, {"message": "No matching event configuration", "status": 200, "url": "/api/triggerevent", "data": {"event": "STAGE_MOVEMENT", "eventParam": "Author resubmission", "data": {"customer": "ppl_12345", "project": "bsr", "doi": "bsr-2025-50609"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 200}, {"name": "message", "value": "No matching event configuration found"}]}, "method": "post", "category": "System"}, {"message": "Filter conditions not met for event", "status": 200, "url": "/api/triggerevent", "data": {"event": "STAGE_MOVEMENT", "eventParam": "Associate editor review", "data": {"customer": "ppl", "project": "bsr", "doi": "bsr-2025-50609"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 200}, {"name": "message", "value": "No actions were triggered successfully"}]}, "method": "post", "category": "System"}, {"message": "Should execute triggerEmail Action", "status": 200, "url": "/api/triggerevent", "data": {"event": "INVITE", "eventParam": "REVIEWER", "data": {"currStage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "customer": "ppl", "doi": "bsr-2025-50609", "email": "<EMAIL>", "id": "bsr-2025-50609", "message": "Invitation sent", "name": "<PERSON>", "project": "bsr", "recipientName": "<PERSON>", "role": "reviewer", "source-assignment": "Manual", "sourceAssignment": "Manual", "stageName": "Associate editor review", "stagename": "reviewercheck", "status": "waiting", "title": "Prediction of pathological response after neoadjuvant chemotherapy using baseline FDG PET heterogeneity features in breast cancer", "to": "<EMAIL>", "version": "R2"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 200}, {"name": "message", "value": "All events processed successfully"}]}, "method": "post", "category": "System"}, {"message": "Should execute triggerWebhook Action", "status": 200, "url": "/api/triggerevent", "data": {"event": "STAGE_MOVEMENT", "eventParam": "Article in review", "data": {"customer": "ppl", "doi": "bsr-2025-50609", "project": "bsr"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 200}, {"name": "message", "value": "All events processed successfully"}]}, "method": "post", "category": "System"}, {"message": "Should execute triggerRescheduleEmail Action", "status": 200, "url": "/api/triggerevent", "data": {"event": "UPDATE_DUE_DATE", "eventParam": "*", "data": {"stageName": "Associate editor decision", "articleType": "Editorial", "article-version": "R2", "project": "bsr", "id": "bsr-2025-50609", "doi": "bsr-2025-50609", "customer": "ppl", "email": "<EMAIL>", "version": "R2", "updateDueDate": "true", "role": "reviewer", "name": "<PERSON>", "message": "In progress", "status": "accepted", "source-assignment": "Manual", "to": "<EMAIL>", "stagename": "reviewercheck", "recipientName": "<PERSON>"}, "meta": {"siteUrl": "{INSTANCE_URL}"}}, "params": true, "param": {"parameters": [{"name": "status", "value": 200}, {"name": "message", "value": "All events processed successfully"}]}, "method": "post", "category": "System"}]