[{"message": "Return success message if we passed the customer", "status": "200", "url": "/api/getringgoldid", "data": {"keyword": "Odense University Hospital", "type": "getRinggoldInfo"}, "params": true, "param": {"parameters": [{"name": "name", "type": "string"}, {"name": "city", "type": "string"}, {"name": "state", "type": "string"}, {"name": "country", "type": "string"}]}, "method": "post", "category": "Integration"}, {"message": "POST getringgoldid with type=getRinggoldInfo", "status": "200", "url": "/api/getringgoldid/", "method": "post", "data": {"keyword": "Odense University Hospital", "type": "getRinggoldInfo"}, "expectedOutput": [{"name": "Odense Universitetshospital", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 11286}, {"name": "Odense Universitetshospital Klinisk Udvikling", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 573149}, {"name": "Odense Universitetshospital Sygehusenheden Nyborg", "city": "Nyborg", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 702045}, {"name": "Odense Universitetshospital Ojenafdeling E", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 573158}, {"name": "Odense Universitetshospital Radiologisk Afdeling", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 639110}, {"name": "Odense Universitetshospital Ortopaedkirurgisk Afdeling", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 542123}, {"name": "Odense Universitetshospital Onkologisk Afdeling R", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 742721}, {"name": "Odense Universitetshospital Kirurgisk Afdeling A", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 573154}, {"name": "Odense Universitetshospital Endokrinologisk Afdeling M", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 548568}, {"name": "Odense Universitetshospital Klinisk Immunologisk Afdeling", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 646816}, {"name": "Odense Universitetshospital B - Hjertemedicinsk Afdeling", "city": "Odense C", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 676990}, {"name": "Odense Universitetshospital Afdeling for Klinisk Patologi", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 573155}, {"name": "GCP-enheden ved Odense Universitetshospital", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 307867}, {"name": "Odense Universitetshospital Center for Klinisk Epidemiologi", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 573151}, {"name": "Odense Universitetshospital T Hjerte- Lunge- og Karkirurgisk Afdeling", "city": "Odense", "state": "Region Syddanmark", "country": "Denmark", "countryCode": "DK", "ringgoldId": 683567}], "headers": {"content-type": "application/json"}, "category": "Integration"}]