[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/createblankdoc", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "POST createblankdoc with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 204)", "status": "204", "url": "/api/createblankdoc/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": "400", "message": "Project List not found in database"}, "step": "add stage"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]