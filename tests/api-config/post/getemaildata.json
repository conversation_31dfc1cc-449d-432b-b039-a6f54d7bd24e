[{"message": "get email data for bmj customer on Sep 11", "status": "200", "url": "/api/getemaildata", "param": {"parameters": [{"name": "hits", "type": "object"}]}, "data": {"customer": "bmj", "from": "2022-09-11", "to": "2022-09-11", "subject": "*", "fromSize": 10, "size": 5}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "Return error when customer name is not provided", "status": "200", "url": "/api/getemaildata", "data": {"from": "2019-09-11", "to": "2019-09-11", "subject": "*"}, "expectedOutput": "Customer is empty", "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "Return error when customer name is empty", "status": "200", "url": "/api/getemaildata", "data": {"customer": "", "from": "2019-09-11", "to": "2019-09-11", "subject": "*"}, "expectedOutput": "Customer is empty", "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST getemaildata with customer=bmj", "status": "200", "url": "/api/getemaildata/", "method": "post", "data": {"customer": "bmj", "from": "2022-09-11", "to": "2022-09-11", "subject": "*", "fromSize": 10, "size": 5, "index": "dev-emails", "table": "email", "urlToPost": "getEmails"}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST getemaildata", "status": "200", "url": "/api/getemaildata/", "method": "post", "data": {"from": "2019-09-11", "to": "2019-09-11", "subject": "*", "index": "dev-emails", "table": "email", "urlToPost": "getEmails"}, "expectedOutput": "Customer is empty", "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST getemaildata", "status": "200", "url": "/api/getemaildata/", "method": "post", "data": {"customer": "", "from": "2019-09-11", "to": "2019-09-11", "subject": "*", "index": "dev-emails", "table": "email", "urlToPost": "getEmails"}, "expectedOutput": "Customer is empty", "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST getemaildata with customer=bmj, project=*", "status": "200", "url": "/api/getemaildata/", "method": "post", "data": {"customer": "bmj", "from": "2022-09-11", "to": "2022-09-11", "subject": "*", "fromSize": 10, "size": 5, "index": "dev-emails", "table": "email", "urlToPost": "getEmails", "project": "*", "excludeObjects": "", "includeFields": "*"}, "expectedOutput": {"took": 42, "timed_out": false, "_shards": {"total": 1, "successful": 1, "skipped": 0, "failed": 0}, "hits": {"total": {"value": 0, "relation": "eq"}, "max_score": null, "hits": []}}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]