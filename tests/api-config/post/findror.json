[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/findror", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": [], "category": "Integration", "public": false, "hidden": false}, {"message": "POST findror with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/findror/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": [], "headers": {"content-type": "application/json"}, "category": "Integration", "public": false, "hidden": false}]