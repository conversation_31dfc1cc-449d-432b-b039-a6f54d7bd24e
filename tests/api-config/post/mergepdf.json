[{"message": "Get empty response as the payload is not specified", "status": "200", "url": "/api/mergepdf", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "expectedOutput": "Making temp dir...\nDownloading files...{}", "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST mergepdf with customer=bmj, project=bjophthalmol", "status": "200", "url": "/api/mergepdf/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]