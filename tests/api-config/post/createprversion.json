[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/createprversion", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST createprversion with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/createprversion/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June", "stage": "structurecontent"}, "expectedOutput": {"code": "ECONNREFUSED"}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]