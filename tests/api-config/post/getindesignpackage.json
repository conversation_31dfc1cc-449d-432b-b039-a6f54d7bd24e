[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getindesignpackage", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "method": "post", "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer id, project id, jobID) is/are not provided."}}, "category": "System", "public": false, "hidden": false}]