[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/generatekey", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST generatekey with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/generatekey/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 400, "message": "Internal error"}, "message": [{"code": 400, "message": "Provide content to update"}]}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]