[{"message": "Return error message when we not passed the index", "status": "400", "url": "/api/getelasticdata", "data": {"table": "article", "data": [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, {"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}]], "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "Return error message when we not passed the table", "status": "400", "url": "/api/getelasticdata", "data": {"index": "dev", "data": [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, {"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}]], "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "Return error message when we not passed the data", "status": "400", "url": "/api/getelasticdata", "data": {"table": "article", "index": "dev", "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST getelasticdata (status 400)", "status": "400", "url": "/api/getelasticdata/", "method": "post", "data": {"table": "article", "data": [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, {"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}]], "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST getelasticdata (status 400)", "status": "400", "url": "/api/getelasticdata/", "method": "post", "data": {"index": "dev", "data": [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, [{"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}, {"name": "corresp-info.name.email.keyword", "value": "<EMAIL>"}]], "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST getelasticdata (status 400)", "status": "400", "url": "/api/getelasticdata/", "method": "post", "data": {"table": "article", "index": "dev", "apiKey": "0e78ffad-4705-414b-836e-b6893ab3abc6"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]