[{"message": "Restore the version for article bmjdrc-2018-000550", "status": "200", "url": "/api/restoreversion", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "versions": "snapshots/7_publishercheck/bmjdrc-2018-000550_2019_06_21_06_44_16_UTC_Automation_page_load.xml"}, "expectedOutput": "ERROR : file read error in AWS", "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error message when authentication not given", "status": "401", "url": "/api/restoreversion", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "versions": "snapshots/7_publishercheck/bmjdrc-2018-000550_2019_06_21_06_44_16_UTC_Automation_page_load.xml"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error message when customer is not given", "status": "200", "url": "/api/restoreversion", "data": {"project": "bmjdrc", "doi": "bmjdrc-2018-000550", "versions": "snapshots/7_publishercheck/bmjdrc-2018-000550_2019_06_21_06_44_16_UTC_Automation_page_load.xml"}, "expectedOutput": "ERROR : file read error in AWS", "method": "post", "category": "Workflow", "public": false, "hidden": false}]