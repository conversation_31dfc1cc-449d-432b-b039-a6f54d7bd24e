[{"message": "Return error when customer name is not given", "status": "204", "url": "/api/createversion/", "data": {"project": "bmjdrc", "doi": "bmjdrc-2018-000550"}, "expectedOutput": "", "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error when project name is not given", "status": "204", "url": "/api/createversion/", "data": {"customer": "bmj", "doi": "bmjdrc-2018-000550"}, "expectedOutput": "", "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error when doi name is not given", "status": "204", "url": "/api/createversion/", "data": {"customer": "bmj", "project": "bmjdrc"}, "expectedOutput": "", "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error when authentication is not given", "status": "401", "url": "/api/createversion/", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}]