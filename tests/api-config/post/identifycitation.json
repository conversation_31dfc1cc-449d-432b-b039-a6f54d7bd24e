[{"message": "Get empty response when expected payload is not specified", "status": "200", "url": "/api/identifycitation", "data": {"customer": "bmj"}, "method": "post", "category": "References", "public": false, "hidden": false}, {"message": "POST identifycitation with customer=bmj", "status": "200", "url": "/api/identifycitation/", "method": "post", "data": {"customer": "bmj"}, "headers": {"content-type": "application/json"}, "category": "References", "public": false, "hidden": false}]