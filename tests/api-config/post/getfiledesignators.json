[{"message": "Return error Code when project Name is not there", "status": "200", "url": "/api/getfiledesignators", "data": {"customer": "bmj", "doi": "bmjdrc-2018-000550", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error Code when DOI is not there", "status": "200", "url": "/api/getfiledesignators", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error Code when customer is not there", "status": "200", "url": "/api/getfiledesignators", "data": {"doi": "bmjdrc-2018-000550", "project": "bmjdrc", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error message when authentication not given", "status": "401", "url": "/api/getfiledesignators", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "POST getfiledesignators with customer=bmj, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/getfiledesignators/", "method": "post", "data": {"customer": "bmj", "doi": "bmjdrc-2018-000550", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}, {"message": "POST getfiledesignators with customer=bmj, project=annrheumdis", "status": "200", "url": "/api/getfiledesignators/", "method": "post", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}, {"message": "POST getfiledesignators with project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/getfiledesignators/", "method": "post", "data": {"doi": "bmjdrc-2018-000550", "project": "bmjdrc", "process": "get"}, "expectedOutput": {"status": 400, "content": "ERROR : file read error in AWS"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]