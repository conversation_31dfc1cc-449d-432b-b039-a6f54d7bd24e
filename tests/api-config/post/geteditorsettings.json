[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/geteditorsettings", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Configuration", "public": false, "hidden": false}, {"message": "POST geteditorsettings", "status": "200", "url": "/api/geteditorsettings/", "method": "post", "data": {"lang": "en-GB", "settingsList": ["messages"]}, "expectedOutput": {"messages": {"m00001": "Please select a Journal name", "m00002": "Please select an Article type", "m00003": "Please enter the Article title", "m00004": "Please enter the Article summary", "m00005": "Please enter at least one keyword", "m00006": "Please enter at least one speciality/subject area", "m00007": "Please enter a Corresponding author", "m00008": "Please enter a First name", "m00009": "Please enter a Last name", "m00010": "Please enter a valid email", "m00011": "Please enter a Correspondence address", "m00012": "Please enter at least one affiliation", "m00013": "Please upload a file", "m00014": "Please select the document type", "m00015": "It looks like you have uploaded the same file twice", "m00016": "Please tick the checkboxes to agree to the declarations", "m00017": "Unless otherwise indicated, all fields are required", "m00018": "Mandatory fields", "m00019": "Please select an Article language", "m00020": "Please add a detailed review summary for the author", "m00021": "Hide this file from Reviewers", "m00022": "Please check the submission instructions here: <a target='_blank' href='https://www.medwave.cl/link.cgi/instructions-guidelines.act'>LINK</a>", "m00023": "I have reviewed the submission and I would like to:", "m00024": "Sign off on this article via email", "m00025": "Select the files to resubmit your manuscript", "m00026": "Please upload Manuscript file", "m00027": "Please upload a Response to Review file", "m00028": "Sign off on this article via email", "m00029": "Please upload response to Reviewer", "m00030": "Thank you for accepting the request to review!", "m00031": "Click on the below link to access the article.", "m00032": "We have also emailed you a link to the article.", "m00033": "You have signed-off on this article.", "m00034": "In case of any queries, please contact", "m00035": "You have successfully submitted the article!", "m00036": "I understand and declare that I am informed that Medwave is an open-access journal and, as such, must apply submission and publication fees in order to finance the journal’s operation. By submitting my manuscript, I agree to such charges (outlined in “About”), and I declare that I will pay them promptly. If I consider that my manuscript may be eligible for discounts or exemptions, I have included the corresponding request in the cover letter with the due justification.", "m00037": "I confirm that I understand that Medwave requires authors to declare any competing interests, whether financial or non-financial, in relation to the submitted work.If there are potential competing interests, I, as the corresponding author and on behalf of all the co-authors, take responsibility for declaring any potential competing interests and have included the details in the main document in the Notes section before the references.If there are no potential competing interests, I, as the corresponding author and on behalf of all the co-authors, declare that neither I nor any of the co-authors present competing interests in relation to the submitted work, nor any other interests that could be perceived as a potential influence on the results or discussion reported in this manuscript. I have included this statement in the main document in the Notes section before references.", "m00038": "In my capacity as the corresponding author, I declare that this manuscript has not been previously published in another medium or journal (digital or printed), neither in this format nor in a similar format, nor parts of its contents. I declare that this manuscript has not been sent for peer review to another journal or medium.", "m00039": "I declare that the authors listed in the submission to Medwave are all effectively co-authors of the manuscript and have contributed substantively to it. In the manuscript, I have included the roles and contributions of each author in the Notes section before references, following Journal guidelines.", "m00040": "In my capacity as the corresponding author, I declare that this manuscript is an honest, accurate, and transparent transcript of the study being reported; that no significant aspects of the study have been omitted; and that any discrepancies between the study results and those expected (if relevant) have been reported and explained.", "m00041": "On behalf of the co-authors and in my capacity as the corresponding author, I grant Medwave Estudios Limitada an indefinite international license for all forms, formats, and media, currently known, or to be created in the future, which includes the following uses regarding this contribution: a) publication, reproduction, distribution, demonstration and storage, b) translation into other languages, creation of adaptations, republications (reprints), inclusion in collections, creation of summaries, extracts or abstracts, c) creation of any other derivative work based on the contribution, d) the exploitation of all subsidiary rights, e) the inclusion of electronic links from the contribution to third party material, no matter where they are located; and f) granting of a license to third parties to carry out any of the actions as mentioned above.", "m00042": "I declare that the text adheres to the bibliographic and style requirements indicated in the editorial policies and the author guidelines section.", "m00043": "I declare that the ethical aspects (ethics committee approval letter for original research articles or statement of informed consent/assent for case reports) have been fully reported in the manuscript in the Methods section or the Notes section before the references.", "m00044": "You have successfully declined this article.", "m00045": "accepts only the following file formats ", "m00046": "Please save the existing author.", "m00047": "Please save before adding a new author.", "m00048": "Please save before moving to next page.", "m00049": "You have assigned this article.", "m00050": "Reviewer already exists", "m00051": "Please provide a country", "m00052": "Please make sure all Reviews are done before you assign the article to an Associate editor.", "m00053": "Please upload a file", "m00054": "You have successfully signed off the article to the next stage.", "m00055": "Please close the browser to exit the system.", "m00056": "Please enter an institution / organisation", "m00057": "Please enter a city", "m00058": "Please enter a country", "m00059": "Please enter an address", "m00060": "Special characters are not allowed", "m00061": "Please enter the captcha", "m00062": "Invalid cap<PERSON>a", "m00063": "You have successfully resubmitted your manuscript", "m00064": "Please enter valid email IDs separated by comma(,) or semicolon(;)", "m00065": "Log out from kriyadocs", "m00066": "You are logged in kriyadocs. Please log out to continue with the submission form.", "m00067": "<PERSON><PERSON><PERSON> from kriyadocs", "m00068": "Comments from the editor", "m00069": "The following details were not detected or tagged:<br/>[Missing tag]<br/><br/>Please select & tag if available on the manuscript.<br/>Else, you can send it back to the author to add & resubmit.<br/><br/>Note: You can also skip this now and add it after acceptance.", "m00070": "Please add a comment before signing off", "m00071": "Skip and proceed", "m00073": "This file format is not supported for viewing in the editor. Please download the file.", "m00074": "File deleted successfully", "m00075": "Hide all Supplmentary files from reviewers", "m00076": "Download all supplementary files", "m00077": "Hide all Figures from reviewers", "m00078": "Download figures as single PDF", "m00079": "Hide all Trackchanges from reviewers", "m00080": "Download all Trackchanges", "m00081": "Hide all Tables from reviewers", "m00082": "Download all Tables", "m00083": "Hide all Response to Reviewer files from reviewers", "m00084": "Download all Response to Reviewer files", "m00085": "Hide all Reporting guideline files from reviewers", "m00086": "Download all Reporting guideline files", "m00087": "Hide all Cover letter from reviewers", "m00088": "Hide all Headshot from reviewers", "m00089": "Download all Headshot", "m00090": "Hide all Licence to publish from reviewers", "m00091": "Download all Licence to publish", "m00092": "Hide cover letter from reviewers", "m00093": "Download cover letter", "m00094": "Hide Other document files from reviewers", "m00095": "Download Other document files", "m00096": "Hide figures from reviewers", "m00097": "Download figures as single PDF", "m00098": "Hide marked & revised manuscript from reviewers", "m00099": "Download marked & revised manuscript", "m00100": "Hide tables from reviewers", "m00101": "Download tables", "m00102": "Hide point-by-point response from reviewers", "m00103": "Download point-by-point response files", "m00104": "Hide reporting guideline files from reviewers", "m00105": "Download reporting guideline files", "m00106": "Hide cover letter from reviewers", "m00107": "<PERSON>de Headshot from reviewers", "m00108": "Download Headshot", "m00109": "Hide Licence to publish from reviewers", "m00110": "Download Licence to publish", "m00111": "Hide cover letter from reviewers", "m00112": "Download cover letter", "m00113": "Do you want to delete the", "m00114": "Please reupload the required files", "m00115": "This email ID is already associated with another author, please provide a different email ID", "m00116": "Hide all Other document files from reviewers", "m00117": "Download all Other document files", "m00118": "Hide all Point-by-point response from reviewers", "m00119": "Download all Point-by-point response", "m00120": "Hide Other document files from reviewers", "m00121": "Download Other document files", "m00122": "Hide point-by-point response from reviewers", "m00123": "Download Point-by-point response", "m00124": "is already marked as the corresponding author. A submission can only have one corresponding author.", "m00125": "Please enter the Abstract", "m00126": "Please select a Special Issue", "m00127": "Please enter the Conflict of Interest Statement", "m00128": "Please enter the Funding Statement", "m00129": "Please enter the Data Availability Statement", "m00130": "Please tick the checkbox to proceed", "m00131": "Please select the Section type", "m00132": "Min of {reviewerCount} reviewers needs to be invited for the AE to sign off to the next stage", "m00133": "Hide supplementary files from reviewers", "m00134": "Download supplementary files", "m00135": "Please make sure all Reviews are done before you assign the article to the Section editor.", "m00136": "Review is still in progress. If you wish to signoff, please cancel the pending reviews for: Reviewer{reviewerList}.", "m00137": "No Comments", "m00138": "We do a double-blind peer-review process in the first round of peer review. This means that reviewers are unaware of the manuscript author byline and affiliations, and authors are unaware of the reviewer names.", "m00139": "Peer review may be opened up at the second round of peer review to a single-blind review, making reviewers aware of the authors names. This was done in our previous manuscript submission system (OJS) but not in the current submission system (Kriya).", "m00140": "We follow the COPE (Committee on Publication Ethics) guideline document on Editing Peer Reviews. Accordingly, we expect peer-reviewers to submit well-written reviews with a professional and non-offensive tone. Peer reviewer comments will not be edited, censored, or modified in any way and will be presented to the corresponding author and coauthors as they were written. The editor in charge of the decision may or may not agree with the reviewer's comments and may or may not make authors aware of this.", "m00141": "Manuscripts may undergo as many rounds of peer review as necessary and as deemed by the editor in charge. The manuscript may be rejected at any time during this stage if the editor considers that the corrected versions are not publishable.", "m00142": "Hide all Other files from reviewers", "m00143": "Download all other files", "m00144": "Hide Other files from reviewers", "m00145": "Download Other files", "m00146": "Your issue has been resolved successfully.<br> Please close the browser to exit the system.", "m00147": "Please enter the Permission Statement", "m00148": "Content editing is not allowed. To suggest changes, you can add review comments for the author.", "m00149": "Content editing is not allowed. To suggest changes, you can add review comments for the author when this submission is assigned to you.", "m00150": "Hide Journal Publishing Agreement from reviewers", "m00151": "Download Journal Publishing Agreement", "m00152": "All changes saved successfully", "m00153": "Unable to save changes. Please contact your editorial helpdesk.", "m00154": "Your inputs have been saved.", "m00155": "You can close the browser tab and resume later.", "m00156": "Select between one and three topics.", "m00157": "Please enter a Middle name", "m00158": "Hide all Images from reviewers", "m00159": "Download images as single PDF", "m00160": "Hide images from reviewers", "m00161": "Hide all Visual Abstract from reviewers", "m00162": "Download all visual abstract", "m00163": "Hide Visual Abstract from reviewers", "m00164": "Download Visual Abstract", "m00165": "Hide licence to publish from reviewers", "m00166": "Download licence to publish", "m00167": "Hide all copyright form from reviewers", "m00168": "Download all copyright form", "m00169": "Hide copyright form from reviewers", "m00170": "Download copyright form", "m00171": "Ignore and proceed", "m00172": "If required, the editorial team may reach out for clarifications or additional feedback.", "m00173": "Note: ", "m00174": "Next Steps: ", "m00175": "Submission completed successfully", "m00176": "Your submission has been successfully received. The editorial team will now review it, and you will be notified of any updates.", "m00177": "If you need to make urgent corrections, please contact the editorial team before processing begins.", "m00178": "You have signed off successfully", "m00179": "Your sign-off has been recorded, and the manuscript has moved to the next stage of the workflow.", "m00180": "If any corrections or additional actions are needed, please contact the system administrator or managing editor.", "m00181": "Review submitted successfully", "m00182": "Thank you for completing your review. Your feedback has been recorded and sent to the editor.", "m00183": "Copy is disabled in this interface.", "m00184": "Hide Supplementary files not for review from reviewers", "m00185": "Download Supplementary files not for review", "m00186": "This reviewer account has been deactivated. Please contact the editorial office to reactivate the account or choose a different reviewer.", "Confirmation": "Confirmation", "PDF_COMPLETED": "PDF generation completed.", "PDF_FAILED": "PDF generation Failed... Please contact Support.", "PDF_INPROGRESS": "PDF generation in progress... Please wait.", "Download": "Download", "Download_PDF": "Download PDF", "Download_DOC": "Download DOC", "Download_Manuscript_As_PDF": "Download manuscript as PDF", "Article_details": "Article details", "Author_details": "Author details", "Article_language": "Article language", "Upload_documents": "Upload documents", "Upload_file": "Upload file", "drag_and_drop": "Drag and drop to upload file", "file_upload_extension_error": "Please upload the following formats only", "fileupload_or": "OR", "Choose_file": "Choose file", "Upload": "Upload", "Declarations": "Declarations", "Disclosures": "Disclosures", "Summary": "Summary", "Journal_name": "Journal name", "Article_type": "Article type", "Section_type": "Section", "Article_title": "Article title", "Article_summary": "Article summary", "Article_title_placeholder": "Article title...", "Article_summary_placeholder": "Article summary...", "Keywords": "Keywords", "Sections": "Sections", "Keywordinfo": "Enter keywords separated by a comma or semicolon", "Value_Keywords": "Keyword(s)", "Add_Keywords": "Add keywords", "Edit_keywords": "Edit keywords", "Selected_keywords": "Selected keywords", "Select": "Select", "Specialities_Subject_areas": "Specialities / Subject areas", "Topic": "Topic", "Add_specialities_subject_areas": "Add specialities / subject areas", "Selected_subject_areas": "Selected subject areas", "Submission_instructions": "Submission instructions", "sub_inst_text": "Please check the submission instructions here: <a target='_blank' href='https://www.medwave.cl/instrucciones-directrices-autores.html?_view=en'>LINK</a>", "asn_sub_inst_text": "Before completing this page, please ensure your submission meets <i>Kidney News</i> author guidelines. <b>Information for Authors</b> can be found here: <a target='_blank' href='https://www.asn-online.org/publications/kidneynews/authors.aspx'>https://www.asn-online.org/publications/kidneynews/authors.aspx</a>.", "sub_foot_text": "<a target='_blank' href='https://www.medwave.cl/instrucciones-directrices-autores.html?_view=en'>Submission instruction</a>", "Save": "Save", "saveRes": "Save Responses", "Back": "Back", "Next": "Next", "Add_author": "Add author", "Reorder": "Reorder", "Corresponding_author": "Corresponding author", "First_name": "First name", "Last_name": "Last name", "Address": "Address", "Affiliations": "Affiliations", "Edit_affiliation": "Edit affiliation", "Add_affiliations": "Add affiliations", "Add_new": "Add new", "Replace": "Replace", "Choose_existing": "Choose existing", "Department_Faculty": "Department / Faculty", "Institution_Organisation": "Institution Name", "City": "City", "State": "State", "Country": "Country", "Additional_informations": "Additional information", "Gender": "Gender", "Prefix": "Prefix", "Suffix": "Suffix", "Degree": "Degree", "Degrees_and_designations": "Degree(s) and designations", "Deceased": "Deceased", "Anonymous": "Anonymous", "LinkedIn_profile": "LinkedIn profile", "Twitter_handle": "X (formerly Twitter)", "On_behalf_of": "On behalf of", "Equal_contributor_with": "Equal contributor with", "First_authorship_with": "First authorship with", "Senior_authorship_with": "Senior authorship with", "Profile_pic": "Profile picture", "Author_bio": "Author bio", "Edit": "Edit", "Supporting_documents": "Supporting documents", "Manuscript": "Manuscript", "Article_draft": "Article draft", "Revised_manuscript": "Revised Manuscript", "Cover_letter": "Cover letter", "Coverletter": "Cover letter", "Figure": "Figure", "Image": "Image", "Visualabstract": "Visual Abstract", "Choose_document_type": "Choose document type", "Choose_document": "Choose document", "Add": "Add", "Change": "Change", "Statements_of_declarations": "Statements and declarations", "pre_submission_checklist": "Pre-Submission checklist", "Submission_summary": "Submission summary", "Enter_the_number_shown_here": "Enter the number shown here:", "OK": "OK", "Ok": "Ok", "Original": "Original", "Form_details": "Form details", "Form": "Form", "Other_documents": "Other documents", "Summary_review": "Review summary", "Add_comment": "Add comment", "CommentToEditor": "Comment to editor", "CommentToAuthor": "Comment to author", "Save_summary": "Save Summary", "Save_note": "All changes saved on: ", "Send_to_Managing_Editor": "Send to Managing editor", "Send_to_Associate_Editor": "Send to Associate editor", "Mark_as_confidentials": "<PERSON> as confidential", "Add_attachments": "Add attachments", "Make_decision": "Make decision", "Rate_the_reviewer": "Rate the reviewer", "Accepted_as_is": "Accepted as is", "Declined_review": "Declined review", "Rejected_article": "Rejected article", "Revisions_needed": "Revisions needed", "Yet_to_review": "Yet to review", "My_rating": "My rating", "Remarks": "Remarks", "Delete": "Delete", "Add_correspondence_address": "Add correspondence address", "Correspondence_address": "Correspondence address", "Edit_Correspondence_address": "Edit correspondence address", "ORCID_if_available_": "ORCID (if available)", "Email": "Email", "Add_another_affiliation": "Add another affiliation", "Add_another": "Add another", "AUTHORS": "Authors", "Correspondence_to": "Correspondence to", "Submit": "Submit", "Resubmit": "Resubmit", "Authors_list": "Authors list", "Add_new_author": "Add new author", "clicktoadd": "Click to add", "clicktoedit": "Click to edit", "CharactersLeft": "Characters left", "Selected": "Selected", "Default": "<PERSON><PERSON><PERSON>", "Lower_Alpha": "Lower Alpha", "Lower_Greek": "Lower Greek", "Lower_Roman": "Lower Roman", "Upper_Alpha": "Upper Alpha", "Upper_Roman": "Upper Roman", "None": "None", "Circle": "Circle", "Disc": "Disc", "Square": "Square", "Check_Box": "Check Box", "En_dash": "En dash", "Session_time": "Session time", "Running_head": "Running head", "Authors": "Authors", "Affiliation": "Affiliation", "Correspondence": "Correspondence", "Abstract_head": "Abstract head", "Abstract_title": "Abstract title", "Abstract_para": "Abstract para", "Keyword_head": "Keyword head", "Keyword_para": "Keyword para", "Heading_1": "Heading 1", "Heading_2": "Heading 2", "Heading_3": "Heading 3", "Heading_4": "Heading 4", "Heading_5": "Heading 5", "Heading_6": "Heading 6", "Para": "Para", "Ack_head": "ACK. head", "Ref_head": "Ref head", "BM_head": "BM. head", "Ack_para": "ACK. para", "Reference": "Reference", "BM_para": "BM. para", "Figure_caption": "Figure caption", "Table_caption": "Table caption", "Figures": "Figures", "Send_to_Assoc_editor": "Send to Assoc editor", "Sign-off": "Sign-off", "Reviewer": "Reviewer", "Assigned": "Assigned", "Declined": "Declined", "In_progress": "In progress", "Last_activity": "Last activity", "Completed": "Completed", "Status": "Status", "Invitation": "Invitation", "Review": "Review", "Action": "Action", "Assign": "Assign", "Unassign": "Unassign", "Follow_up": "Follow up", "Add_Reviewer": "Add Reviewer", "Reviewer_history": "Reviewer history", "Name": "Name", "Reviews_assigned": "Reviews assigned", "Reviews_completed": "Reviews completed", "Reviews_declined": "Reviews declined", "Average_rating": "Average rating", "Average_turnaround": "Average turnaround", "Confirm": "Confirm", "Sign_off": "Sign off", "Submit_review": "Submit review", "Exit": "Save and exit", "Summary_notes": "Summary notes", "Add_Associate_editor": "Add Associate editor", "Add_Handling_editor": "Add Associate editor", "Given_name": "Given name", "Click_to_add": "Click to add", "Click_to_edit": "Click to edit", "Resubmit_manuscript": "Resubmit manuscript", "Add_keywords": "Add keywords", "Characters_left": "Characters left", "ChooseLanguage": "Language", "Not_tagged": "Not tagged", "search_style": "Search style", "Assign_Editors": "Assign Editors", "Assign_Reviewers": "Assign Reviewers", "Contant_Editorial_Helpdesk": " Contact editorial helpdesk", "Assign_as_backup": "Assign as backup", "Remove_backup": "Remove backup", "Assigned_as_backup": "Assigned as backup", "Removing": "Removing...", "Type_to_search": "Type to search", "Ask": "Ask", "Due": "Due", "Start": "Start", "Follow-up": "Follow-up", "Assign_Associate_Editor": "Assign Associate Editor", "Associate_Editor": "Associate editor", "Associate_Editor_Check": "Associate Editor Check", "Primary_Editor_Check": "Primary Editor Check", "Screening_check": "Screening check", "EIC_review": "EIC review", "Board_review": "Board review", "Editorial_review": "Editorial review", "Editorial_QC": "Editorial QC", "EIC_decision": "EIC decision", "Revised_Editor_Check": "Revised Editor Check", "Editor_Send_Decision": "Editor Send Decision", "eMail": "eMail", "Articles_reviewed": "Articles reviewed", "Rating": "Rating", "Assign_Handling_Editor": "Assign Handling Editor", "Handling_Editor": "Handling editor", "Email_template": "Email template", "Send": "Send", "Add_Associate_Editor": "Add Associate Editor", "Add_Handling_Editor": "Add Handling Editor", "Title_page": "Title page", "Table": "Table", "Supplement": "Other document files", "Otherfiles": "Other files", "Copyrightform": "Copyright form", "Responsetoreviewer": "Point-by-point response", "Send_to_Assoc_Editor": "Send to Assoc. Editor", "Insert_row_above": "Insert row above", "Insert_row_below": "Insert row below", "Insert_column_left": "Insert column left", "Insert_column_right": "Insert column right", "Delete_row": "Delete row", "Delete_column": "Delete column", "Merge_cells": "Merge cells", "Split_cells": "Split cells", "Move_to_head": "Move to head", "Mark_as_head": "<PERSON> as head", "Mark_as_confidential": "<PERSON> as confidential", "Hide_from _author": "Hide from Author", "Review_Summary": "Review Summary", "Read_more": "Read more", "collapse": "Collapse", "Managing_Editor": "Managing editor", "Editorial_Assistant": "Editorial assistant", "JOA": "JOA", "Editor-in-Chief": "Editor-in-Chief", "Section_Editor": "Section editor", "Development_Editor": "Development editor", "Executive_Editor": "Executive editor", "Lead_Editor": "Lead Editor", "Lead_guest_editor": "Lead guest editor", "guest_editor": "Guest editor", "View_article": "View article", "Reportingguideline": "Reporting guideline", "Reporting_guideline": "Reporting guideline", "Author_response_to_reviews": "Point-by-point response", "Headshot": "Headshot", "Licencetopublish": "License to publish", "Tables": "Tables", "Trackchanges": "Marked & revised manuscript", "Document_type": "Document type", "LSHeader": "Welcome to the Medwave submission system", "LSSubHeader": "Log in with your user ID and password to submit your manuscript", "Login": "<PERSON><PERSON>", "LoginMed": "Log in with Medwave", "Powered_by": "Powered by", "Resources": "Resources", "Submission_instruction": "Submission instruction", "Cancel": "Cancel", "Reply": "Reply", "addLine1": "Address line 1", "addLine2": "Address line 2", "Postal": "Postal code", "medwave_sub_img": "/images/customers/medwave/English-submit.png", "Go_to_old_UI": "Go to old UI", "reviewer_exist": "This user is already an existing Reviewer. Please provide a different email address.", "handlingeditor_exist": "This user is already an existing Handling editor. Please provide a different email address.", "associateeditor_exist": "This user is already an existing Associate editor. Please provide a different email address.", "default_exist": "already exist", "Presub_Checklist": "I have reviewed and understood all of the above.", "asn_Presub_Checklist": "I have reviewed and understand all of the above.", "Presubmission_Checklist": "Pre-submission checklist", "Supplemantary_file": "Supplementary files", "Supplementaryfile": "Supplementary File", "ConflictofInterest": "Conflict of Interest", "Publishing_Agreement": "Publishing Agreement", "Supporting_doc_formats": "Supported File Formats: Doc, DocX", "Abstract": "Abstract", "Brief_Abstract": "Brief abstract/Key points", "Abstract_placeholder": "Article abstract...", "Brief_Abstract_placeholder": "Brief abstract/Key points...", "Regular_issue": "Regular Issue", "Special_issue": "Special Issue", "Submit_to_an_issue": "Submit to an Issue", "Data_Availability_placeholder": "Enter text (if you already included a data availability in your manuscript you can copy and paste it here)", "Funding_statement_placeholder": "Enter text (if you already included a funding in your manuscript you can copy and paste it here)", "conflict_of_interest": "Conflict of interest", "Conflict_intrest_yes": "Yes", "Conflict_intrest_no": "No", "Conflict_Intrest_placeholder": "Enter text (if you already included a conflict of interest in your manuscript you can copy and paste it here)", "Conflict_Intrest_placeholder_asn": "Describe the relevant interest or commitment here. If there are multiple authors, please indicate to which author this interest pertains. (If you already included a conflict of interest statement in your article draft, you can copy and paste it here.)", "Issue_type": "Issue type", "Special_issue_type": "Special issue", "Pre_submission_checklist": "Pre-submission checklist", "back_to_submission": "Back to Submission", "agree_submit": "Agree &amp; Submit", "Add_rev_text": "Add your comments", "geo_science_world": "GeoScienceWorld", "thieme": "<PERSON><PERSON><PERSON><PERSON>", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "support_litho_text": "Support: <EMAIL>", "support_thieme_text": "Support: <EMAIL>", "Add_rev_text_alt": "Notes to the Author", "saveforlater": "Save and exit", "Invite_Reviewers": "Invite Reviewers", "Invited_on": "Invited on", "Invite": "Invite", "invite_message": "Invited for review", "completed_message": "Invited for review", "accepted_message": "Invited for review", "terminated_message": "Invited for review", "rejected_message": "Invited for review", "backup_message": "Selected as backup", "waiting_message": "Invited for review", "standby_message": "Added to reviewer list", "terminated_status": "Cancelled", "waiting_status": "Yet to respond", "backup_status": "Selected as backup", "completed_status": "Accepted", "accepted_status": "Accepted", "inprogress_status": "Accepted", "standby_status": "Yet to invite", "rejected_status": "Declined", "Journal_Publishing_Agreement": "Journal Publishing Agreement", "read_only_mode": "<span style='font-weight:550;'>View only</span> <span style='font-size:0.7rem'> (current assignee: #assigneeName) </span>", "read_only_rejected_article": "<span style='font-weight:550;'>View only</span> <span style='font-size:0.7rem'> (This is a rejected manuscript) </span>", "review_history": "Review history", "active_assignments": "Active assignments", "select_editor": "Select editor", "Hide_from_reviewers": "Hide from reviewers", "withdraw_mailnode": "Notify key stakeholders via email", "withdraw_manuscript": "Withdraw manuscript", "reject_manuscript": "Reject manuscript", "withdraw_success": "The manuscript has been successfully withdrawn.", "Technical_Check": "Technical Check", "Journal_Check": "Journal Check", "Editor_Scope_Check": "Editor <PERSON><PERSON>", "LTP_form": "Licence to publish form"}}, "headers": {"content-type": "application/json"}, "category": "Configuration", "public": false, "hidden": false}, {"message": "POST geteditorsettings with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/geteditorsettings/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "ERROR: Parameter lang missing in input.", "headers": {"content-type": "application/json"}, "category": "Configuration", "public": false, "hidden": false}]