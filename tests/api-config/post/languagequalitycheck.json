[{"message": "Get empty response as the expected payload is not specified", "status": "400", "url": "/api/languagequalitycheck", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {}, "category": "Integration", "public": false, "hidden": false}, {"message": "POST languagequalitycheck with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/languagequalitycheck/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "Integration", "public": false, "hidden": false}]