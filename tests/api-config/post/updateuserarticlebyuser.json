[{"message": "Successfully update user article by user with elastic ID", "status": "200", "url": "/api/updateuserarticlebyuser", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "email": "<EMAIL>", "elasticID": "elastic_123456", "role": "reviewer", "status": "in-progress", "assignedDate": "2024-01-15T09:00:00Z", "userComments": "Review in progress, preliminary assessment complete", "updateFields": {"progress": 50, "lastActivity": "2024-01-20T14:30:00Z"}}, "expectedOutput": {"status": "Success", "elasticResponse": {}, "uniqueId": "unique_123"}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Update user article status to completed", "status": "200", "url": "/api/updateuserarticlebyuser", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "email": "<EMAIL>", "status": "completed", "completedDate": "2024-02-01T16:00:00Z", "userComments": "Review completed successfully"}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/updateuserarticlebyuser", "data": {"customer": "jmir", "project": "jmir"}, "expectedOutput": {"error": "Missing required parameters: doi, email"}, "method": "post", "category": "Articles", "public": false, "hidden": false}]