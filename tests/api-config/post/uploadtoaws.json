[{"message": "Successfully upload image file to AWS", "status": "200", "url": "/api/uploadtoaws", "FormData": true, "formFields": {"customer": "jmir", "project": "jmir", "doi": "244444", "file": {"type": "file", "path": "/_testFiles/inputfiles/post/uploadtoaws/figure1.jpg"}, "fileType": "image", "awsBucket": "jmir-assets", "awsPath": "images/figures/", "fileName": "figure1-retinal-scan.jpg", "contentType": "image/jpeg", "replaceExisting": false, "makePublic": true}, "expectedOutput": {"status": "Success", "awsUrl": "https://s3.amazonaws.com/jmir-assets/images/figures/figure1-retinal-scan.jpg", "fileId": "file_123456", "fileName": "figure1-retinal-scan.jpg", "uploadedAt": "2024-01-15T13:00:00Z", "fileSize": 1048576, "contentType": "image/jpeg"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error when file is missing", "status": "400", "url": "/api/uploadtoaws", "FormData": true, "formFields": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "fileType": "image"}, "expectedOutput": {"error": "file is required"}, "method": "post", "category": "Exports", "public": false, "hidden": false}]