[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/generatepurchaseorder", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST generatepurchaseorder with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/generatepurchaseorder/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"error": "Missing parameters"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]