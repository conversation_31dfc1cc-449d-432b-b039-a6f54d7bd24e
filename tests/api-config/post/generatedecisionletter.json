[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/generatedecisionletter", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": "Required parameters missing from request", "category": "Articles", "public": false, "hidden": false}, {"message": "POST generatedecisionletter with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/generatedecisionletter/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "Required parameters missing from request", "headers": {"content-type": "application/json"}, "category": "Articles", "public": false, "hidden": false}]