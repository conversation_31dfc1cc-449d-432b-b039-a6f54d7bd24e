[{"message": "Successfully update user assigned pages", "status": "200", "url": "/api/updateuserassignedpages", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "email": "<EMAIL>", "assignedPages": [{"pageNumber": 1, "pageType": "title", "status": "completed", "assignedDate": "2024-01-15T09:00:00Z", "dueDate": "2024-01-20T17:00:00Z"}, {"pageNumber": 2, "pageType": "content", "status": "in-progress", "assignedDate": "2024-01-15T09:00:00Z", "dueDate": "2024-01-25T17:00:00Z"}], "role": "editor", "updateType": "update"}, "expectedOutput": {"status": "Success", "updatedPages": 2, "assignedTo": "<EMAIL>"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error when assigned pages array is empty", "status": "400", "url": "/api/updateuserassignedpages", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "email": "<EMAIL>", "assignedPages": []}, "expectedOutput": {"error": "assignedPages array cannot be empty"}, "method": "post", "category": "Users", "public": false, "hidden": false}]