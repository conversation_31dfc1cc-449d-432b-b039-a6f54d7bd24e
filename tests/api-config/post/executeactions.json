[{"message": "Throw Error as the payload is not specified", "status": "404", "url": "/api/executeactions", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST executeactions with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/executeactions/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]