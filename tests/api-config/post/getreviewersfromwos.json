[{"message": "Get empty reviewers from WOS if no reviewers are found", "status": "200", "url": "/api/getreviewersfromwos", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": [], "category": "Users", "public": false, "hidden": false}, {"message": "POST getreviewersfromwos with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/getreviewersfromwos/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": [], "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}]