[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/bulkinvitereviewers", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Users"}, {"message": "POST bulkinvitereviewers with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/bulkinvitereviewers/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "JOB_MANAGER_URL environment variable is not defined", "headers": {"content-type": "application/json"}, "category": "Users"}]