[{"message": "Successfully update user profile information", "status": "200", "url": "/api/updateuser", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "role": "reviewer", "status": "active", "affiliation": "University of Example", "orcid": "0000-0000-0000-0000", "updateType": "profile"}, "expectedOutput": {"status": "Success", "userId": "user123", "updatedFields": ["firstName", "lastName", "affiliation", "orcid"]}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Update user role from author to editor", "status": "200", "url": "/api/updateuser", "data": {"customer": "ppl", "project": "bst", "email": "<EMAIL>", "role": "editor", "updateType": "role"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error when required email is missing", "status": "400", "url": "/api/updateuser", "data": {"customer": "jmir", "project": "jmir", "firstName": "<PERSON>"}, "expectedOutput": {"error": "Email is required"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "POST updateuser", "status": "200", "url": "/api/updateuser/", "method": "post", "data": {"login-details": {"status": "in-active"}}, "expectedOutput": {"success": {"_index": "dev-users", "_id": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "_version": 6035, "result": "updated", "_shards": {"total": 2, "successful": 1, "failed": 0}, "_seq_no": 12891, "_primary_term": 67}}, "category": "Users", "public": false, "hidden": false}]