[{"message": "Update the stage's due date", "status": "200", "url": "/api/updatestageduedate?apiKey={APIKEY}", "data": {"dueDate": "Jul 11 2025 16:06:00", "stageName": "Waiting for CE", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "dateType": "planned-end-date"}, "method": "post", "category": "Articles"}, {"message": "Update the stage's due date", "status": "400", "url": "/api/updatestageduedate?apiKey={APIKEY}", "params": "true", "param": {"parameters": [{"name": "message", "type": "string", "value": "Missing core identifiers for due date update"}]}, "data": {"dueDate": "Jul 11 2025 16:06:00", "stageName": "Waiting for CE", "customer": "bmj", "doi": "bmjoq-2023-00235709", "dateType": "planned-end-date"}, "method": "post", "category": "Articles"}, {"message": "POST updatestageduedate with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/updatestageduedate/", "method": "post", "data": {"id": "aaas-demo-20205-2003", "dueDate": "Jul 26 2025 13:35:00", "stageName": "Pre-editing", "customer": "aaas", "project": "aaas", "doi": "aaas-demo-20205-2003", "to": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "recipient": "<PERSON><PERSON><PERSON>", "dateType": "planned-end-date", "cardID": "0", "notify": {"notifyTitle": "Update due date", "successText": "Due date updated for aaas-demo-20205-2003", "errorText": "Error while update due date for aaas-demo-20205-2003"}, "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Pre-editing\"] and status[.=\"in-progress\"]]", "content": "<stage><planned-end-date>2025-07-26 13:35:00</planned-end-date></stage>"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": 200, "message": "Update due date request processed successfully", "result": {}}, "category": "Articles"}, {"message": "POST updatestageduedate with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/updatestageduedate/", "method": "post", "data": {"id": "aaas-demo-20205-2003", "dueDate": "Jul 26 2025 13:39:00", "stageName": "Pre-editing", "customer": "aaas", "project": "aaas", "doi": "aaas-demo-20205-2003", "to": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "recipient": "<PERSON><PERSON><PERSON>", "dateType": "planned-end-date", "cardID": "0", "notify": {"notifyTitle": "Update due date", "successText": "Due date updated for aaas-demo-20205-2003", "errorText": "Error while update due date for aaas-demo-20205-2003"}, "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Pre-editing\"] and status[.=\"in-progress\"]]", "content": "<stage><planned-end-date>2025-07-26 13:39:00</planned-end-date></stage>"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": 200, "message": "Update due date request processed successfully", "result": {}}, "category": "Articles"}, {"message": "POST updatestageduedate with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/updatestageduedate/", "method": "post", "data": {"id": "bmjoq-2023-00235709", "dueDate": "Jul 26 2025 13:54:00", "stageName": "Waiting for CE", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "to": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "recipient": "<PERSON><PERSON><PERSON>", "dateType": "planned-end-date", "cardID": "0", "notify": {"notifyTitle": "Update due date", "successText": "Due date updated for bmjoq-2023-00235709", "errorText": "Error while update due date for bmjoq-2023-00235709"}, "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]", "content": "<stage><planned-end-date>2025-07-26 13:54:00</planned-end-date></stage>"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": 200, "message": "Update due date request processed successfully", "result": {}}, "category": "Articles"}]