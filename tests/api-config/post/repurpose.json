[{"message": "POST repurpose with customer=example-customer, project=source-project, doi=10.1234/source.doi", "status": "200", "url": "/api/repurpose/", "method": "post", "data": {"customer": "example-customer", "project": "source-project", "doi": "10.1234/source.doi", "repproject": "target-project", "repdoi": "10.1234/target.doi"}, "expectedOutput": {"status": 200, "content": "job loaded successfully"}, "headers": {"content-type": "application/json"}, "category": "Workflow"}]