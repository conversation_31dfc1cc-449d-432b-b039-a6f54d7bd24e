[{"message": "Throw Error as the payload is not specified", "status": "403", "url": "/api/htmltopdf", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": "htmltopdf: HTML content input missed", "category": "Exports", "public": false, "hidden": false}, {"message": "POST htmltopdf with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 403)", "status": "403", "url": "/api/htmltopdf/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "htmltopdf: HTML content input missed", "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]