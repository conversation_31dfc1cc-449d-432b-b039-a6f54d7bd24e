[{"message": "POST addstage with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/addstage/", "method": "post", "data": {"cardID": "0", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "holdFrom": "Hold", "releaseHold": "true", "notify": {"notifyTitle": "Remove Hold", "successText": "bmjoq-2023-00235709 has been removed from hold", "errorText": "Error while remove hold for bmjoq-2023-00235709"}, "updatingXML": false, "getFinalXMLAttempt": 0, "stageName": "Waiting for CE", "notesData": {"content": [{"content": [], "created": "2025-07-25 08:34:05"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": "200", "message": "Current stage name and requested name are same : Waiting for CE"}, "step": "add stage"}, "category": "Workflow"}, {"message": "POST addstage with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/addstage/", "method": "post", "data": {"cardID": "0", "customer": "aaas", "project": "aaas", "doi": "aaas-demo-20205-2003", "holdFrom": "Hold", "releaseHold": "true", "notify": {"notifyTitle": "Remove Hold", "successText": "aaas-demo-20205-2003 has been removed from hold", "errorText": "Error while remove hold for aaas-demo-20205-2003"}, "updatingXML": "aaas-demo-20205-2003", "getFinalXMLAttempt": 0, "stageName": "Pre-editing", "notesData": {"content": [{"content": [], "created": "2025-07-25 08:21:07"}], "doi": "aaas-demo-20205-2003", "customer": "aaas", "project": "aaas", "noteType": "workflow", "logging": false}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": "200", "message": "Current stage name and requested name are same : Pre-editing"}, "step": "add stage"}, "headers": {"content-type": "application/json"}, "category": "Workflow"}, {"message": "POST addstage with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/addstage/", "method": "post", "data": {"cardID": "0", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "holdFrom": "Hold", "releaseHold": "true", "notify": {"notifyTitle": "Remove Hold", "successText": "bmjoq-2023-00235709 has been removed from hold", "errorText": "Error while remove hold for bmjoq-2023-00235709"}, "updatingXML": "bmjoq-2023-00235709", "getFinalXMLAttempt": 0, "stageName": "Waiting for CE", "notesData": {"content": [{"content": [], "created": "2025-07-25 08:34:05"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": "200", "message": "Current stage name and requested name are same : Waiting for CE"}, "step": "add stage"}, "headers": {"content-type": "application/json"}, "category": "Workflow"}]