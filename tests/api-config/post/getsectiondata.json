[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getsectiondata", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Issues", "public": false, "hidden": false}, {"message": "POST getsectiondata with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/getsectiondata/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "Configuration not found", "headers": {"content-type": "application/json"}, "category": "Issues", "public": false, "hidden": false}]