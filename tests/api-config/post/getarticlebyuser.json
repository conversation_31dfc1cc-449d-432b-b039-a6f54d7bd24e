[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getarticlebyuser", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "POST getarticlebyuser with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/getarticlebyuser/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"statusCode": 500, "body": {"status": {"code": 404, "message": {"status": "validation failed", "message": "Required parameters missing to generate query - \"{\\\"url\\\":\\\"/dev/_search\\\",\\\"queryTemplate\\\":{\\\"query\\\":{\\\"bool\\\":{\\\"must\\\":[{\\\"query_string\\\":{\\\"query\\\":\\\"customer:(*) AND project:(*) AND kriya-version:* AND workflowStatus:*\\\"}},{\\\"nested\\\":{\\\"path\\\":\\\"stage\\\",\\\"query\\\":{\\\"bool\\\":{\\\"should\\\":[{\\\"wildcard\\\":{\\\"stage.article-key.key.email.keyword\\\":{\\\"value\\\":\\\"*{user}*\\\",\\\"case_insensitive\\\":true}}},{\\\"wildcard\\\":{\\\"stage.assigned.to.keyword\\\":{\\\"value\\\":\\\"*{user}*\\\",\\\"case_insensitive\\\":true}}}]}}}}]}},\\\"from\\\":\\\"0\\\",\\\"size\\\":\\\"1000\\\",\\\"sort\\\":[{\\\"stageDue\\\":{\\\"order\\\":\\\"asc\\\"}}]},\\\"returnObj\\\":{\\\"error\\\":\\\"resData.error\\\",\\\"success\\\":\\\"resData.body.hits\\\"}}\" "}}}, "headers": {"x-dns-prefetch-control": "off", "x-frame-options": "SAMEORIGIN", "strict-transport-security": "max-age=15552000; includeSubDomains", "x-download-options": "noopen", "x-content-type-options": "nosniff", "x-xss-protection": "1; mode=block", "access-control-allow-origin": "*", "content-type": "application/json; charset=utf-8", "content-length": "900", "etag": "W/\"384-QVRiYubYoa9lamGiHpS3J6nlt2I\"", "vary": "Accept-Encoding", "date": "Thu, 24 Jul 2025 10:44:01 GMT", "connection": "keep-alive", "keep-alive": "timeout=5"}, "request": {"uri": {"protocol": "http:", "slashes": true, "auth": null, "host": "localhost:7001", "port": "7001", "hostname": "localhost", "hash": null, "search": null, "query": null, "pathname": "/query/getarticlebyuser", "path": "/query/getarticlebyuser", "href": "http://localhost:7001/query/getarticlebyuser"}, "method": "post", "headers": {"Content-Type": "application/json", "Accept": "application/json", "content-length": 123}}}, "headers": {"content-type": "application/json"}, "category": "Metadata and Reports", "public": false, "hidden": false}]