[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/datawarehouse", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": 200, "message": "update success"}, "category": "Integration", "public": false, "hidden": false}, {"message": "POST datawarehouse with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/datawarehouse/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": 200, "message": "update success"}, "headers": {"content-type": "application/json"}, "category": "Integration", "public": false, "hidden": false}]