[{"message": "Successfully validate volume and issue number", "status": "200", "url": "/api/validatevolumeissuenumber", "data": {"customer": "ppl", "project": "bst", "volume": "103", "issue": "5", "year": 2024, "validateAgainstExisting": true, "checkSequence": true, "allowDuplicates": false}, "expectedOutput": {"status": "Success", "isValid": true, "validationResults": {"volumeValid": true, "issueValid": true, "sequenceValid": true, "duplicateFound": false}, "suggestions": []}, "method": "post", "category": "References", "public": false, "hidden": false}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/validatevolumeissuenumber", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "volume": "103"}, "expectedOutput": {"error": "Missing required parameter: issue", "validationErrors": ["issue is required"]}, "method": "post", "category": "References", "public": false, "hidden": false}]