[{"message": "Get empty response as the expected payload is not specified", "status": "400", "url": "/api/jnisworkflow", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {}, "category": "References", "public": false, "hidden": false}, {"message": "POST jnisworkflow with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/jnisworkflow/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "References", "public": false, "hidden": false}]