[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/loggerreport", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"error": "Missing required parameters: table or data"}, "category": "System", "public": false, "hidden": false}, {"message": "POST loggerreport with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/loggerreport/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"error": "Missing required parameters: table or data"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]