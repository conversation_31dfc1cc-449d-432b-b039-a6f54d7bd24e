[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/htmltoxml", "encode": true, "data": {"customer": "bmj"}, "expectedOutput": {"status": {"code": 400, "message": "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "POST htmltoxml with customer=bmj", "status": "200", "url": "/api/htmltoxml/", "method": "post", "data": {"customer": "bmj"}, "expectedOutput": {"status": {"code": 400, "message": "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]