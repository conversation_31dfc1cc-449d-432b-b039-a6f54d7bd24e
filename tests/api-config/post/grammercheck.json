[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/grammercheck", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Integration", "public": false, "hidden": false}, {"message": "POST grammercheck with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/grammercheck/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {}, "headers": {"content-type": "application/json"}, "category": "Integration", "public": false, "hidden": false}]