[{"message": "Test label API with minimal required fields and verify error response for missing required fields", "status": "200", "url": "/api/label", "method": "post", "expectedOutput": "error - {\"status\":500}\nresponse - {\"status\":{\"code\":404,\"message\":{\"root_cause\":[{\"type\":\"strict_dynamic_mapping_exception\",\"reason\":\"mapping set to strict, dynamic introduction of [color] within [_doc] is not allowed\"}],\"type\":\"strict_dynamic_mapping_exception\",\"reason\":\"mapping set to strict, dynamic introduction of [color] within [_doc] is not allowed\"}}}\n", "category": "Configuration", "public": false, "hidden": false}, {"message": "POST label", "status": "200", "url": "/api/label/", "method": "post", "category": "Configuration", "public": false, "hidden": false}]