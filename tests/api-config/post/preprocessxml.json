[{"message": "Return error message when Authentication is not given", "status": "401", "url": "/api/preprocessxml", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Preprocess the content for IMA Article", "status": "400", "url": "/api/preprocessxml", "data": {"customer": "ima", "project": "ijm", "doi": "ijm-00001"}, "expectedOutput": "{\"status\":{\"code\":\"400\",\"message\":\"Could not pre-process the xml. Input content missing\",\"error\":{\"status\":{\"code\":\"400\",\"message\":\"update meta reject\",\"error\":{\"errno\":-61,\"code\":\"ECONNREFUSED\",\"syscall\":\"connect\",\"address\":\"::1\",\"port\":9080}},\"step\":\"preprocessxml\"}},\"step\":\"preprocessxml\"}", "method": "post", "category": "Articles", "public": false, "hidden": false}]