[{"message": "Reset cookies", "status": "200", "url": "/api/roleswitch?apiKey={APIKEY}", "data": {"kurolesConfig": {"customer-name": "ppl", "role-type": "publisher", "access-level": "manager"}}, "method": "post", "expectedOutput": {"code": 200, "message": "Role switched successfully"}, "category": "Users"}, {"message": "Reset cookies - invalid data", "status": "400", "url": "/api/roleswitch?apiKey={APIKEY}", "data": {"kurolesConfigss": {"customer-name": "ppl", "role-type": "publisher", "access-level": "manager"}}, "method": "post", "expectedOutput": {"code": 400, "message": "Missing role config"}, "category": "Users"}, {"message": "POST roleswitch", "status": "200", "url": "/api/roleswitch/", "method": "post", "data": {"kurolesConfig": {"role-type": "production", "customer-name": "aaas", "access-level": "admin"}}, "expectedOutput": {"code": 200, "message": "Role switched successfully"}, "category": "Users"}]