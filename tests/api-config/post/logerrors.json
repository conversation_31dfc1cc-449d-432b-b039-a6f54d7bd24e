[{"message": "Get success message from logerrors API", "status": "200", "url": "/api/logerrors", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": "ok", "category": "System", "public": false, "hidden": false}, {"message": "POST logerrors with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/logerrors/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "ok", "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]