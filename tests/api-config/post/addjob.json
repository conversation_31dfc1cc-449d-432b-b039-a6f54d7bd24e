[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/addjob", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": "400", "message": "Job already exists", "logData": false}, "step": "add job"}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST addjob with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/addjob/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": "400", "message": "Job already exists", "logData": false}, "step": "add job"}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]