[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getclinicaldata", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"message": "Mandatory field is missing"}, "category": "Configuration", "public": false, "hidden": false}, {"message": "POST getclinicaldata with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/getclinicaldata/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"message": "Mandatory field is missing"}, "headers": {"content-type": "application/json"}, "category": "Configuration", "public": false, "hidden": false}]