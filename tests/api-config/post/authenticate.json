[{"message": "Return error if we not passed any parameter", "status": "200", "url": "/api/authenticate", "method": "post", "expectedOutput": {"error": "Missing parameters", "status": 0}, "category": "Users", "public": false, "hidden": false}, {"message": "<PERSON><PERSON> failed - Invalid user", "status": "200", "url": "/api/authenticate", "data": {"user": "<EMAIL>", "pass": "1234"}, "expectedOutput": "", "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate with type=keeplive", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"type": "keeplive"}, "expectedOutput": {"status": "ok"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate with customer=aaas, project=aaas, doi=aaas-demo-20205-2003, type=keeplive", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"customer": "aaas", "project": "aaas", "doi": "aaas-demo-20205-2003", "type": "keeplive"}, "expectedOutput": 500, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate with type=keeplive", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"type": "keeplive", "searchUserMail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "expectedOutput": 500, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate", "status": "200", "url": "/api/authenticate/", "method": "post", "expectedOutput": {"error": "Missing parameters", "status": 0}, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"user": "<EMAIL>", "pass": "1234"}, "expectedOutput": {"error": "Access denied"}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"user": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "pass": "J@!shanka6"}, "expectedOutput": {"error": "Access denied"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST authenticate with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709, type=keeplive", "status": "200", "url": "/api/authenticate/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "type": "keeplive"}, "expectedOutput": 500, "category": "Users", "public": false, "hidden": false}]