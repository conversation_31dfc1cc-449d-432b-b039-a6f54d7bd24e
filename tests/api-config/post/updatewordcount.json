[{"message": "Successfully update article word count with full breakdown", "status": "200", "url": "/api/updatewordcount", "data": {"customer": "jmir", "project": "jmir", "doi": "244444", "wordCountData": {"totalWords": 4500, "abstractWords": 250, "bodyWords": 3800, "referenceWords": 450, "figureCount": 5, "tableCount": 3, "equationCount": 12}, "countType": "automatic", "updateElastic": true}, "expectedOutput": {"status": "Success", "totalWords": 4500, "countBreakdown": {"abstract": 250, "body": 3800, "references": 450}, "elasticUpdated": true}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Return error when word count data is missing", "status": "400", "url": "/api/updatewordcount", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "countType": "automatic"}, "expectedOutput": {"error": "wordCountData is required"}, "method": "post", "category": "Articles", "public": false, "hidden": false}]