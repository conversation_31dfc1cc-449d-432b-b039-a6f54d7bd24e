[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/issuedata_milestone", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "category": "Issues", "public": false, "hidden": false}, {"message": "POST issuedata_milestone with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/issuedata_milestone/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "headers": {"content-type": "application/json"}, "category": "Issues", "public": false, "hidden": false}]