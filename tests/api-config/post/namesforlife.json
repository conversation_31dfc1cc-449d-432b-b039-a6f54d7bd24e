[{"message": "Throw Error as the payload is not specified", "status": "500", "url": "/api/namesforlife", "data": {"customer": "mbs", "project": "ijsem"}, "method": "post", "expectedOutput": "Internal Server Error", "category": "Integration", "public": false, "hidden": false}, {"message": "POST namesforlife with customer=mbs, project=ijsem (status 500)", "status": "500", "url": "/api/namesforlife/", "method": "post", "data": {"customer": "mbs", "project": "ijsem"}, "expectedOutput": 500, "headers": {"content-type": "application/json"}, "category": "Integration", "public": false, "hidden": false}]