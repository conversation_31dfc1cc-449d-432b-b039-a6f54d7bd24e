[{"message": "Throw Error as the payload is not specified", "status": "500", "url": "/api/getstagereport", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "method": "post", "expectedOutput": {"message": "Articles not found"}, "category": "Workflow", "public": false, "hidden": false}, {"message": "POST getstagereport with customer=bmj, project=bjophthalmol (status 500)", "status": "500", "url": "/api/getstagereport/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "expectedOutput": {"message": "Articles not found"}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]