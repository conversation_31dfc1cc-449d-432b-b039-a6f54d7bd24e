[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getproofprefix", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"error": "One or more of required parameters (customer, project, doi) is/are not provided."}, "category": "System", "public": false, "hidden": false}, {"message": "POST getproofprefix with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/getproofprefix/", "method": "post", "data": {"customer": "aaas", "project": "aaas", "doi": "aaas-demo-20205-2003", "articleType": "", "category": "", "altTemplate": "", "jobLoadDate": ""}, "expectedOutput": {"error": "One or more of required parameters (customer, project, doi) is/are not provided."}, "category": "System", "public": false, "hidden": false}, {"message": "POST getproofprefix with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/getproofprefix/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "articleType": "Quality education report", "category": "", "altTemplate": "", "jobLoadDate": ""}, "expectedOutput": {"message": "undefined"}, "category": "System", "public": false, "hidden": false}]