[{"message": "Successfully update ROR registry data", "status": "200", "url": "/api/updateror", "FormData": true, "formFields": {"funding-registry": {"type": "file", "path": "/_testFiles/inputfiles/post/updateror/ror-registry.rdf"}, "cmsVersion": "v3.0", "batchSize": 100, "maxRetries": 2}, "expectedOutput": "Elastic update for ROR initiated. Check these following emails for the update", "method": "post", "category": "Integration", "public": false, "hidden": false}, {"message": "Return error when registry file is missing", "status": "400", "url": "/api/updateror", "FormData": true, "formFields": {"cmsVersion": "v3.0"}, "expectedOutput": {"error": "funding-registry file is required"}, "method": "post", "category": "Integration", "public": false, "hidden": false}]