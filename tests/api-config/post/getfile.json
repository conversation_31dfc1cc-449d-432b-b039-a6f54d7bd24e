[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getfile", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": {"code": 400, "message": "Input data validation"}, "message": "One or more mandatory inputs (project, customer, fileType) is missing."}, "category": "Exports", "public": false, "hidden": false}, {"message": "POST getfile with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/getfile/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 400, "message": "Input data validation"}, "message": "One or more mandatory inputs (project, customer, fileType) is missing."}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]