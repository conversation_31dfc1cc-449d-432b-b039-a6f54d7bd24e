[{"message": "Return error when we not passed the parameter or paramer as empty", "data": {"logName": "apptracker-Test", "logStatus": "failure", "logType": "application"}, "method": "post", "status": "500", "url": "/api/eslogger", "expectedOutput": {"status": "failed", "message": "Missing requested parameters"}, "category": "System"}, {"message": "Return error - Invalid credential", "data": {"message": "Test", "logName": "apptracker-Test", "logStatus": "failure", "logType": "application"}, "method": "post", "status": "401", "url": "/api/eslogger", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "category": "System"}, {"message": "POST eslogger (status 500)", "status": "500", "url": "/api/eslogger/", "method": "post", "data": {"logName": "apptracker-Test", "logStatus": "failure", "logType": "application"}, "expectedOutput": {"status": "failed", "message": "Missing requested parameters"}, "headers": {"content-type": "application/json"}, "category": "System"}, {"message": "POST eslogger", "status": "200", "url": "/api/eslogger/", "method": "post", "data": {"message": "Test", "logName": "apptracker-Test", "logStatus": "failure", "logType": "application"}, "expectedOutput": {"_index": "dev-logs", "_id": "q3sHPJgBVRK7ntaH29-Q", "_version": 1, "result": "created", "_shards": {"total": 2, "successful": 1, "failed": 0}, "_seq_no": 2155, "_primary_term": 68}, "headers": {"content-type": "application/json"}, "category": "System"}]