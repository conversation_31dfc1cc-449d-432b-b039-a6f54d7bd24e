[{"message": "Return error message when stage name is missing", "status": "200", "url": "/api/revokearticle", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June", "stageName": ""}, "expectedOutput": {"status": {"code": 204, "message": "Could not access article"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Return error message when Authentication is not given", "status": "401", "url": "/api/revokearticle", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjophthalmol-2018-312811", "stageName": "Author Review"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}]