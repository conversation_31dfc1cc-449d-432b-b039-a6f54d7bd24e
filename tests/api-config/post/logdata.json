[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/logdata", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"code": 400, "error": "logType is required"}, "category": "System", "public": false, "hidden": false}, {"message": "POST logdata with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/logdata/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"code": 400, "error": "logType is required"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]