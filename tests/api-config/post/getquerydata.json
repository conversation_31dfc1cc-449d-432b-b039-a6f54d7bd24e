[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getquerydata", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Configuration", "public": false, "hidden": false}, {"message": "POST getquerydata with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/getquerydata/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": 400, "message": "Missing parameters (table or index or data)"}, "headers": {"content-type": "application/json"}, "category": "Configuration", "public": false, "hidden": false}]