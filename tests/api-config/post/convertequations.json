[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/convertequations", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": {"code": "400", "message": "Invalid Input"}}, "category": "Exports", "public": false, "hidden": false}, {"message": "POST convertequations with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/convertequations/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": "400", "message": "Invalid Input"}}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]