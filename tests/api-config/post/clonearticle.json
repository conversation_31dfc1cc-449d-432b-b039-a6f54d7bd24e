[{"message": "Throw Error as the payload is not specified", "status": "204", "url": "/api/clonearticle", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST clonearticle with customer=bmj, project=bjophthalmol, doi=bjo-test-27June with doi=bjo-test-27June (status 204)", "status": "204", "url": "/api/clonearticle/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June", "clone": {"body": {}}}, "expectedOutput": {"errno": -2, "code": "ENOENT", "syscall": "open", "path": "cms/v3.0/js/review_content/config/customer/undefined/default/default_template.xml"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]