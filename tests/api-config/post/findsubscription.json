[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/findsubscription", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": [], "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "POST findsubscription with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/findsubscription/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": [], "headers": {"content-type": "application/json"}, "category": "Metadata and Reports", "public": false, "hidden": false}]