[{"message": "Assign user when project/Customer is not mentioned", "expectedOutput": "{\"status\":{\"code\":\"404\",\"message\":\"Internal error\"},\"message\":\"File not found\"}", "status": "403", "url": "/api/assignuser", "data": {"email": "<EMAIL>", "customer": "bmj", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "Manager <PERSON><PERSON>, <EMAIL>"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Re-assign user", "params": true, "param": {"parameters": [{"name": "content", "type": "string", "value": "Assigned to : Manager <PERSON><PERSON>, <EMAIL>"}]}, "status": "200", "url": "/api/assignuser?apiKey={APIKEY}", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "Manager <PERSON><PERSON>, <EMAIL>"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Unassign user", "param": {"parameters": [{"name": "content", "type": "string", "value": "Unassigned : Manager <PERSON><PERSON>, <EMAIL>"}]}, "status": "200", "url": "/api/assignuser?apiKey={APIKEY}", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": ""}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, doi=bmjgast-2019-000302 (status 403)", "status": "403", "url": "/api/assignuser/", "method": "post", "data": {"email": "<EMAIL>", "customer": "bmj", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "Manager <PERSON><PERSON>, <EMAIL>"}, "expectedOutput": {"status": {"code": "404", "message": "Internal error"}, "message": "File not found"}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjgast, doi=bmjgast-2019-000302 (status 403)", "status": "403", "url": "/api/assignuser/", "method": "post", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "Manager <PERSON><PERSON>, <EMAIL>", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 3, "updateReviewerAttempt": 0}, "expectedOutput": "Error While Updating elastic for bmjgast-2019-000302", "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjgast, doi=bmjgast-2019-000302 (status 403)", "status": "403", "url": "/api/assignuser/", "method": "post", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 3, "updateReviewerAttempt": 0}, "expectedOutput": "Error While Updating elastic for bmjgast-2019-000302", "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjoq, doi=bmjoq-************* (status 200)", "status": "200", "url": "/api/assignuser/", "method": "post", "data": {"email": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-*************", "assignedBy": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>@exeterpremedia.com", "assignTo": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>@exeterpremedia.com", "stageName": "Waiting for CE", "supportStageName": "", "toSkip": "assignee", "cardID": "0", "workflowType": ""}, "param": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "created": {"type": "string", "format": "date-time"}, "fullname": {"type": "string"}, "useremail": {"type": "string", "format": "email"}}, "additionalProperties": false}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjoq, doi=bmjoq-*************", "status": "200", "url": "/api/assignuser/", "method": "post", "data": {"email": "j<PERSON><PERSON><PERSON>@exeterpremedia.com", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-*************", "assignedBy": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>@exeterpremedia.com", "assignTo": "<PERSON><PERSON><PERSON>, j<PERSON><PERSON><PERSON>@exeterpremedia.com", "stageName": "Waiting for CE", "supportStageName": "", "toSkip": "assignee", "cardID": "0", "workflowType": "", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "param": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "created": {"type": "string", "format": "date-time"}, "fullname": {"type": "string"}, "useremail": {"type": "string", "format": "email"}}, "additionalProperties": false}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjgast, doi=bmjgast-2019-000302", "status": "200", "url": "/api/assignuser/", "method": "post", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "Manager <PERSON><PERSON>, <EMAIL>", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "param": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "created": {"type": "string", "format": "date-time"}, "fullname": {"type": "string"}, "useremail": {"type": "string", "format": "email"}}, "additionalProperties": false}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST assignuser with customer=bmj, project=bmjgast, doi=bmjgast-2019-000302", "status": "200", "url": "/api/assignuser/", "method": "post", "data": {"email": "<EMAIL>", "customer": "bmj", "project": "bmjgast", "assign": true, "doi": "bmjgast-2019-000302", "assignedBy": "Manager <PERSON><PERSON>, <EMAIL>", "assignTo": "", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "param": {"type": "object", "properties": {"content": {"type": "string"}, "id": {"type": "string", "format": "uuid"}, "created": {"type": "string", "format": "date-time"}, "fullname": {"type": "string"}, "useremail": {"type": "string", "format": "email"}}, "additionalProperties": false}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}]