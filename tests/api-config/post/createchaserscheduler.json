[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/createchaserscheduler", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST createchaserscheduler with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/createchaserscheduler/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"error": "Failed to create scheduler", "details": "Cannot read properties of undefined (reading 'customer')"}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]