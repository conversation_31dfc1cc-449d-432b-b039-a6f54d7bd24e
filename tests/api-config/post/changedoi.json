[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/changedoi", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": {"code": 500, "message": "Missing required parameters (customer, project, doiArray, regexPattern, replacementFormat, session.kuser.kuemail)."}}, "category": "Workflow", "public": false, "hidden": false}, {"message": "POST changedoi with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/changedoi/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": 500, "message": "Missing required parameters (customer, project, doiArray, regexPattern, replacementFormat, session.kuser.kuemail)."}}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]