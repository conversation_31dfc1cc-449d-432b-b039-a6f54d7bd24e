[{"message": "Successfully upload striking image for article cover", "status": "200", "url": "/api/uploadstrikingimage", "FormData": true, "formFields": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "strikingImage": {"type": "file", "path": "/_testFiles/inputfiles/post/uploadstrikingimage/cover-image.jpg"}, "imageCaption": "Retinal scan showing disease progression", "imageCredits": "<PERSON><PERSON>, University Hospital", "imagePosition": "cover", "replaceExisting": false}, "expectedOutput": {"status": "Success", "imageUrl": "https://s3.amazonaws.com/bucket/striking-images/cover-image.jpg", "imageId": "striking_123", "uploadedAt": "2024-01-15T11:00:00Z"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error when striking image file is missing", "status": "400", "url": "/api/uploadstrikingimage", "FormData": true, "formFields": {"customer": "jmir", "project": "jmir", "doi": "244444", "imageCaption": "Test caption"}, "expectedOutput": {"error": "strikingImage file is required"}, "method": "post", "category": "Exports", "public": false, "hidden": false}]