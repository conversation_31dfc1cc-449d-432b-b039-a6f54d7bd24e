[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getelasticdatabydate", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST getelasticdatabydate with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/getelasticdatabydate/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "One of the parameters is missing - customer, index, startdate, enddate", "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]