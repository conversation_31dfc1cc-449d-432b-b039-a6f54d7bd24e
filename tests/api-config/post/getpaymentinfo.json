[{"message": "Return error if we not passed the customer name", "status": "400", "url": "/api/getpaymentinfo", "data": {"project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjophthalmol-2019-314696", "updateTag": "false"}, "expectedOutput": {"status": {"code": 500, "message": "One or more of required parameters (customer id, project id) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return user data when user without have login credentials", "status": "401", "url": "/api/getpaymentinfo", "data": {"customer": "bmj", "project": "jramc", "doi": "jramc-2019-001187"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Users", "public": false, "hidden": false}]