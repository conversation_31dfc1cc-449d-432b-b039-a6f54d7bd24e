[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/commonfunction", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": "function to call is not defined", "category": "System", "public": false, "hidden": false}, {"message": "POST commonfunction with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/commonfunction/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "function to call is not defined", "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]