[{"message": "Successfully upload user profile image", "status": "200", "url": "/api/updateuserimage", "FormData": true, "formFields": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "userimage": {"type": "file", "path": "/_testFiles/inputfiles/post/updateuserimage/profile.jpg"}, "imageType": "profile", "replaceExisting": true}, "expectedOutput": {"status": "Success", "imageUrl": "https://s3.amazonaws.com/bucket/user-images/profile.jpg", "imageSize": 245760, "uploadedAt": "2024-01-15T10:30:00Z"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Upload user avatar image", "status": "200", "url": "/api/updateuserimage", "FormData": true, "formFields": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "userimage": {"type": "file", "path": "/_testFiles/inputfiles/post/updateuserimage/avatar.png"}, "imageType": "avatar", "replaceExisting": false}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error when image file is missing", "status": "400", "url": "/api/updateuserimage", "FormData": true, "formFields": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "imageType": "profile"}, "expectedOutput": {"error": "userimage file is required", "code": "MISSING_FILE"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error for invalid file type", "status": "400", "url": "/api/updateuserimage", "FormData": true, "formFields": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "userimage": {"type": "file", "path": "/_testFiles/inputfiles/post/updateuserimage/document.pdf"}}, "expectedOutput": {"error": "Invalid file type. Only PNG and JPEG files are allowed", "code": "LIMIT_FILE_TYPE"}, "method": "post", "category": "Users", "public": false, "hidden": false}]