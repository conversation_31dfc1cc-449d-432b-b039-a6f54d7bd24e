[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/acceptrejectchanges", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "POST acceptrejectchanges with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/acceptrejectchanges/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "Already accepted", "headers": {"content-type": "application/json"}, "category": "Articles", "public": false, "hidden": false}]