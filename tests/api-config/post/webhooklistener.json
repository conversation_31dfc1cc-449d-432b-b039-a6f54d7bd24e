[{"message": "Successfully process submission complete webhook", "status": "200", "url": "/api/webhooklistener", "data": {"eventType": "SUBMISSION_COMPLETE", "status": "COMPLETE", "id": "submission_12345", "timestamp": "2024-01-15T14:30:00Z", "source": "plagiarism-service", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "submissionId": "sub_12345", "reportUrl": "https://reports.example.com/12345"}, "signature": "sha256=abc123def456", "retryCount": 0}, "expectedOutput": {"status": "Success", "message": "Report generated!", "processedAt": "2024-01-15T14:30:15Z", "eventId": "submission_12345"}, "method": "post", "category": "Integration", "public": false, "hidden": false}, {"message": "Return error for missing required parameters", "status": "400", "url": "/api/webhooklistener", "data": {"eventType": "SUBMISSION_COMPLETE", "timestamp": "2024-01-15T16:30:00Z"}, "expectedOutput": {"error": "Missing required parameters: status, id", "eventType": "SUBMISSION_COMPLETE", "retryable": false}, "method": "post", "category": "Integration", "public": false, "hidden": false}]