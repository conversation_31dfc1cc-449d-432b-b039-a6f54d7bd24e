[{"message": "Return error when customer name is not given", "status": "200", "url": "/api/updatecopyright", "data": {"project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjophthalmol-2019-314537"}, "expectedOutput": {"status": {"code": 500, "message": "One or more of required parameters (customer id, project id) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "method": "post", "category": "References", "public": false, "hidden": false}]