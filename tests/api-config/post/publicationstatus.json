[{"message": "Throw Error as the expected payload is not specified", "status": "400", "url": "/api/publicationstatus", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "method": "post", "expectedOutput": {"status": {"code": 500, "message": "One or more of required parameters (customer id, project id, article id) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "category": "Workflow", "public": false, "hidden": false}]