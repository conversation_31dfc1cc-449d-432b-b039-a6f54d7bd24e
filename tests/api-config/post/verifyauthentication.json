[{"message": "Successfully verify JWT authentication token", "status": "200", "url": "/api/verifyauthentication", "data": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "authType": "jwt", "email": "<EMAIL>", "validatePermissions": true, "requiredRole": "reviewer"}, "expectedOutput": {"status": "Success", "isValid": true, "userId": "user123", "email": "<EMAIL>", "role": "reviewer", "permissions": ["read", "write", "review"], "expiresAt": "2024-02-15T10:00:00Z", "refreshRequired": false}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error for invalid token", "status": "401", "url": "/api/verifyauthentication", "data": {"token": "invalid_token", "authType": "jwt"}, "expectedOutput": {"error": "Invalid token", "code": "INVALID_TOKEN"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/verifyauthentication", "data": {"authType": "jwt"}, "method": "post", "category": "Users", "public": false, "hidden": false}]