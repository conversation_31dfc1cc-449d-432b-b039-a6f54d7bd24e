[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/multiqueuecount", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"error": "Missing required parameters: customer or customerFilterList."}, "category": "Workflow", "public": false, "hidden": false}, {"message": "POST multiqueuecount with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/multiqueuecount/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"error": "Missing required parameters: customer or customerFilterList."}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]