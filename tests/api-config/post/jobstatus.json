[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/jobstatus", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST jobstatus with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/jobstatus/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": 400, "message": "Error in getting job status", "error": "Unsupported protocol undefined:"}, "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]