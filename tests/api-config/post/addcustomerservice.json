[{"message": "Throw Error as the payload is not specified", "status": "500", "url": "/api/addcustomerservice", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "POST addcustomerservice with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 500)", "status": "500", "url": "/api/addcustomerservice/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "Parameters missing!", "headers": {"content-type": "application/json"}, "category": "Metadata and Reports", "public": false, "hidden": false}]