[{"message": "Signs off the call, performs certain actions based on the stage, and returns a success message upon completion.", "status": "200", "url": "/api/signoff", "data": {"customer": "kriya", "project": "kriya", "doi": "kriya_2024_226", "role": "reviewer", "currStage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "to": "<EMAIL>", "recipientName": "<PERSON>", "stagename": "Reviewer Check", "version": "Original", "skipAssignTo": "true", "currPageName": "peerreview_editor", "emailBody": "<p>Dear <PERSON>,</p><p>I would like to invite you to review the manuscript titled “<span class=\"article-title\">Testing Multiple Email - Authors</span>” which has been submitted to <i><span class=\"journal-title\">Kriya</span></i>. I believe you would serve as an excellent reviewer to the manuscript based on your expertise. We ask that reviewers return their comments within 14 days of agreeing to review. However, if you need more time, please let us know.</p><p>Below is the manuscript abstract for your reference.</p><div class=\"abstract-div\"><span class=\"h1\" style=\"display:block;font-weight:bold;font-size:1rem\">Abstract</span><span class=\"p\" style=\"display:block;margin-bottom: .5rem;\">The accurate forecasting of oil field production rate is a crucial indicator for each oil field’s successful development, but due to the complicated reservoir conditions and unknown underground environment, the high accuracy of production rate forecasting is a popular challenge. To find a low time consumption and high accuracy method for forecasting production rate, the current paper proposes a hybrid model, Simulated Annealing- Long Short-Term Memory network (SA-LSTM), based on the daily oil production rate of tight reservoirs with the in-situ data of injection and production rates in fractures. Furtherly, forecasting results are compared with the numerical simulation model output. The LSTM can effectively learn time-sequence problems, while simulated annealing (SA) can optimize the hyperparameters (learning rate, batch size, decay rate) in LSTM to achieve higher accuracy. By conducting the optimized hyperparameters into the LSTM model, the daily oil production rate can be forecasted well.After training and predicting on existing production data, three different methods were used to forecast daily oil production for the next 300 days. The results were then validated using numerical simulations to compare the forecasting of LSTM and SA-LSTM. The results show that SA-LSTM can more efficiently and accurately predict daily oil production. The fitting accuracies of the three methods are as follows: numerical reservoir simulation (96.2%), LSTM (98.1%), and SA-LSTM (98.7%). The effectiveness of SA-LSTM in production rate is particularly outstanding.The 20 actual producing Wells in tight reservoirs were also well validated.</span></div><p contenteditable=\"false\" style=\"background-color: lightgray;padding: 5px;\">If you agree to review the manuscript, please click <i><a contenteditable=\"false\" style=\"background: #FF5722;padding: 3px 6px;border-radius: 3px;color: #fff !important;\" data-link-page=\"peerreview_editor\" href=\"{acceptLink}\">Agree to review</a>.</i></p><p contenteditable=\"false\" style=\"background-color: lightgray;padding: 5px;\">If you are unable to review the article, please click <i><a contenteditable=\"false\" style=\"background: #FF5722;padding: 3px 6px;border-radius: 3px;color: #fff !important;\" href=\"{rejectLink}\">Decline to review</a></i>.</p><p>Please note that the online peer review platform is currently not supported using Internet Explorer. Please review using Google Chrome.</p><p>In the following link you will find a comprehensive guide for <i><span class=\"journal-title\">Kriya</span></i>’s peer reviewers: <a href=\"https://pubs.geoscienceworld.org/kriya/pages/reviewer-guidelines\">https://pubs.geoscienceworld.org/kriya/pages/reviewer-guidelines</a></p><p>You can also refer to COPE (Committee on Publication Ethics) “Ethical guidelines for peer reviewers”: <a href=\"https://doi.org/10.24318/cope.2019.1.9\">https://doi.org/10.24318/cope.2019.1.9</a></p><p>Please decline this invitation if a potential conflict of interest exists between yourself and either the authors or the subject of this manuscript. Potential conflicts can be financial or intellectual and include recent collaboration or sharing the same institution as the authors. For more information about our conflicts of interest policies, please see <a href=\"https://pubs.geoscienceworld.org/kriya/pages/code-of-publishing-ethics\">https://pubs.geoscienceworld.org/kriya/pages/code-of-publishing-ethics</a></p><p>Best regards,</p><p></p><p><i><span class=\"journal-title\">Kriya</span></i></p>", "editedTo": "<EMAIL>", "replyTo": "<EMAIL>", "subject": "Invitation to review manuscript kriya_2024_226 for <PERSON><PERSON>"}, "expectedOutput": {"status": {"code": 200, "message": "data received"}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}]