[{"message": "Successfully upload Excel table to AWS", "status": "200", "url": "/api/uploadtabletoaws", "FormData": true, "formFields": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "tableFile": {"type": "file", "path": "/_testFiles/inputfiles/post/uploadtabletoaws/data-table.xlsx"}, "tableId": "table_001", "tableCaption": "Patient demographics and clinical outcomes", "tableFormat": "xlsx", "awsBucket": "bmj-tables", "awsPath": "annrheumdis/2024/", "replaceExisting": false}, "expectedOutput": {"status": "Success", "awsUrl": "https://s3.amazonaws.com/bmj-tables/annrheumdis/2024/data-table.xlsx", "tableId": "table_001", "uploadedAt": "2024-01-15T12:00:00Z", "fileSize": 52480}, "method": "post", "category": "References", "public": false, "hidden": false}, {"message": "Return error when table file is missing", "status": "400", "url": "/api/uploadtabletoaws", "FormData": true, "formFields": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "tableCaption": "Missing file test"}, "expectedOutput": {"error": "tableFile is required"}, "method": "post", "category": "References", "public": false, "hidden": false}]