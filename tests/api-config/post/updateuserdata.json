[{"message": "Return error if we not passed the customer name", "status": "200", "url": "/api/updateuserdata", "data": {"email": "<EMAIL>", "city": "salem"}, "param": {"parameters": [{"name": "statusCode", "type": "number", "value": 400}]}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error if we provide invalid user", "status": "400", "url": "/api/updateuserdata", "data": {"email": "<EMAIL>", "lastName": "<PERSON><PERSON><PERSON>", "customerName": "bmj"}, "expectedOutput": {"message": "user not found"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return user data when user without have login credentials", "status": "401", "url": "/api/updateuserdata", "data": {"email": "<EMAIL>", "lastName": "<PERSON><PERSON><PERSON>", "customerName": "bmj"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return data if we pass all parameters", "status": "200", "url": "/api/updateuserdata", "data": {"email": "<EMAIL>", "lastName": "<PERSON><PERSON><PERSON>", "modifiedData": true}, "method": "post", "category": "Users", "public": false, "hidden": false}]