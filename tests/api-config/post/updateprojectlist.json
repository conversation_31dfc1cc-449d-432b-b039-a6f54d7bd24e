[{"message": "Return error when customer name missing", "method": "post", "data": {"customer": ""}, "expectedOutput": {"status": 400, "message": "Customer name missing"}, "status": "400", "url": "/api/updateprojectlist", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "Return error when customer name is wrong", "method": "post", "data": {"customer": "sabari"}, "expectedOutput": {"status": 400, "message": "Unable to find given customer name"}, "status": "400", "url": "/api/updateprojectlist", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "Return error when user is not authorised", "method": "post", "data": {"customer": "bmj"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "status": "401", "url": "/api/updateprojectlist", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "POST updateprojectlist with customer=bmj", "status": "200", "url": "/api/updateprojectlist/", "method": "post", "data": {"customer": "bmj"}, "expectedOutput": {"status": 200, "message": ["bmj updated"], "output": []}, "headers": {"content-type": "application/json"}, "category": "Metadata and Reports", "public": false, "hidden": false}]