[{"message": "Throw Error as the incorrect payload is not specified", "status": "400", "url": "/api/getarticletype", "data": {"customer": "unknown"}, "method": "post", "expectedOutput": "Could not find config file", "category": "Configuration", "public": false, "hidden": false}, {"message": "POST getarticletype with customer=unknown (status 400)", "status": "400", "url": "/api/getarticletype/", "method": "post", "data": {"customer": "unknown"}, "expectedOutput": "Could not find config file", "headers": {"content-type": "application/json"}, "category": "Configuration", "public": false, "hidden": false}]