[{"message": "POST updatedata with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709, type=mergeData", "status": "200", "url": "/api/updatedata/", "method": "post", "data": {"doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "functionToCall": "updateXpath", "type": "mergeData", "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]", "content": "<stage><job-logs><log><status type=\"system\">logged-off</status></log></job-logs></stage>"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": 200, "message": "success"}, "message": {"code": 200, "message": [{"code": 200, "message": {"process": "update", "update": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]"}}]}}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST updatedata with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/updatedata/", "method": "post", "data": {"fastTrackComment": "Comments: Fasttrack article<br>", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "stageName": "Waiting for CE", "priority": "on", "cardID": "0", "data": {"process": "update", "update": "//workflow/priority", "content": "<priority requested-date=\"2025-07-25 08:24:25\">on</priority>", "append": "//workflow"}, "notify": {"notifyTitle": "Fast Track article", "successText": "Flag status for bmjoq-2023-00235709 had been updated", "errorText": "Error while update flag status for bmjoq-2023-00235709"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": 200, "message": "success"}, "message": {"code": 200, "message": [{"code": 200, "message": {"process": "update", "update": "//workflow/priority", "append": "//workflow"}}]}}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST updatedata with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/updatedata/", "method": "post", "data": {"fastTrackComment": "", "customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "stageName": "Waiting for CE", "priority": "off", "cardID": "0", "data": {"process": "update", "update": "//workflow/priority", "content": "<priority requested-date=\"2025-07-25 08:24:29\">off</priority>", "append": "//workflow"}, "notify": {"notifyTitle": "Fast Track article", "successText": "Flag status for bmjoq-2023-00235709 had been updated", "errorText": "Error while update flag status for bmjoq-2023-00235709"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": 200, "message": "success"}, "message": {"code": 200, "message": [{"code": 200, "message": {"process": "update", "update": "//workflow/priority", "append": "//workflow"}}]}}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST updatedata with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709, type=mergeData", "status": "200", "url": "/api/updatedata/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "type": "mergeData", "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]", "content": "<stage><job-logs><log data-insert=\"true\" data-doi=\"bmjoq-2023-00235709\" data-project=\"bmjoq\" data-customer=\"bmj\"><username><PERSON><PERSON><PERSON></username><useremail>j<PERSON><PERSON><PERSON>@exeterpremedia.com</useremail><start-date>2025-07-25</start-date><start-time>09:18:27</start-time><end-date>2025-07-25</end-date><end-time>09:18:27</end-time><status type=\"system\">in-active</status></log></job-logs></stage>"}, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": {"code": 200, "message": "success"}, "message": {"code": 200, "message": [{"code": 200, "message": {"process": "update", "update": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]"}}]}}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST updatedata with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709, type=mergeData", "status": "200", "url": "/api/updatedata/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "type": "mergeData", "skipElasticUpdate": "true", "data": {"process": "update", "xpath": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]", "content": "<stage><job-logs><log><end-date>2025-07-25</end-date><end-time>10:38:22</end-time></log></job-logs></stage>"}}, "expectedOutput": {"status": {"code": 200, "message": "success"}, "message": {"code": 200, "message": [{"code": 200, "message": {"process": "update", "update": "//workflow/stage[name[.=\"Waiting for CE\"] and status[.=\"in-progress\"]]"}}]}}, "category": "Articles", "public": false, "hidden": false}]