[{"message": "Successfully update user article assignment", "status": "200", "url": "/api/updateuserarticle", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "email": "<EMAIL>", "role": "reviewer", "name": "Dr. <PERSON>", "status": "assigned", "message": "Review assignment confirmed", "version": "R1", "title": "Clinical Study on Eye Disease", "keywords": ["ophthalmology", "clinical trial", "eye disease"], "assignedStartDate": "2024-01-15T09:00:00Z", "assignedEndDate": "2024-02-15T17:00:00Z", "sourceAssignment": "Manual", "to": "<EMAIL>"}, "expectedOutput": {"status": "Success", "elasticId": "elastic123", "eventTriggered": true}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Update user article with author role", "status": "200", "url": "/api/updateuserarticle", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "email": "<EMAIL>", "role": "author", "name": "Dr. <PERSON>", "status": "submitted"}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/updateuserarticle", "data": {"customer": "jmir", "project": "jmir", "email": "<EMAIL>"}, "expectedOutput": {"error": "Missing required parameters: doi, role"}, "method": "post", "category": "Articles", "public": false, "hidden": false}]