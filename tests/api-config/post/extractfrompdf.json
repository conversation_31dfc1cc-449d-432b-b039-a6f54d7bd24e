[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/extractfrompdf", "data": {"customer": "bmj"}, "method": "post", "expectedOutput": "Extracting...\naborted", "category": "Exports", "public": false, "hidden": false}, {"message": "POST extractfrompdf with customer=bmj, type=output (status 500)", "status": "500", "url": "/api/extractfrompdf/", "method": "post", "data": {"customer": "bmj", "remoteAddress": "::1", "siteName": "localhost:3001", "type": "output", "env": "development", "dbBasePath": "integration"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]