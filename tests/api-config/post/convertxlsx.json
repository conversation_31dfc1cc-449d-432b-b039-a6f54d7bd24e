[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/convertxlsx", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "POST convertxlsx with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/convertxlsx/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": {"code": "400", "message": "ERROR: xlsx file length is 0"}, "step": "convert xlsx"}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]