[{"message": "Return success message if we passed the customer", "status": "200", "url": "/api/generatecustomconfig", "data": {"customer": "bmj"}, "expectedOutput": {"message": "bmj created"}, "method": "post", "category": "Configuration"}, {"message": "Return Error message if we passed the invalid customer", "status": "200", "url": "/api/generatecustomconfig", "data": {"customer": "sabari"}, "expectedOutput": {"message": "sabari Config not found"}, "method": "post", "category": "Configuration"}, {"message": "POST generatecustomconfig with customer=bmj", "status": "200", "url": "/api/generatecustomconfig/", "method": "post", "data": {"customer": "bmj"}, "expectedOutput": {"message": "bmj created"}, "headers": {"content-type": "application/json"}, "category": "Configuration"}, {"message": "POST generatecustomconfig with customer=sabari", "status": "200", "url": "/api/generatecustomconfig/", "method": "post", "data": {"customer": "sabari"}, "expectedOutput": {"message": "sabari Config not found"}, "headers": {"content-type": "application/json"}, "category": "Configuration"}]