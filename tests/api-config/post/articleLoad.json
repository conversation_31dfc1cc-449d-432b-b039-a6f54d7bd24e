[{"message": "Successfully load article data", "status": "200", "url": "/api/articleload", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "loadType": "full", "includeMetadata": true, "includeContent": true}, "expectedOutput": {"status": "Success", "articleData": {}, "loadedAt": "2024-01-15T10:00:00Z"}, "method": "post", "category": "Workflow"}, {"message": "Load article with metadata only", "status": "200", "url": "/api/articleload", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "loadType": "metadata", "includeMetadata": true, "includeContent": false}, "method": "post", "category": "Workflow"}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/articleload", "data": {"customer": "jmir", "project": "jmir"}, "expectedOutput": {"error": "Missing required parameter: doi"}, "method": "post", "category": "Workflow"}]