[{"message": "Successfully update reviewer status to accepted", "status": "200", "url": "/api/updatereviewer", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "userMailID": "<EMAIL>", "reviewerStatus": "accepted", "reviewerComments": "I accept this review assignment", "updateUserXML": true, "updateUserArticle": true, "version": "R1"}, "expectedOutput": {"status": {"code": 200, "message": "Reviewer updated."}, "step": "updateReviewer"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Update reviewer status to declined with decision", "status": "200", "url": "/api/updatereviewer", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "userMailID": "<EMAIL>", "reviewerStatus": "declined", "reviewerComments": "Unable to review due to conflict of interest", "reviewerDecision": "decline", "processDecisionCheck": true}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/updatereviewer", "data": {"customer": "jmir", "project": "jmir"}, "expectedOutput": {"status": {"code": 400, "message": "Missing required parameters: doi, userMailID"}}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "Return error for invalid email format", "status": "400", "url": "/api/updatereviewer", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "userMailID": "invalid-email"}, "method": "post", "category": "Users", "public": false, "hidden": false}, {"message": "POST updatereviewer with customer=jmir, project=jmir (status 400)", "status": "400", "url": "/api/updatereviewer/", "method": "post", "data": {"customer": "jmir", "project": "jmir"}, "expectedOutput": {"status": {"code": 500, "message": "Email address not provided. Unexpected input"}, "step": "updatereviewer"}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}, {"message": "POST updatereviewer with customer=bmj, project=bmjdrc, doi=bmjdrc-2018-000550 (status 400)", "status": "400", "url": "/api/updatereviewer/", "method": "post", "data": {"customer": "bmj", "project": "bmjdrc", "doi": "bmjdrc-2018-000550", "userMailID": "invalid-email"}, "expectedOutput": {"status": {"code": 500, "message": "Reviewer status not provided. Unexpected input"}, "step": "updatereviewer"}, "headers": {"content-type": "application/json"}, "category": "Users", "public": false, "hidden": false}]