[{"message": "Return error when we not passed the customer name", "status": "200", "url": "/api/exportallarticles", "data": {"project": "thorax<PERSON><PERSON>", "fileName": "thoraxjnl_74_2"}, "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "Return error when we not passed the project name", "status": "200", "url": "/api/exportallarticles", "data": {"customer": "bmj", "doi": "thoraxjnl_74_2"}, "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "method": "post", "category": "Exports", "public": false, "hidden": false}, {"message": "POST exportallarticles with project=thoraxjnl", "status": "200", "url": "/api/exportallarticles/", "method": "post", "data": {"project": "thorax<PERSON><PERSON>", "fileName": "thoraxjnl_74_2"}, "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}, {"message": "POST exportallarticles with customer=bmj, doi=thoraxjnl_74_2", "status": "200", "url": "/api/exportallarticles/", "method": "post", "data": {"customer": "bmj", "doi": "thoraxjnl_74_2"}, "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "headers": {"content-type": "application/json"}, "category": "Exports", "public": false, "hidden": false}]