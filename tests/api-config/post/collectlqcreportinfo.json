[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/collectlqcreportinfo", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": true, "category": "System", "public": false, "hidden": false}, {"message": "POST collectlqcreportinfo with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/collectlqcreportinfo/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": true, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]