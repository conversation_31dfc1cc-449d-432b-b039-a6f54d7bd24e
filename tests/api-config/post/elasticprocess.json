[{"message": "Throw Error as the payload is not specified", "status": "500", "url": "/api/elasticprocess", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": {"status": "failed", "message": "Missing process Type"}, "category": "System", "public": false, "hidden": false}, {"message": "POST elasticprocess with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 500)", "status": "500", "url": "/api/elasticprocess/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"status": "failed", "message": "Missing process Type"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}, {"message": "POST elasticprocess", "status": "200", "url": "/api/elasticprocess/", "method": "post", "data": {"index": "dev-user-preferences", "table": "user-preference", "processType": "getDocument", "id": "00ea2cb1-30e0-41b2-af40-fd82ebbf1ef6"}, "expectedOutput": {"msg": "Not Found", "path": "/dev-user-preferences/_doc/00ea2cb1-30e0-41b2-af40-fd82ebbf1ef6", "query": {}, "statusCode": 404, "response": "{\"_index\":\"dev-user-preferences\",\"_id\":\"00ea2cb1-30e0-41b2-af40-fd82ebbf1ef6\",\"found\":false}"}, "category": "System", "public": false, "hidden": false}]