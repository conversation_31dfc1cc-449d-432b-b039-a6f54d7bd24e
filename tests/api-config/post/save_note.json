[{"message": "add notes when user is authenticated", "status": "400", "data": {"content": {"content": "tester", "fullname": "<PERSON><PERSON><PERSON> V<PERSON>", "created": "2019-05-03 13:45:51"}, "doi": "bjophthalmol-2019-314081", "customer": "bmj", "project": "bjo", "noteType": "", "notesCount": 6}, "url": "/api/save_note", "method": "post", "param": {"parameters": [{"name": "content", "type": "string", "value": "tester"}, {"name": "fullname", "type": "string", "value": "<PERSON><PERSON><PERSON> V<PERSON>"}]}, "expectedOutput": {"status": 400, "message": "MESSAGE: Error occured during workflow production notes fetching and trying to save notes "}, "category": "Articles", "public": false, "hidden": false}, {"message": "add notes when user is authenticated", "status": "400", "data": {"content": [{"content": "tester", "fullname": "<PERSON><PERSON><PERSON><PERSON>", "created": "2019-05-03 13:45:51"}], "doi": "bjophthalmol-2019-314081", "customer": "bmj", "project": "bjo", "noteType": "", "notesCount": 6}, "url": "/api/save_note", "method": "post", "param": {"parameters": [{"name": "content", "type": "string", "value": "tester"}, {"name": "fullname", "type": "string", "value": "<PERSON><PERSON><PERSON><PERSON>"}]}, "expectedOutput": {"status": 400, "message": "MESSAGE: Error occured during workflow production notes fetching and trying to save notes "}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article has been fast tracked<br>Comments: Test fasttrack<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:09:26"}], "doi": "aaas-demo-20205-2003", "customer": "aaas", "project": "aaas", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article has been fast tracked<br>Comments: Test fasttrack<br>", "id": "ba1a3ca0-3dd9-4877-b274-29354f797686", "created": "2025-07-25 08:09:26", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article removed from fast track<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:09:31"}], "doi": "aaas-demo-20205-2003", "customer": "aaas", "project": "aaas", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article removed from fast track<br>", "id": "91fb5da5-2359-4beb-8e36-df6ed1b63c77", "created": "2025-07-25 08:09:31", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article signed-off from Pre-editing to <strong> Hold </strong><br>Reason: On hold awaiting payment <br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:09:41"}], "doi": "aaas-demo-20205-2003", "customer": "aaas", "project": "aaas", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article signed-off from Pre-editing to <strong> Hold </strong><br>Reason: On hold awaiting payment <br>", "id": "019365e0-1f22-490d-894b-92bc287df087", "created": "2025-07-25 08:09:41", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=aaas, project=aaas, doi=aaas-demo-20205-2003", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article signed-off from <strong>Hold</strong> to Pre-editing<br>Comments: Removing hold<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:19:50"}], "doi": "aaas-demo-20205-2003", "customer": "aaas", "project": "aaas", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article signed-off from <strong>Hold</strong> to Pre-editing<br>Comments: Removing hold<br>", "id": "09c2fe88-8935-4014-a7e5-105231fe9382", "created": "2025-07-25 08:19:50", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article has been fast tracked<br>Comments: Fasttrack article<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:24:25"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article has been fast tracked<br>Comments: Fasttrack article<br>", "id": "6dd601e4-90f2-4a9f-9e13-8e416c2ce62d", "created": "2025-07-25 08:24:25", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article removed from fast track<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:24:29"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article removed from fast track<br>", "id": "c24fcb80-7fef-4e6f-b76f-6f167c121972", "created": "2025-07-25 08:24:29", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article signed-off from Waiting for CE to <strong> Hold </strong><br>Reason: On hold awaiting payment <br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:24:34"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article signed-off from Waiting for CE to <strong> Hold </strong><br>Reason: On hold awaiting payment <br>", "id": "277fe203-8dc9-4da2-8f6a-6365884c3e65", "created": "2025-07-25 08:24:34", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "Article signed-off from <strong>Hold</strong> to Waiting for CE<br>Comments: Payment received<br>", "fullname": "<PERSON><PERSON><PERSON>", "created": "2025-07-25 08:32:47"}], "doi": "bmjoq-2023-00235709", "customer": "bmj", "project": "bmjoq", "noteType": "workflow", "logging": false, "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"content": "Article signed-off from <strong>Hold</strong> to Waiting for CE<br>Comments: Payment received<br>", "id": "2c133441-829a-4023-83b1-7bb1b86f221f", "created": "2025-07-25 08:32:47", "fullname": "<PERSON><PERSON><PERSON>", "useremail": "j<PERSON><PERSON><PERSON>@exeterpremedia.com"}, "headers": {"content-type": "application/json; charset=UTF-8"}, "category": "Articles", "public": false, "hidden": false}, {"message": "POST save_note with customer=bmj, project=bjo, doi=bjophthalmol-2019-314081 (status 400)", "status": "400", "url": "/api/save_note/", "method": "post", "data": {"content": [{"content": "tester", "fullname": "<PERSON><PERSON><PERSON> V<PERSON>", "created": "2019-05-03 13:45:51"}], "doi": "bjophthalmol-2019-314081", "customer": "bmj", "project": "bjo", "noteType": "", "notesCount": 6, "logging": false}, "expectedOutput": {"status": 400, "message": "MESSAGE: Error occured during workflow production notes fetching and trying to save notes "}, "headers": {"content-type": "application/json"}, "category": "Articles", "public": false, "hidden": false}]