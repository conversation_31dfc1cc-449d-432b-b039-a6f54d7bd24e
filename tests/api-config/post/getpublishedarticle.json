[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getpublishedarticle", "data": {"customer": "bmj"}, "method": "post", "expectedOutput": {"status": {"code": 400, "message": "One or more of required parameters (customer id, project id, doi id) is/are not provided. requested action on stage cannot be done. Unexpected input"}}, "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "POST getpublishedarticle with customer=bmj", "status": "200", "url": "/api/getpublishedarticle/", "method": "post", "data": {"customer": "bmj"}, "expectedOutput": {"status": {"code": 400, "message": "One or more of required parameters (customer id, project id, doi id) is/are not provided. requested action on stage cannot be done. Unexpected input"}}, "headers": {"content-type": "application/json"}, "category": "Metadata and Reports", "public": false, "hidden": false}]