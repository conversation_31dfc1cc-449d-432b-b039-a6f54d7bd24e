[{"message": "Throw Error as the payload is not specified", "status": "204", "url": "/api/autocontentstructuring", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "expectedOutput": "", "category": "Workflow", "public": false, "hidden": false}, {"message": "POST autocontentstructuring with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 204)", "status": "204", "url": "/api/autocontentstructuring/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "Step function execution failed", "headers": {"content-type": "application/json"}, "category": "Workflow", "public": false, "hidden": false}]