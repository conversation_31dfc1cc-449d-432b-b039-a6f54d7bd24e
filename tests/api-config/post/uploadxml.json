[{"message": "POST uploadxml with customer=bmj, project=annrheumdis, doi=annrheumdis-2020-218398", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "annrheumdis-2020-218398", "doi": "annrheumdis-2020-218398", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------339240598491225668276427"}, "category": "Exports"}, {"message": "POST uploadxml with customer=bmj, project=bjophthalmol, doi=bjo-test-27June", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "comment": "bjo-test-27June", "doi": "bjo-test-27June", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------874765848538063726939758"}, "category": "Exports"}, {"message": "POST uploadxml with customer=bmj, project=bmjgast, doi=bmjgast-2019-000302", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "bmj", "project": "bmjgast", "comment": "bmjgast-2019-000302", "doi": "bmjgast-2019-000302", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------281345362553378454712901"}, "category": "Exports"}, {"message": "POST uploadxml with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "comment": "bmjoq-2023-00235709", "doi": "bmjoq-2023-00235709", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------650399220007355424908355"}, "category": "Exports"}, {"message": "POST uploadxml with customer=elife, project=elife, doi=64509", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "elife", "project": "elife", "comment": "64509", "doi": "64509", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------601398483578766087924615"}, "category": "Exports"}, {"message": "POST uploadxml with customer=elife, project=elife, doi=64589", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "elife", "project": "elife", "comment": "64589", "doi": "64589", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------125190092884301778948789"}, "category": "Exports"}, {"message": "POST uploadxml with customer=jmir, project=jmir, doi=244444", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "jmir", "project": "jmir", "comment": "244444", "doi": "244444", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------731103040912354387657532"}, "category": "Exports"}, {"message": "POST uploadxml with customer=kriya, project=kriya, doi=kriya_2025_11120", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "kriya", "project": "kriya", "comment": "kriya_2025_11120", "doi": "kriya_2025_11120", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------390547880393699893909565"}, "category": "Exports"}, {"message": "POST uploadxml with customer=kriya, project=kriya, doi=kriya_2025_435", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "kriya", "project": "kriya", "comment": "kriya_2025_435", "doi": "kriya_2025_435", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------794134825128294915934452"}, "category": "Exports"}, {"message": "POST uploadxml with customer=kriya, project=kriya, doi=kriya_2025_456", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "kriya", "project": "kriya", "comment": "kriya_2025_456", "doi": "kriya_2025_456", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------715789339788491297833005"}, "category": "Exports"}, {"message": "POST uploadxml with customer=ppl, project=bsr, doi=bsr-2025-50609", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "ppl", "project": "bsr", "comment": "bsr-2025-50609", "doi": "bsr-2025-50609", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------826629287967793830752966"}, "category": "Exports"}, {"message": "POST uploadxml with customer=ppl, project=bst, doi=bst-2024-1243", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "ppl", "project": "bst", "comment": "bst-2024-1243", "doi": "bst-2024-1243", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------005907635191698730541133"}, "category": "Exports"}, {"message": "POST uploadxml with customer=ppl, project=test_journal, doi=test_journal_2025_101", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "ppl", "project": "test_journal", "comment": "test_journal_2025_101", "doi": "test_journal_2025_101", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------201834087140857395328628"}, "category": "Exports"}, {"message": "POST uploadxml with customer=ppl, project=test_journal, doi=test_journal_2025_120", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "ppl", "project": "test_journal", "comment": "test_journal_2025_120", "doi": "test_journal_2025_120", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------202017518373818276962749"}, "category": "Exports"}, {"message": "POST uploadxml with customer=ppl, project=test_journal, doi=test_journal_2025_125", "status": "200", "url": "/api/uploadxml/", "method": "post", "data": {"customer": "ppl", "project": "test_journal", "comment": "test_journal_2025_125", "doi": "test_journal_2025_125", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": "file upload success", "headers": {"content-type": "multipart/form-data; boundary=--------------------------529179753974450940204462"}, "category": "Exports"}]