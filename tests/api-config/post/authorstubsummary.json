[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/authorstubsummary", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "POST authorstubsummary with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/authorstubsummary/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": "processType required", "headers": {"content-type": "application/json"}, "category": "Articles", "public": false, "hidden": false}]