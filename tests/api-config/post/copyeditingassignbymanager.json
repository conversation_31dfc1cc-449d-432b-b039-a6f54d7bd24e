[{"message": "Successfully assign copy editor by manager", "status": "200", "url": "/api/copyeditingassignbymanager", "data": {"customer": "bmj", "project": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "annrheumdis-2018-214280", "msID": "annrheumdis-2018-214280", "accountID": 40, "service": "Copyediting", "quantity": "5718", "vendorMailID": "<EMAIL>", "userName": "Copyeditor 3", "pageCount": 23, "assignedBy": "<EMAIL>", "dueDate": "2024-02-15T17:00:00Z"}, "expectedOutput": {"status": {"code": 200, "message": "data received", "data": "{\"msID\":\"annrheumdis-2018-214280\",\"accountID\":40,\"service\":\"Copyediting\",\"quantity\":\"5718\",\"vendorMailID\":\"<EMAIL>\",\"userName\":\"Copyeditor 3\",\"pageCount\":23}"}}, "method": "post", "category": "Workflow"}, {"message": "Assign copy editor for PPL BST article", "status": "200", "url": "/api/copyeditingassignbymanager", "data": {"customer": "ppl", "project": "bst", "doi": "bst-2024-1243", "msID": "bst-2024-1243", "accountID": 45, "service": "Copyediting", "quantity": "4200", "vendorMailID": "<EMAIL>", "userName": "Senior Editor", "pageCount": 18}, "method": "post", "category": "Workflow"}, {"message": "Return error when required parameters are missing", "status": "400", "url": "/api/copyeditingassignbymanager", "data": {"customer": "jmir", "project": "jmir", "doi": "244444"}, "expectedOutput": {"error": "Missing required parameters: msID, accountID, service"}, "method": "post", "category": "Workflow"}]