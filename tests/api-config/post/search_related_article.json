[{"message": "Return error when input data is missing", "status": "200", "url": "/api/search_related_article", "data": "", "expectedOutput": "ERROR:Input Data missing", "method": "post", "category": "Articles", "public": false, "hidden": false}, {"message": "Return error when user not authorized", "status": "401", "url": "/api/search_related_article", "data": {"customer": "ima", "project": "ijm", "doi": "ijm-00001"}, "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "post", "category": "Articles", "public": false, "hidden": false}]