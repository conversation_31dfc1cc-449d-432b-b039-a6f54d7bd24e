[{"message": "Load article ijgc-2018-000124", "status": "200", "url": "/workflow?currStage=parseEmail", "data": {"customer": "bmj", "project": "ijgc", "doi": "ijgc-2018-000124", "ftpFileName": "ijgc-2018-000124", "articleTitle": "articleTitle", "articleType": "articleType", "subject": "field", "specialInstructions": "specialInstructions", "priority": "priority", "mandatoryFields": "mandatoryFields", "hasMandatoryData": "hasMandatoryData", "forceLoad": "true", "status": {"code": 200}}, "expectedOutput": {"status": {"code": 200, "message": "data received", "data": "\"job reloaded successfully\""}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}, {"message": "Load article ijgc-2018-000150", "status": "200", "url": "/workflow?currStage=parseEmail", "data": {"customer": "bmj", "project": "ijgc", "doi": "ijgc-2018-000150", "ftpFileName": "ijgc-2018-000150", "articleTitle": "articleTitle", "articleType": "articleType", "subject": "field", "specialInstructions": "specialInstructions", "priority": "priority", "mandatoryFields": "mandatoryFields", "hasMandatoryData": "hasMandatoryData", "forceLoad": "true", "status": {"code": 200}}, "expectedOutput": {"status": {"code": 200, "message": "data received", "data": "\"job reloaded successfully\""}}, "method": "post", "category": "Workflow", "public": false, "hidden": false}]