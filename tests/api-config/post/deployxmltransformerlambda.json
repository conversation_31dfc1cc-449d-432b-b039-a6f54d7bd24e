[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/deployxmltransformerlambda", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "method": "post", "category": "System", "public": false, "hidden": false}, {"message": "POST deployxmltransformerlambda with customer=bmj, project=bjophthalmol, doi=bjo-test-27June (status 400)", "status": "400", "url": "/api/deployxmltransformerlambda/", "method": "post", "data": {"customer": "bmj", "project": "b<PERSON><PERSON><PERSON><PERSON><PERSON>", "doi": "bjo-test-27June"}, "expectedOutput": {"message": "Email is required to perform deployment"}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]