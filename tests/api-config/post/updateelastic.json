[{"message": "POST updateelastic with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/updateelastic/", "method": "post", "data": {"customer": "bmj", "project": "bmjoq", "doi": "bmjoq-2023-00235709", "apiKey": "36ab61a9-47e1-4db6-96db-8b95a9923599", "workflowTypes": "milestone", "index": "dev", "table": "articles", "postContent": true, "updateElasticAttempt": 0, "updateReviewerAttempt": 0}, "expectedOutput": {"status": 200, "content": {"statusCode": 201, "body": {"_index": "dev", "_id": "bmjoq-2023-00235709", "_version": 1, "result": "created", "_shards": {"total": 2, "successful": 1, "failed": 0}, "_seq_no": 1566, "_primary_term": 19}, "headers": {"location": "/dev/_doc/bmjoq-2023-00235709", "x-elastic-product": "Elasticsearch", "content-type": "application/json", "content-length": "158"}, "request": {"uri": {"protocol": "http:", "slashes": true, "auth": null, "host": "localhost:9200", "port": "9200", "hostname": "localhost", "hash": null, "search": null, "query": null, "pathname": "/dev/_doc/bmjoq-2023-00235709", "path": "/dev/_doc/bmjoq-2023-00235709", "href": "http://localhost:9200/dev/_doc/bmjoq-2023-00235709"}, "method": "POST", "headers": {"Content-Type": "application/json", "Cache-Control": "no-cache", "accept": "application/json", "content-length": 11568}}}}, "headers": {"content-type": "application/json"}, "category": "Metadata and Reports"}]