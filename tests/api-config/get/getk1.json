[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getk1", "method": "get", "expectedOutput": "{\"status\":{\"code\":\"400\",\"message\":\"Did not get a response\"}}", "category": "System", "public": false, "hidden": false}, {"message": "GET getk1", "status": "200", "url": "/api/getk1/", "method": "get", "expectedOutput": {"status": {"code": "400", "message": "Did not get a response"}}, "category": "System", "public": false, "hidden": false}]