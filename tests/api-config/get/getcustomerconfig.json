[{"message": "getcustomerconfig: Success - All configs exist", "status": 200, "url": "/api/getcustomerconfig?customer=bmj", "method": "get", "expectedOutput": {"stage": {"stagetemplate": [{"name": "addJob", "type": "support"}, {"name": "Awaiting files", "type": "support"}, {"name": "File download", "type": "support"}, {"name": "Convert Files", "type": "support"}, {"name": "Pre-editing", "type": "production"}, {"name": "Automated LQC", "type": "production"}, {"name": "Waiting for CE", "type": "production"}, {"name": "Copyediting", "type": "production"}, {"name": "Copyediting Check", "type": "production"}, {"name": "Typesetter Check", "type": "production"}, {"name": "Proofreading", "type": "production"}, {"name": "Author Review", "type": "production"}, {"name": "Editor Review", "type": "production"}, {"name": "Publisher Check", "type": "production"}, {"name": "Typesetter Review", "type": "production"}, {"name": "Author Revision", "type": "production"}, {"name": "Publisher Review", "type": "production"}, {"name": "For Embargo", "type": "production"}, {"name": "Validation Check", "type": "production"}, {"name": "Final Deliverables", "type": "production"}, {"name": "Upload for Online First", "type": "production"}, {"name": "Archive", "type": "common"}, {"name": "Banked", "type": "common"}, {"name": "Withdrawn", "type": "common"}, {"name": "Resupply", "type": "common"}, {"name": "Hold", "type": "common"}, {"name": "Support", "type": "common"}], "productionStageNames": [{"name": "Pre-editing"}, {"name": "Automated LQC"}, {"name": "Waiting for CE"}, {"name": "Copyediting"}, {"name": "Copyediting Check"}, {"name": "Typesetter Check"}, {"name": "Proofreading"}, {"name": "Author Review"}, {"name": "Editor Review"}, {"name": "Publisher Check"}, {"name": "Typesetter Review"}, {"name": "Author Revision"}, {"name": "Publisher Review"}, {"name": "For Embargo"}, {"name": "Validation Check"}, {"name": "Final Deliverables"}, {"name": "Upload for Online First"}], "milestonetemplate": [{"name": "addJob"}, {"name": "Analysis"}, {"name": "QC Checking"}, {"name": "Issue Makeup"}, {"name": "Issue Revises"}, {"name": "Pass for Press"}, {"name": "Files to Printer"}, {"name": "Upload Online"}, {"name": "Archive"}]}, "accessconfig": {"hold-fastrack": {"production": {"production": "view", "support": "view", "developer": "view", "vendor": "view", "manager": "edit", "admin": "edit"}, "publisher": {"production": "view", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "view", "manager": "edit", "production": "view"}}, "includeweekend": {"daysprod": "false", "daysoverdue": "false"}, "revokeArticle": {"production": {"production": "hide", "support": "hide", "developer": "hide", "vendor": "hide", "manager": "hide", "admin": "hide"}, "publisher": {"production": "hide", "manager": "hide", "admin": "hide"}, "copyeditor": {"vendor": "hide", "manager": "hide", "production": "hide"}}, "assignment": {"production": {"production": "edit", "support": "edit", "vendor": "view", "developer": "edit", "manager": "edit", "admin": "edit"}, "publisher": {"production": "edit", "publisher": "edit", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "edit", "manager": "edit", "production": "edit"}}, "duedate": {"production": {"production": "edit", "support": "edit", "vendor": "edit", "developer": "edit", "manager": "edit", "admin": "edit"}, "publisher": {"production": "edit", "publisher": "edit", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "view", "manager": "edit", "production": "view"}}, "assetStatus": {"production": {"production": "view", "support": "view", "vendor": "view", "developer": "view", "manager": "view", "admin": "view"}, "publisher": {"production": "edit", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "view", "manager": "view", "production": "view"}}, "pressReleaseDate": {"publisher": {"production": "edit", "manager": "edit", "admin": "edit"}}, "pressReleaseStatus": {"production": {"production": "view", "support": "view", "vendor": "view", "developer": "view", "manager": "view", "admin": "view"}, "publisher": {"production": "edit", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "view", "manager": "view", "production": "view"}}, "leftsidebar": {"production": {"production": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "vendor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "developer": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide"}, "support": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide"}, "admin": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "edit", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide", "settings": "edit"}, "manager": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide", "settings": "edit"}, "handlingeditor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "associateeditor": {"issueview": "edit", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "leadguesteditor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "guesteditor": {"issueview": "edit", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "editor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}}, "publisher": {"production": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide"}, "publisher": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "manager": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide", "settings": "edit"}, "admin": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide", "settings": "edit"}, "editor": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "handlingeditor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "associateeditor": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "leadguesteditor": {"issueview": "hide", "articlesview": "hide", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}, "guesteditor": {"issueview": "edit", "articlesview": "edit", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "hide"}}, "copyeditor": {"vendor": {"issueview": "hide", "articlesview": "edit", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "edit"}, "manager": {"issueview": "hide", "articlesview": "edit", "articlefilterview": "hide", "reportview": "edit", "reviewerslist": "hide", "cardview": "hide"}, "production": {"issueview": "hide", "articlesview": "edit", "articlefilterview": "hide", "reportview": "hide", "reviewerslist": "hide", "cardview": "edit"}}}, "articles": {"production": {"production": "<PERSON><PERSON><PERSON>", "vendor": "<PERSON><PERSON><PERSON>", "support": "<PERSON><PERSON><PERSON>", "developer": "<PERSON><PERSON><PERSON>", "manager": "<PERSON><PERSON><PERSON>", "admin": "<PERSON><PERSON><PERSON>", "associateeditor": "<PERSON><PERSON><PERSON>", "handlingeditor": "<PERSON><PERSON><PERSON>", "guesteditor": "<PERSON><PERSON><PERSON>", "leadguesteditor": "<PERSON><PERSON><PERSON>"}, "publisher": {"production": "<PERSON><PERSON><PERSON>", "manager": "<PERSON><PERSON><PERSON>", "admin": "<PERSON><PERSON><PERSON>", "editor": "<PERSON><PERSON><PERSON>", "associateeditor": "<PERSON><PERSON><PERSON>", "handlingeditor": "<PERSON><PERSON><PERSON>", "guesteditor": "<PERSON><PERSON><PERSON>", "leadguesteditor": "<PERSON><PERSON><PERSON>", "developmenteditor": "developmenteditorview", "executiveeditor": "<PERSON><PERSON><PERSON>"}, "copyeditor": {"vendor": "assignedtomelistview", "production": "assignedtomelistview", "manager": "<PERSON><PERSON><PERSON>", "copyeditor": "assignedtomelistview"}, "author": {"vendor": "assignedtomelistview", "production": "assignedtomelistview", "manager": "<PERSON><PERSON><PERSON>", "copyeditor": "assignedtomelistview"}}, "taskdropdown": {"production": {"production": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "hide", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "vendor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "hide", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "developer": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "support": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "admin": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "manager": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "handlingeditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "associateeditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "leadguesteditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "guesteditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}}, "publisher": {"production": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "manager": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "admin": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "editor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "hide", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "handlingeditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "associateeditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "leadguesteditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "guesteditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}, "developmenteditor": {"assign": "hide", "assignedtomelistview": "hide", "unassign": "hide", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide", "developmenteditorview": "edit"}, "executiveeditor": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "edit", "banked": "edit", "associatewithme": "hide", "withdrawn": "edit"}}, "copyeditor": {"vendor": {"assign": "hide", "assignedtomelistview": "edit", "unassign": "edit", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "production": {"assign": "hide", "assignedtomelistview": "edit", "unassign": "edit", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "manager": {"assign": "edit", "assignedtomelistview": "hide", "unassign": "edit", "assigntoexeter": "edit", "others": "edit", "all": "edit", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}, "copyeditor": {"assign": "hide", "assignedtomelistview": "edit", "unassign": "edit", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}}, "author": {"production": {"assign": "hide", "assignedtomelistview": "edit", "unassign": "edit", "assigntoexeter": "hide", "others": "hide", "all": "hide", "archive": "hide", "banked": "hide", "associatewithme": "hide", "withdrawn": "hide"}}}, "dashboardView": {"production": {"production": "edit", "admin": "edit", "vendor": "edit", "developer": "edit", "support": "edit", "manager": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "publisher": {"production": "edit", "manager": "edit", "admin": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "author": {"production": "edit", "manager": "edit", "admin": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "copyeditor": {"manager": "edit"}}, "removeDashboardSearchBar": {"production": {"production": "edit", "admin": "edit", "developer": "edit", "support": "edit", "manager": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "publisher": {"production": "edit", "manager": "edit", "admin": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "author": {"production": "edit", "manager": "edit", "admin": "edit", "handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}, "copyeditor": {"manager": "edit", "production": "hide", "vendor": "hide"}}, "supportView": {"production": ["production", "admin", "developer", "support", "manager", "handlingeditor", "associateeditor", "guesteditor", "leadguesteditor"], "publisher": ["handlingeditor", "associateeditor", "guesteditor", "leadguesteditor"]}, "rightsidebar": {"messages": {"production": ["production", "vendor", "admin", "developer", "support", "manager", "associateeditor", "handlingeditor", "guesteditor", "leadguesteditor"], "publisher": ["production", "manager", "admin", "editor", "associateeditor", "handlingeditor", "guesteditor", "leadguesteditor"], "copyeditor": ["manager"]}, "submissionStages": {"default": []}, "history": {"production": ["production", "vendor", "admin", "developer", "support", "manager", "handlingeditor", "associateeditor", "guesteditor", "leadguesteditor"], "publisher": ["production", "manager", "admin", "editor", "handlingeditor", "associateeditor", "guesteditor", "leadguesteditor"], "copyeditor": ["manager"]}, "tickets": {"production": ["production", "vendor", "admin", "developer", "support", "manager"], "publisher": ["production", "manager", "admin", "editor"], "copyeditor": ["manager"]}, "notes": {"production": ["production", "vendor", "admin", "developer", "support", "manager"], "publisher": ["production", "manager", "admin", "editor"], "copyeditor": ["manager"]}, "customAssets": "hide", "reviewerinfo": "hide", "supportTicket": "edit", "removeCustomAssets": ["Insight1", "Editorial2", "Correction", "Retraction", "Feature Article3", "Review Article"], "showOldRightSidebar": "true"}, "editpage": "proof_review", "viewpage": "proof_review", "deskreject": {}, "retry": {"production": {"admin": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "developer": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "production": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "vendor": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "support": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "manager": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"], "reviewer": ["addJob", "Add job", "Awaiting files", "File download", "Convert Files", "Convert files", "Support"]}}, "retrylqc": {"production": {"admin": ["Support"], "developer": ["Support"], "production": ["Support"], "vendor": ["Support"], "support": ["Support"], "manager": ["Support"], "reviewer": ["Support"]}}, "probevalidation": {"production": {"production": "edit", "support": "edit", "developer": "edit", "manager": "edit", "admin": "edit"}}, "repurposedtb": {"production": {"production": "edit", "support": "edit", "developer": "edit", "manager": "edit", "admin": "edit"}}, "exportpackage": {"production": {"admin": ["Archive"], "developer": ["Archive"], "production": ["Archive"], "vendor": ["Archive"], "support": ["Archive"], "manager": ["Archive"], "reviewer": ["Archive"]}}, "exportepub": {"production": {"admin": [], "developer": [], "production": [], "vendor": [], "support": [], "manager": [], "reviewer": []}}, "customlabels": {"production": {"production": "hide", "support": "hide", "vendor": "hide", "developer": "hide", "manager": "hide", "admin": "hide"}, "publisher": {"production": "edit", "manager": "edit", "admin": "edit"}, "copyeditor": {"vendor": "hide", "production": "hide", "manager": "edit"}}, "showStages": {"production": {"production": [], "support": [], "vendor": [], "developer": [], "manager": [], "admin": []}, "publisher": {"production": [], "manager": [], "admin": []}, "author": {"production": [], "manager": [], "admin": []}, "copyeditor": {"vendor": [], "production": [], "manager": []}}, "allowOnlySelfAssign": {"copyeditor": {"vendor": "true", "production": "true", "manager": "false"}}, "userassignaccessrolestages": {"author": {"author": ["Author Review", "Author check", "Author Revision", "Author Resubmission", "Author resubmission", "Author revision", "EPR Review"]}, "production": {"admin": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "Ready for Publication", "Author Accepted <PERSON><PERSON><PERSON>", "EPR Review", "Revises", "PM review", "Author corrections", "Author corrections-final", "Publisher corrections", "Editor corrections", "Final QC corrections"], "developer": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "Ready for Publication", "Author Accepted <PERSON><PERSON><PERSON>", "EPR Review", "Revises", "PM review", "Author corrections", "Author corrections-final", "Publisher corrections", "Editor corrections", "Final QC corrections"], "vendor": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Revises", "EPR Review", "Author corrections", "Author corrections-final", "Publisher corrections", "Editor corrections", "Final QC corrections"], "support": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "Ready for Publication", "Author Accepted <PERSON><PERSON><PERSON>", "EPR Review", "Revises", "PM review", "Author corrections", "Author corrections-final", "Publisher corrections", "Editor corrections", "Final QC corrections"], "manager": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "Ready for Publication", "Author Accepted <PERSON><PERSON><PERSON>", "EPR Review", "Revises", "PM review", "Author corrections", "Author corrections-final", "Publisher corrections", "Editor corrections", "Final QC corrections"], "reviewer": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Copyediting QC", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Revises", "EPR Review"]}, "publisher": {"publisher": ["Copyediting", "Publisher Check", "Publisher Review", "Publisher approval", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Associate Editor Check", "Publisher check", "Publisher review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Translator check", "Ready for Publication", "Feature Review", "EPR Review", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision", "PM review", "For Embargo"], "production": ["Copyediting", "Publisher Check", "Publisher Review", "Publisher approval", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Associate Editor Check", "Publisher check", "Publisher review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Translator check", "Ready for Publication", "Feature Review", "EPR Review", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision", "PM review", "For Embargo"], "manager": ["Copyediting", "Waiting for CE", "EPR Review", "Final QC", "Publisher Check", "Publisher Review", "Publisher approval", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Associate Editor Check", "Publisher check", "Publisher review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Translator check", "Ready for Publication", "Feature Review", "EPR Review", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision", "PM review", "For Embargo"], "admin": ["Copyediting", "Publisher Check", "Publisher Review", "Publisher approval", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Associate Editor Check", "Publisher check", "Publisher review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Translator check", "Ready for Publication", "Feature Review", "EPR Review", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision", "PM review", "For Embargo"]}, "copyeditor": {"copyeditor": ["Copyediting", "EPR Review", "Copyediting check", "Copyediting review", "Final QC"], "vendor": ["Copyediting", "EPR Review", "Copyediting check", "Copyediting review", "Final QC"], "production": ["Copyediting", "EPR Review", "Copyediting check", "Copyediting review", "Final QC"], "manager": ["Waiting for CE", "Copyediting Check", "Copyediting", "Copyediting QC", "EPR Review", "Copyediting review", "Final QC"]}}, "duedatechangeaccessrolestages": {"author": {"author": ["Author Review", "Author Revision", "Author Resubmission", "Author resubmission", "Author revision"]}, "production": {"admin": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "AAM"], "support": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "AAM"], "manager": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets", "Proofreading", "AAM"], "reviewer": ["Pre-editing", "Typesetter Check", "Typesetter Review", "Typesetter review", "Validation Check", "Final Deliverables", "Copyediting Check", "Copyediting check", "Waiting for CE", "Waiting for proofreading", "Support", "Typesetter check", "Final deliverables", "Waiting for Assets"]}, "publisher": {"publisher": ["Publisher Check", "Publisher Review", "Editor Check", "Editor Review", "Editor review", "Author Review", "Author Revision", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Author resubmission", "Author revision", "Associate Editor Check", "Publisher check", "Publisher review", "Author review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Author Resubmission", "Translator check", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision"], "production": ["Publisher Check", "Publisher Review", "Editor Check", "Editor Review", "Editor review", "Author Review", "Author Revision", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Author resubmission", "Author revision", "Associate Editor Check", "Publisher check", "Publisher review", "Author review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Author Resubmission", "Translator check", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision"], "manager": ["Waiting for CE", "Copyediting Check", "Copyediting", "Copyediting QC", "EPR Review", "Copyediting review", "Final QC", "Publisher Check", "Publisher Review", "Editor Check", "Editor Review", "Editor review", "Author Review", "Author Revision", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Author resubmission", "Author revision", "Associate Editor Check", "Publisher check", "Publisher review", "Author review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Author Resubmission", "Translator check", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision"], "admin": ["Publisher Check", "Publisher Review", "Editor Check", "Editor Review", "Editor review", "Author Review", "Author Revision", "Managing editor", "Handling editor", "Article in review", "Associate editor", "Author resubmission", "Author revision", "Associate Editor Check", "Publisher check", "Publisher review", "Author review", "Revised Editor Check", "Primary Editor Check", "Editor Send Decision", "Waiting for Assets", "Author Resubmission", "Translator check", "Editorial assistant", "Section editor-decision", "Associate editor-decision", "Technical decision", "Rejected manuscripts", "Section editor", "Lead guest editor", "Guest editor", "Guest editor-decision", "Lead guest editor-decision"]}, "copyeditor": {"copyeditor": ["Copyediting", "Copyediting QC"], "manager": ["Waiting for CE", "Copyediting Check", "Copyediting", "Copyediting QC", "EPR Review", "Copyediting review", "Final QC"]}}, "showreports": ["All Article summary", "All Stage summary report", "WIP Article summary", "WIP Stage summary report", "Published Article summary", "Published Stage summary report", "Articles on hold", "Corrections report", "Copyediting report", "Article resupply report", "Articles with exeter", "Banked Article summary"], "authorFeedBack": "true", "publisherFeedBack": "false", "tableheadings": {"stagehistory": {"authorfeedback": [{"name": "Author", "value": ""}, {"name": "Rating", "value": ""}, {"name": "Additional Comments", "value": ""}], "publisherfeedback": [{"name": "Publisher", "value": ""}, {"name": "Rating", "value": ""}, {"name": "Additional Comments", "value": ""}], "rowHeading": [{"name": "DOI", "value": ""}, {"name": "Article Number", "value": ""}, {"name": "Project Name", "value": ""}, {"name": "Invoice ID", "value": ""}, {"name": "Page Count", "value": ""}, {"name": "Word Count", "value": ""}, {"name": "Word Count Excluding reference", "value": ""}, {"name": "Days in production", "value": ""}, {"name": "Article Stage", "value": ""}, {"name": "Article Status", "value": ""}, {"name": "Article Type", "value": ""}, {"name": "Times at Support", "value": ""}, {"name": "Days in Support", "value": ""}, {"name": "Times at Hold", "value": ""}, {"name": "Days in Hold", "value": ""}, {"name": "Accepted Date", "value": "2Days"}, {"name": "Exported Date", "value": "1Day"}, {"name": "Planned End Date", "value": "1Day"}, {"name": "Total Duration (days)", "value": "3Days"}, {"name": "Copyright", "value": "", "customer": "mbs"}, {"name": "License Type", "value": ""}, {"name": "Corresponding Author", "value": ""}, {"name": "Corresponding Author <PERSON><PERSON>", "value": ""}, {"name": "Pre-editing", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Copy-editing", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Typesetter Check", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Publisher Check", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Author Review", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Typesetter Review", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Publisher Review", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Final Deliverables", "value": ""}, {"name": "TAT (days)", "value": ""}, {"name": "Archive", "value": ""}, {"name": "TAT (days)", "value": ""}], "stagetodisplay": ["Pre-editing", "Copyediting", "Typesetter Check", "Publisher Check", "Author Review", "Typesetter Review", "Publisher Review", "Final Deliverables", "Archive"]}, "reviewerTable": {"totalArticles": "Total", "inprogress": "In progress", "completed": "Completed", "invited": "Invited", "declined": "Declined"}}, "widget": {"production": {"admin": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "developer": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "production": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "vendor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "support": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "manager": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "reviewer": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "handlingeditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "associateeditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "leadguesteditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "guesteditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"]}, "publisher": {"publisher": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "manager": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "admin": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "handlingeditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "associateeditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "leadguesteditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "guesteditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"]}, "author": {"publisher": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "manager": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "admin": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "handlingeditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "associateeditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "leadguesteditor": ["article_overdue_prod", "article_urgent_prod", "article_assignedtome", "issue_overdue_prod", "issue_urgent_prod", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"], "guesteditor": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"]}, "copyeditor": {"manager": ["article_overdue", "article_urgent", "article_assignedtome", "issue_overdue", "issue_urgent", "issue_assignedtome", "articles_loaded", "articles_publish", "articles_average"]}}, "articleCard": {"volumeGroup": "false"}, "filters": {"Days In Production": ["<5", "5-10", "11-15", "16-20", "21-25", "26-30", ">30"], "Days overdue": ["All", "1-2 days", "3-4 days", "5-10 days", "11-15 days", "16-20 days", "21-25 days", "26-30 days", ">30 days"]}, "reportTable": {"removeHypen": "false", "addPrefix": "false"}, "filterColumn": {"WIP Article summary": ["add Job", "Awaiting files", "File download", "Convert Files", "Hold", "Support", "Author Revision", "Archive", "addJob"], "Published Article summary": ["add Job", "Awaiting files", "File download", "Convert Files", "Hold", "Support", "Author Revision", "addJob"]}, "articleType": ["Addendum", "Correction", "Miscellaneous", "Retraction", "Thank you to our reviewers", "Policy statement"], "language": [], "reportFilters": {"All Article summary": {"removeFilters": "false"}, "All Stage summary report": {"removeFilters": "false"}, "WIP Article summary": {"removeFilters": "false"}, "WIP Stage summary report": {"removeFilters": "false"}, "Press Info": {"removeFilters": "false"}, "Published Article summary": {"monthWiseFilters": "false"}, "Published Stage summary report": {"monthWiseFilters": "false"}, "In Progress Linked Article Summary": {"monthWiseFilters": "false"}, "Published Linked Article Summary": {"monthWiseFilters": "false"}}, "hideTopNavBarIcons": {"copyeditor": {"vendor": "hide", "manager": "edit", "copyeditor": "hide", "production": "hide"}, "publisher": {"handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit", "all": "edit"}, "production": {"handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit"}}, "hideAddArticle": {"publisher": {"handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit", "leadguesteditor": "edit", "all": "edit"}, "production": {"handlingeditor": "edit", "associateeditor": "edit", "guesteditor": "edit"}, "author": {"production": "hide"}, "copyeditor": {"vendor": "hide", "manager": "hide", "copyeditor": "hide", "production": "hide"}}, "hideAddSubmission": {"author": {"production": "edit"}}, "hideDownload": ["archdischild", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bjsports", "emermed", "fetalneonatal", "<PERSON><PERSON><PERSON>", "heartjnl", "jc<PERSON><PERSON>", "bmjsrh", "medhum", "oemed", "practneurol", "thorax<PERSON><PERSON>"], "displayOverdueStageTAT": {"Waiting for Assets": "21"}, "daysOverdueCard": {"removeOverdue": "false"}, "dueDaySelector": "planned-end-date", "articleinreview": [], "reviewinfo": {"reviewstages": [], "reviewinprogress": [], "reviewdecisionstage": [], "reviewrequired": null}, "updateassigneduser": [], "sendlinkwhenassigned": {"copyeditor": {"externalvendor": "true"}}, "reassignCopyeditorstage": ["Copyediting"], "showAssignUserName": {"copyeditor": {"manager": {"Waiting for CE": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}, "Copyediting": {"roleType": ["copyeditor"], "accessLevel": ["vendor", "production", "manager", "externalvendor"]}, "Copyediting QC": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}, "Copyediting Check": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}, "Copyediting check": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}, "Typesetter Check": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}, "AAM": {"roleType": ["copyeditor"], "accessLevel": ["manager"]}}}, "publisher": {"manager": {"Waiting for CE": {"roleType": ["publisher"], "accessLevel": ["manager"]}, "Copyediting": {"roleType": ["publisher", "copyeditor"], "accessLevel": ["vendor", "production", "manager", "externalvendor"]}, "Copyediting QC": {"roleType": ["publisher"], "accessLevel": ["manager"]}, "Copyediting Check": {"roleType": ["publisher"], "accessLevel": ["manager"]}, "Copyediting check": {"roleType": ["publisher"], "accessLevel": ["manager"]}}}}, "sendassignedemail": {}, "switch_doi": "false", "analytical-report": {"production": {"production": "show", "support": "show", "vendor": "show", "developer": "show", "manager": "show", "admin": "show"}, "publisher": {"production": "show", "manager": "show", "admin": "show"}, "author": {"production": "hide", "manager": "hide", "admin": "hide"}, "copyeditor": {"vendor": "hide", "production": "hide", "manager": "show"}}, "production-report": "true", "bulkIssueUpload": "true", "displayPeerReviewTab": "false", "name": "Journal name", "retainNodeCount": true, "quickactions": [{"icon": "fa fa-kriya-search-user-outline fa-2xl", "title": "Author details", "description": "View Author Details.", "allowedGroups": ["production"], "actions": {"type": "callFunc", "callFunc": "eventHandler.components.actionitems.openAuthorDetailsPopup", "params": {"showEditorReviewerTab": false}}}], "withdrawForm": {"allowedAccessLevel": ["manager"], "allowedRoletype": ["production"]}}}, "category": "Configuration"}]