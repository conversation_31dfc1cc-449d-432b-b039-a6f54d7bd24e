[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/scheduledreminders", "method": "get", "category": "Workflow", "public": false, "hidden": false}, {"message": "GET scheduledreminders (status 400)", "status": "400", "url": "/api/scheduledreminders/", "method": "get", "expectedOutput": {"message": "Request failed with status code 500", "name": "AxiosError", "stack": "AxiosError: Request failed with status code 500\n    at settle (/Users/<USER>/Documents/Kriyadocs_services/kriya2.0/staging/node_modules/axios/dist/node/axios.cjs:2053:12)\n    at IncomingMessage.handleStreamEnd (/Users/<USER>/Documents/Kriyadocs_services/kriya2.0/staging/node_modules/axios/dist/node/axios.cjs:3170:11)\n    at IncomingMessage.emit (node:events:530:35)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (/Users/<USER>/Documents/Kriyadocs_services/kriya2.0/staging/node_modules/axios/dist/node/axios.cjs:4280:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async /Users/<USER>/Documents/Kriyadocs_services/kriya2.0/staging/cms/v3.0/api/get/scheduledreminders.js:38:34", "config": {"transitional": {"silentJSONParsing": true, "forcedJSONParsing": true, "clarifyTimeoutError": false}, "adapter": ["xhr", "http", "fetch"], "transformRequest": [null], "transformResponse": [null], "timeout": 0, "xsrfCookieName": "XSRF-TOKEN", "xsrfHeaderName": "X-XSRF-TOKEN", "maxContentLength": -1, "maxBodyLength": -1, "env": {}, "headers": {"Accept": "application/json, text/plain, */*", "Content-Type": "application/json", "User-Agent": "axios/1.10.0", "Content-Length": "162", "Accept-Encoding": "gzip, compress, deflate, br"}, "method": "post", "url": "http://localhost:7001/query/getscheduledreminder", "data": "{\"index\":\"dev-reminderlogs\",\"table\":\"reminderlogs\",\"actionType\":\"Email\",\"status\":\"success OR scheduled\",\"from\":0,\"size\":100,\"event\":\"*\",\"eventParam\":\"*\",\"to\":\"*\"}", "allowAbsoluteUrls": true}, "code": "ERR_BAD_RESPONSE", "status": 500}, "category": "Workflow", "public": false, "hidden": false}]