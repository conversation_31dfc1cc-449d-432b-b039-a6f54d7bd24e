[{"message": "Don't return article file when customer name is not given", "status": "400", "url": "/api/getissuedata?doi=bjophthalmol_103_2&project=bjophthalmol", "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "method": "get", "category": "Issues"}, {"message": "Don't return article file when project name is not given", "status": "400", "url": "/api/getissuedata?doi=bjophthalmol_103_2&customer=bmj", "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer, project, issue filename) is/are not provided."}}, "method": "get", "category": "Issues"}]