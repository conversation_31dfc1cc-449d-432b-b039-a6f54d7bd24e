[{"message": "GET getcomponents with customer=kriya, project=kriya, doi=kriya_2025_435", "status": "200", "url": "/api/getcomponents?customer=kriya&project=kriya&doi=kriya_2025_435&articleState=read-only&articlesRole=publisher&customerType=journal&accessType=dashboard&offlineState=false", "method": "get", "fileLocation": "/_testFiles/expectedoutputfiles/get/getcomponents/kriya_2025_435.html", "outputfileLocation": "/_testFiles/expectedoutputfiles/get/getcomponents/actual/kriya_2025_435.html", "category": "Configuration"}]