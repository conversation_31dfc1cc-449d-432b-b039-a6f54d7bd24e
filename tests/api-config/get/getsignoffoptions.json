[{"message": "Return Error when project is not defined", "status": 400, "url": "/api/getsignoffoptions?customer=bmj", "expectedOutput": {"status": {"code": "400", "message": "Project not found in project list"}, "step": "Get workflow data"}, "method": "get", "category": "Workflow"}, {"message": "Return Error when current stage is not defined", "status": 401, "url": "/api/getsignoffoptions?customer=bmj&project=bjophthalmol", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "get", "category": "Workflow", "public": false, "hidden": false}, {"message": "GET getsignoffoptions with customer=bmj, project=bjophthalmol (status 400)", "status": "400", "url": "/api/getsignoffoptions/?customer=bmj&project=bjophthalmol&currStage=signoff&currProcess=signoff", "method": "get", "expectedOutput": {"status": {"code": "400", "message": "Project not found in project list"}, "step": "Get workflow data"}, "category": "Workflow", "public": false, "hidden": false}, {"message": "GET getsignoffoptions with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709 with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/getsignoffoptions/?customer=bmj&doi=bmjoq-2023-00235709&project=bmjoq&currStage=signoff&version=Original&revieweremail=iain.smith%40nhs.net&currProcess=signoff", "method": "get", "expectedOutput": "<response><options><p><input type=\"radio\" data-reviewer=\"editor-contact-author\" data-email-editable=\"true\"/></p></options><trigger name=\"editor-contact-author\" status=\"200\" action=\"sendMail\" use-param=\"mail-config\"><to/><cc/><replyto><span class=\"publisher-email\"><EMAIL></span></replyto><subject><span class=\"article-id\">10.1136/bmjoq-2023-00235709</span></subject><body><div class=\"email-body\"><p contenteditable=\"true\" data-user=\"{recipientName}\">Dear {recipientName},</p><p class=\"useredit\"/><p contenteditable=\"true\">Best regards,</p><p>{userName}</p><p contenteditable=\"true\"><i><span class=\"journal-title\">BMJ Open Quality</span></i></p></div></body></trigger></response>", "category": "Workflow", "public": false, "hidden": false}]