[{"message": "Return bir projects when customer is there", "url": "/api/projects?customer=bmj", "param": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"_index": {"type": "string"}, "_id": {"type": "string"}, "_score": {"type": "number"}, "_source": {"type": "object", "properties": {"projects": {"type": "array", "items": {"type": "object", "properties": {"workflowTemplate": {"type": "string"}, "tableSetterConfig": {"type": "string"}, "pe": {"type": "string"}, "jobTemplate": {"type": "string"}, "name": {"type": "string"}, "fullName": {"type": "string"}, "tableSetterCSS": {"type": "string"}, "componentTemplate": {"type": "string"}, "pm": {"type": "string"}, "parameters": {"type": "object", "additionalProperties": {"type": "string"}}, "styleTemplate": {"type": "string"}, "proofConfig": {"type": "string"}, "proofConfig-journal-id": {"type": "string"}, "customCSS": {"type": "string"}, "customcomponentTemplate": {"type": "string"}}, "additionalProperties": true}}}, "additionalProperties": true}}, "additionalProperties": true}}, "status": 200, "method": "get", "category": "Metadata and Reports"}]