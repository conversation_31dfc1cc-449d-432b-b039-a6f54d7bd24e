[{"message": "Return error when E-mail id is not given", "status": "200", "url": "/api/getaddjobpage/", "expectedOutput": {"status": {"code": 200, "message": "ERROR: Email ID is not provided."}}, "method": "get", "category": "System", "public": false, "hidden": false}, {"message": "Return error when author<PERSON><PERSON> is not given", "status": "200", "url": "/api/getaddjobpageemailID=bmj", "expectedOutput": {"status": {"code": 200, "message": "ERROR: Author Name is not provided."}}, "method": "get", "category": "System"}, {"message": "Don't return article file when authenticaion not given", "status": "401", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "url": "/api/getaddjobpage", "method": "get", "category": "System", "public": false, "hidden": false}, {"message": "GET getaddjobpage with customer=example-customer, project=example-project, doi=10.1234/example.doi", "status": "200", "url": "/api/getaddjobpage/?customer=example-customer&project=example-project&doi=10.1234%2Fexample.doi", "method": "get", "expectedOutput": {"status": {"code": 200, "message": "Job page data retrieved successfully"}, "data": {"jobTemplate": "template-data", "workflow": "workflow-config"}}, "headers": {"content-type": "application/json"}, "category": "System", "public": false, "hidden": false}]