[{"message": "FetchArticlesByStage: Auth Error - API Key no access to specified customer (requires mock)", "status": "200", "url": "/api/fetcharticlesbystagecustomer=anotherCustomer&stageName=preediting", "method": "get", "expectedOutput": {"message": "No Articles found", "data": []}, "category": "Workflow"}, {"message": "FetchArticlesByStage: Logic Error - Missing stageName query parameter (auth success mock)", "status": 400, "url": "/api/fetcharticlesbystagecustomer=ppl", "method": "get", "expectedOutput": {"status": 400, "message": "Invalid request parameters", "details": {"message": "must have required property 'stageName'"}}, "category": "Workflow"}, {"message": "GET fetcharticlesbystage with customer=ppl", "status": "200", "url": "/api/fetcharticlesbystage/?apiKey=invalidKey&customer=ppl&stageName=preediting", "method": "get", "expectedOutput": {"message": "No Articles found", "data": []}, "category": "Workflow"}]