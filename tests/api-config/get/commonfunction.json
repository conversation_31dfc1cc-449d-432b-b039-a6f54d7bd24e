[{"message": "Dispatcher: Return 400 if type is not defined in query (GET)", "status": 400, "url": "/api/commonfunction", "method": "get", "expectedOutput": "function to call is not defined", "category": "System"}, {"message": "Dispatcher: Return 400 if type in query refers to a non-existent function (GET)", "status": 400, "url": "/api/commonfunctiontype=nonExistentFunction", "method": "get", "expectedOutput": "function to call is not defined", "category": "System"}, {"message": "processapi: Success (type in query - GET)", "status": 200, "url": "/api/commonfunctiontype=processapi", "method": "get", "expectedOutput": true, "category": "System"}, {"message": "getSpecialIssueConfig: Error - config file does not exist (GET)", "status": 400, "url": "/api/commonfunctiontype=getSpecialIssueConfig&customer=nonExistentCustomer&project=testProject", "method": "get", "expectedOutput": "{\"err\":\"file not exist\"}", "category": "System"}, {"message": "getSpecialIssueConfig: Success - project section missing in config, returns empty array (GET)", "status": 200, "url": "/api/commonfunctiontype=getSpecialIssueConfig&customer=ppl", "method": "get", "expectedOutput": [], "category": "System"}, {"message": "getThemedCollectionConfig: Success - file and section exist (GET)", "status": 200, "url": "/api/commonfunctiontype=getThemedCollectionConfig&customer=ppl&project=bcj", "method": "get", "expectedOutput": ["Epigenetics", "Glycobiology", "Cell Signaling in the 21st Century", "Awards"], "category": "System"}, {"message": "getThemedCollectionConfig: Error - config file does not exist (GET)", "status": 400, "url": "/api/commonfunctiontype=getThemedCollectionConfig&customer=nonExistentCustomer&project=testProject", "method": "get", "expectedOutput": "{\"err\":\"file not exist\"}", "category": "System"}, {"message": "getThemedCollectionConfig: Success - project section missing in config, returns empty array (GET)", "status": 200, "url": "/api/commonfunctiontype=getThemedCollectionConfig&customer=ppl", "method": "get", "expectedOutput": [], "category": "System"}, {"message": "getPeerReviewConfig: Success - file exists (GET)", "status": 200, "url": "/api/commonfunctiontype=getPeerReviewConfig&customer=ppl", "method": "get", "expectedOutput": {"article-types": {"default": {"Research": {"data-value": "Research"}, "Review": {"data-value": "Review"}, "Commentary": {"data-value": "Commentary"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}, "Correspondence": {"data-value": "Correspondence"}, "Correction": {"data-value": "Correction"}, "Retraction": {"data-value": "Retraction"}, "Expression of Concern": {"data-value": "Expression of Concern"}, "Editorial Note": {"data-value": "Editorial Note"}}, "bcj": {"Research": {"data-value": "Research"}, "Review": {"data-value": "Review"}, "Commentary": {"data-value": "Commentary"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}, "Correspondence": {"data-value": "Correspondence"}}, "bsr": {"Research": {"data-value": "Research"}, "Review": {"data-value": "Review"}, "Commentary": {"data-value": "Commentary"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}, "Correspondence": {"data-value": "Correspondence"}, "Brief Report": {"data-value": "Brief Report"}}, "bst": {"Review": {"data-value": "Review"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}, "Correspondence": {"data-value": "Correspondence"}, "Author Profile": {"data-value": "Author Profile"}}, "cs": {"Research": {"data-value": "Research"}, "Review": {"data-value": "Review"}, "Commentary": {"data-value": "Commentary"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}, "Correspondence": {"data-value": "Correspondence"}, "Hypothesis": {"data-value": "Hypothesis"}}, "etls": {"Review": {"data-value": "Review"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}}, "ebc": {"Review": {"data-value": "Review"}, "Perspective": {"data-value": "Perspective"}, "Editorial": {"data-value": "Editorial"}}, "bio": {"Careers": {"data-value": "Careers"}, "Editorial": {"data-value": "Editorial"}, "Education": {"data-value": "Education"}, "Events": {"data-value": "Events"}, "Feature": {"data-value": "Feature"}, "Beginner's Guide": {"data-value": "Beginner's Guide"}, "Interview": {"data-value": "Interview"}, "Lifelong Learning": {"data-value": "Lifelong Learning"}, "News": {"data-value": "News"}, "Obituary": {"data-value": "Obituary"}, "Outreach": {"data-value": "Outreach"}, "Policy": {"data-value": "Policy"}, "Puzzle": {"data-value": "Puzzle"}, "Reviews": {"data-value": "Reviews"}, "Student Focus": {"data-value": "Student Focus"}, "30 Second Read": {"data-value": "30 Second Read"}}, "themed-collection": {"bcj": ["Epigenetics", "Glycobiology", "Cell Signaling in the 21st Century", "Awards"], "bsr": ["Imbalance of Homeostasis in the Nervous System", "Awards", "Modern technologies in protein production, biophysical and structural analysis"], "bst": ["Epigenetics", "Awards"], "cs": ["Biology of Ageing", "<PERSON><PERSON>", "Awards", "Shifting paradigms in immunity: New cells, or new functions for old cells"], "bio": ["Award Lectures", "Title of Special Section"]}, "special-issue": {"etls": ["AI in Drug Discovery", "Biofuels", "CryoEM", "Bunyaviruses", "Gut-Brain Axis"], "ebc": ["Plant Metabolism", "Proteasome and Protein Degradation", "Gut Microbiota and Disease", "Ubiquitin", "Epigenetics and Chemical Exposures", "Immunomodulation/Immunotherapy", "Understanding Biochemistry 2025"], "bio": ["Understanding Biochemistry", "Metabolic Bioengineering"]}}, "project-types": {"bcj": {"data-value": "bcj", "jrnlType": ["regular-issue", "themed-collection"]}, "bsr": {"data-value": "bsr", "jrnlType": ["regular-issue", "themed-collection"]}, "bst": {"data-value": "bst", "jrnlType": ["regular-issue", "themed-collection"]}, "cs": {"data-value": "cs", "jrnlType": ["regular-issue", "themed-collection"]}, "etls": {"data-value": "etls", "jrnlType": ["regular-issue", "special-issue"]}, "ebc": {"data-value": "ebc", "jrnlType": ["regular-issue", "special-issue"]}, "bio": {"data-value": "bio", "jrnlType": ["regular-issue", "themed-collection", "special-issue"]}}, "section-types": {"default": {"HR, Learning & Organization Studies": {"data-value": "HR, Learning & Organization Studies"}, "Accounting, Finance & Economics": {"data-value": "Accounting, Finance & Economics"}, "Public Policy & Environmental Management": {"data-value": "Public Policy & Environmental Management"}, "Operations, Logistics & Quality": {"data-value": "Operations, Logistics & Quality"}}}, "file-types": {"default": {"coverletter": {"data-value": "coverletter", "text": "Cover letter", "skipFromDropdownList": true, "data-i18n": "[text]messages.Cover_letter", "header-hide-text": "[text]messages.m00092", "header-download-text": "[text]messages.m00093", "header-hide-text-single": "[text]messages.m00092", "header-download-text-single": "[text]messages.m00093", "hidefromreviewer": "default", "fileAcceptType": "single", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "licencetopublishform": {"data-value": "licencetopublishform", "text": "Licence to Publish form", "skipFromDropdownList": true, "data-i18n": "[text]messages.LTP_form", "header-hide-text": "[text]messages.m00165", "header-download-text": "[text]messages.m00166", "header-hide-text-single": "[text]messages.m00165", "header-download-text-single": "[text]messages.m00166", "hidefromreviewer": "default", "fileAcceptType": "single", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "figure": {"data-value": "figure", "data-i18n": "[text]messages.Figure", "text": "Figure", "data-accept-file": ".bmp,.tiff,.jpeg,.png,.eps,.pdf,.tif,.jpg,.gif", "header-hide-text": "[text]messages.m00077", "header-download-text": "[text]messages.m00078", "header-hide-text-single": "[text]messages.m00096", "header-download-text-single": "[text]messages.m00097", "hidefromreviewer": "optional", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "responsetoreviewer": {"data-value": "responsetoreviewer", "text": "Author response to reviews", "data-i18n": "[text]messages.Author_response_to_reviews", "header-hide-text": "[text]messages.m00102", "header-download-text": "[text]messages.m00103", "header-hide-text-single": "[text]messages.m00102", "header-download-text-single": "[text]messages.m00103", "hidefromreviewer": "optional", "fileAcceptType": "single", "skipFromDropdownList": true, "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "image": {"data-value": "image", "data-i18n": "[text]messages.Image", "text": "Image", "data-accept-file": ".bmp,.tiff,.jpeg,.png,.eps,.pdf,.tif,.jpg", "header-hide-text": "[text]messages.m00158", "header-download-text": "[text]messages.m00159", "header-hide-text-single": "[text]messages.m00160", "header-download-text-single": "[text]messages.m00159", "hidefromreviewer": "optional", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "tables": {"data-value": "tables", "text": "Table", "data-i18n": "[text]messages.Table", "header-hide-text": "[text]messages.m00081", "header-download-text": "[text]messages.m00082", "header-hide-text-single": "[text]messages.m00100", "header-download-text-single": "[text]messages.m00101", "hidefromreviewer": "optional", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "supplement": {"data-value": "supplement", "text": "Supplement File", "data-i18n": "[text]messages.Supplementaryfile", "header-hide-text": "[text]messages.m00075", "header-download-text": "[text]messages.m00076", "header-hide-text-single": "[text]messages.m00133", "header-download-text-single": "[text]messages.m00134", "hidefromreviewer": "optional", "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "trackchanges": {"data-value": "trackchanges", "text": "Track changes", "data-i18n": "[text]messages.Trackchanges", "header-hide-text": "[text]messages.m00098", "header-download-text": "[text]messages.m00099", "header-hide-text-single": "[text]messages.m00098", "header-download-text-single": "[text]messages.m00099", "hidefromreviewer": "optional", "fileAcceptType": "single", "skipFromDropdownList": true, "options": {"download": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor", "author", "reviewer"], "delete": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "hidetoreviewer": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"], "addnew": ["editor", "associateeditor", "guesteditor", "handlingeditor", "leadguesteditor"]}}, "manuscript": {"data-value": "manuscript", "text": "Manuscript", "skipFromDropdownList": true, "data-i18n": "[text]messages.Manuscript"}}}, "mandatory-files": {"types": ["manuscript", "responsetoreviewer", "trackchanges", "licencetopublishform"]}, "hide-for-files": {"except": ["manuscript", "figure", "tables", "supplement", "responsetoreviewer", "reportingguideline", "otherfiles", "trackchanges"]}, "article-language": {"default": {"English": {"data-value": "english"}}}, "languages-list": {"default": {"English": {"data-value": "en-GB", "text": "ENGLISH (US)"}}}, "peer_review": {"reviewer": {"removeNodes": "[data-blind]"}, "rightpanel": {"summary": {"mandatory": false, "reviewer": {"mandatory": true}}, "comments": {"reviewer": {"mandatory": false}}, "editor": {"comment-name": "{Name} (Screening check)", "card-label": "Screening check", "display-role": "Screening check", "data-i18n": "[text]messages.Screening_check"}, "associateeditor": {"comment-name": "{Name} (Associate editor review)", "card-label": "Associate editor review", "display-role": "Associate editor review", "data-i18n": "[text]messages.Associate_editor_review"}, "guesteditor": {"comment-name": "{Name} (Guest editor review)", "card-label": "Guest editor review", "display-role": "Guest editor review", "data-i18n": "[text]messages.Guest_editor_review"}, "reviewer": {"comment-name": "{Name} (Reviewer)", "display-role": "Reviewer"}, "author": {"comment-name": "{Name} (Author)", "display-role": "Author"}}}, "custom-peerreview": {"add-summary-option": ["editor", "associateeditor", "guesteditor", "reviewer", "handlingeditor", "leadguesteditor"], "editor_article_special_issue_type": ["editor", "associateeditor", "guesteditor", "reviewer", "handlingeditor", "author"], "new_submission_form_details": ["editor", "associateeditor", "guesteditor", "reviewer", "handlingeditor", "author"]}, "section-order": {"default": ["article-detail-page", "submission-checklist", "author-detail-page", "declarations-page", "file-upload-page", "article-summary-page"]}, "custom-submission": {"middlename-asn": "edit", "prefix": "edit", "save-for-later": "edit", "suffix-asn": "edit", "linkedin": "edit", "twitter": "edit", "other-socials": "edit", "author-bio": "edit", "custom-submit-group-asn": "edit", "corresp-city": "edit", "corresp-state": "edit", "corresp-country": "edit", "corresp-addLine1": "edit", "corresp-addLine2": "edit", "custom-degree-asn": "edit", "corresp-postal": "edit", "supporting-documents-asn": "edit", "Manuscript-asn": "edit", "Supplementary": "edit", "copyrightform": "edit", "kwd-type-field": "edit", "article-instruction-asn": "edit", "custom-declare-instruction": "edit", "custom-foot-bar-asn": "edit", "custom-author-instruction-asn": "edit", "custom-article-instruction-asn": "edit", "custom-declaration-ins-group-asn": "edit", "custom-declaration-group": "edit", "pre-submission-checklist": "edit", "corresaddressgroup": "edit", "custom-summary-issueType": "edit", "custom-summary-specialissue": "edit", "custom-summary-checklist": "edit", "custom-summary-articletype": "edit", "custom-foot-bar-emerald": "edit", "custom-corresp-address-asn": "edit", "custom-corresp-exist-asn": "edit", "custom-addnewcorresp-asn": "edit", "panel-for-page3": "edit", "RORInstitution": "edit", "disclosure-header": "edit", "custom-summary-declaration": "edit", "article-topic-field": {"attr": {"min": 1, "max": 3, "data-mandatory": "true"}}, "document-footer-note-specific": "edit", "add-aff-option": {"attr": {"tmp-link-array": "a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z"}}, "custom -abstract-group-asn": "edit", "article-subject-field-asn": {"attr": {"data-mandatory": "true"}}, "permission-field": {"attr": {"data-mandatory": "true"}}, "ci-field-asn": {"attr": {"data-mandatory": "true"}}, "corresp-author": {"attr": {"data-multi-corresp": "false"}}, "tab-for-page4": "edit", "line-for-page4": "edit", "tab-for-page5": "edit", "panel-for-pagegsw2": "edit", "panel-for-pagegsw3": "edit", "panel-for-pagegsw4": "edit", "panel-for-pagegsw5": "edit", "panel-for-pagegsw6": "edit", "page-gsw": "edit", "subject-area-field": "edit", "custom-prechecklist-group-asn": "edit"}, "subject-areas": {"default": ["Acute Kidney Injury", "Augmented Intelligence, Digital Health, and Data Science", "Basic Science", "Bone and Mineral Metabolism", "Business", "Chronic Kidney Disease", "Clinical Science", "Development of the Kidney", "Diabetes and the Kidney", "Dialysis", "Education and Training", "Ethics", "Genetic Diseases of the Kidneys", "Geriatric Nephrology", "Glomerular Diseases", "Health Equity", "Health Maintenance, Nutrition, and Metabolism", "Hypertension and Cardiovascular Diseases", "Interventional Nephrology", "Molecular and Physiology", "Onconephrology", "Palliative Care", "Pathology", "Pediatric Nephrology", "Policy", "Practice Management", "Transplantation and Immunology", "Women's Health"]}, "useNewContrib": true, "roleStages": {"editor": "Screening check", "associateeditor": "Associate editor review", "splwf": {"editor": "Screening check", "guesteditor": "Guest editor review"}}, "login": {"config-path": "/config/default/orcid_peer_login_ppl.html", "css-path": "/css/submission/customer/gsw/customer_style.css", "credentials": {"orcid": {"clientId": "APP-POO97SMXL2LWEWE9", "clientSecret": "e68672ed-3981-45e9-86c8-db1af6330d6d", "accessTokenUri": "https://api.orcid.org/oauth/token", "authorizationUri": "https://orcid.org/oauth/authorize", "redirectUri": "{$site}/oauth", "logoutUri": "https://orcid.org/signout", "scopes": "/authenticate", "state": "orcid", "setcookie": "true"}}}, "splWFXpath": "//custom-meta-group/custom-meta[meta-name='special-issue'][not(//stage//name[.='Section editor'])]", "article-title-count": {"default": "none"}, "article-summary-count": {"default": "none"}, "ignore-signoff-validation": "true", "ithenticateVersion": "v2.0", "blind": "true", "mandatory-files-based-on-article-type": {"copyrightform": ["Basic Article", "Fellows First", "Letter to the Editor", "Special Section"]}, "submission-signoff-publisher-email": "<EMAIL>", "custom-messages": {"submission-signoff-message-head": "Thank you for your submission to", "submission-signoff-message-head1": " Portland Press!", "submission-signoff-message-note": "You will receive an email confirmation of your submission details and will hear from our staff or editors soon. In the meantime, please contact", "submission-signoff-message-note1": "with any questions."}, "addNewUser": {"reviewer": {"affiliationRequired": false}}, "doi-format": {"bcj": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "bsr": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "bst": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "cs": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "etls": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "ebc": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "bio": {"startVal": 3000, "continous-doi": "true", "doi-length": 4, "doi-separator": "-"}, "__STUBS": {"suffix": "_C"}, "__TRANSFER": {"suffix": "_T"}}, "jrnlTransferMatrix": {"bsr": ["bst", "bcj", "cs"], "bcj": ["bsr", "bst", "cs"], "bst": ["bsr", "bcj", "cs"], "cs": ["bsr", "bcj", "bst"], "ebc": ["bst", "bsr", "bcj", "cs"]}, "author-transfer-decision-period": {"default": 7, "bcj": 7, "bsr": 7, "bst": 7, "cs": 7, "etls": 7, "ebc": 7, "bio": 7}, "invite-reviewer-due-date": {"default": {"data-value": 6}}, "project-version-dropdown": true, "reviewer-minimum-comments": {"bcj": {"default": 2}, "bsr": {"default": 2}, "bst": {"default": 2}, "cs": {"default": 2}, "etls": {"default": 2}, "ebc": {"default": 2}}}, "expectedResponseType": "json", "category": "System"}, {"message": "getPeerReviewConfig: Error - config file does not exist (GET)", "status": 400, "url": "/api/commonfunctiontype=getPeerReviewConfig&customer=nonExistentCustomer&project=testProject", "method": "get", "expectedOutput": "{\"err\":\"file not exist\"}", "category": "System"}, {"message": "GET commonfunction (status 400)", "status": "400", "url": "/api/commonfunction/", "method": "get", "expectedOutput": "function to call is not defined", "category": "System"}]