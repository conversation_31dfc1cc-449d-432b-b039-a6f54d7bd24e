[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/gets3uploadcredentials", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "GET gets3uploadcredentials (status 400)", "status": "400", "url": "/api/gets3uploadcredentials/", "method": "get", "expectedOutput": "A Valid filename Is Needed!", "category": "Exports", "public": false, "hidden": false}]