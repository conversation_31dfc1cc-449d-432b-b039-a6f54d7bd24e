[{"message": "Get the article xml and transform to html for editor", "status": "200", "headers": {"referer": "{SITE_REFERER}"}, "url": "/api/getarticledata?customer=kriya&project=kriya&doi=kriya_2025_435&cmsVersion=v3.0&articlesRole=publisher&customerType=journal&apiKey={APIKEY}", "method": "get", "fileLocation": "/_testFiles/expectedoutputfiles/get/getarticledata/kriya_2025_435.html", "outputfileLocation": "/_testFiles/expectedoutputfiles/get/getarticledata/actual/kriya_2025_435.html", "category": "Articles"}]