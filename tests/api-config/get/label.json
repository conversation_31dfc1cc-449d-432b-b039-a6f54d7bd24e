[{"message": "Label: Return all labels for a particular customer - RS", "status": 200, "url": "/api/label", "method": "get", "data": {"customer": "rs"}, "params": true, "param": {"parameters": [{"name": "customer", "type": "string"}, {"name": "id", "type": "string"}, {"name": "category", "type": "string"}, {"name": "labelName", "type": "string"}, {"name": "componentType", "type": "string"}, {"name": "background", "type": "string"}, {"name": "color", "type": "string"}, {"name": "shortName", "type": "string"}, {"name": "displayOnCard", "type": "string"}, {"name": "values", "type": "string"}, {"name": "allowEdit", "type": "string"}, {"name": "source", "type": "string"}, {"name": "created", "type": "string"}]}, "category": "Configuration"}]