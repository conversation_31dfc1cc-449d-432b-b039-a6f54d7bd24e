[{"message": "Customer Service: Return error when 'customer' and 'service' parameters are missing (GET)", "status": 500, "url": "/api/customerservice", "method": "get", "expectedOutput": "Parameters missing!", "category": "Metadata and Reports", "public": false, "hidden": false}, {"message": "GET customerservice (status 500)", "status": "500", "url": "/api/customerservice/", "method": "get", "expectedOutput": "Parameters missing!", "category": "Metadata and Reports", "public": false, "hidden": false}]