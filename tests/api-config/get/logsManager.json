[{"url": "/api/logsmanagerqueryString=save&fromdate=2019-04-22&todate=2019-04-22", "status": "200", "message": "get save logs details with perfect request", "param": {"key": "hits.hits", "parameters": [{"name": "_index", "type": "String"}, {"name": "_type", "type": "String"}, {"name": "_source", "type": "object"}]}, "method": "get", "category": "System"}, {"url": "/api/logsmanager", "status": "401", "message": "get logsmanager details with out login credentials", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "method": "get", "category": "System"}, {"message": "GET logsmanager (status 204)", "status": "204", "url": "/api/logsmanager/", "method": "get", "expectedOutput": 400, "category": "System"}, {"message": "GET logsmanager (status 204)", "status": "204", "url": "/api/logsmanager/", "method": "get", "expectedOutput": 400, "category": "System"}]