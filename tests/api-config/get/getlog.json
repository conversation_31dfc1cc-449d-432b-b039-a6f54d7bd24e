[{"message": "Return error when file is not there in DB", "status": "200", "url": "/api/getlogcustomer=bmj&project=annrheumdis", "expectedOutput": "Error : File not found", "method": "get", "category": "System"}, {"message": "Don't return article file when authenticaion not given", "status": "401", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "url": "/api/getlog", "method": "get", "category": "System", "public": false, "hidden": false}, {"message": "GET getlog", "status": "200", "url": "/api/getlog/", "method": "get", "expectedOutput": "Error : File not found", "category": "System", "public": false, "hidden": false}]