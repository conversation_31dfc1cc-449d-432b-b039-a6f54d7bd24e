[{"message": "getpagetemplate: Error - No matching page/project in config", "status": 204, "url": "/api/getpagetemplateproject=unknown&cmsVersion=v2.0&pageName=unknownPage", "method": "get", "outputfileLocation": "/_testFiles/expectedoutputfiles/getpagetemplate/actual/getpagetemplate-error-no-matching-page-project.html", "category": "Configuration"}, {"message": "getpagetemplate: Error - Referenced toolbar-template file missing", "status": 204, "url": "/api/getpagetemplateproject=bmj&pageName=editorWithToolbar", "method": "get", "outputfileLocation": "/_testFiles/expectedoutputfiles/getpagetemplate/actual/getpagetemplate-error-referenced-toolbar-template-file-missing.html", "category": "Configuration"}, {"message": "getpagetemplate: Error - Referenced component-template file missing", "status": 204, "url": "/api/getpagetemplateproject=bmj&pageName=editorWithComponent", "method": "get", "outputfileLocation": "/_testFiles/expectedoutputfiles/getpagetemplate/actual/getpagetemplate-error-referenced-component-template-file-missing.html", "category": "Configuration"}, {"message": "getpagetemplate: Success - Only page template exists", "status": 200, "url": "/api/getpagetemplateproject=bmj&pageName=dashboard", "method": "get", "outputfileLocation": "/_testFiles/expectedoutputfiles/getpagetemplate/actual/getpagetemplate-success-only-page-template-exists.html", "category": "Configuration"}, {"message": "getpagetemplate: Success - With custom cmsVersion", "status": 200, "url": "/api/getpagetemplateproject=bmj&pageName=dashboard&cmsVersion=v3.0", "method": "get", "outputfileLocation": "/_testFiles/expectedoutputfiles/getpagetemplate/actual/getpagetemplate-success-with-custom-cmsversion.html", "category": "Configuration"}]