[{"message": "Convert the html to doc", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey={APIKEY}", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "Convert the html to doc without DOI ", "status": "200", "expectedOutput": {"error": "Missing DOI"}, "url": "/api/htmltodoc/?doi=&customer=bmj&project=bmjdrc&apiKey={APIKEY}", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "Convert the html to doc without Customer ", "status": "200", "expectedOutput": {"error": "Missing customer"}, "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=&project=bmjdrc&apiKey={APIKEY}", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "Convert the html to doc without Project ", "status": "200", "expectedOutput": {"error": "Missing project"}, "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=&apiKey={APIKEY}", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "Convert the html to doc without API Project", "status": "401", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey=", "method": "get", "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Can't get page template."}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc", "status": "200", "url": "/api/htmltodoc/?doi=&customer=bmj&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing DOI"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing customer"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing project"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey=", "method": "get", "expectedOutput": {"error": "Can't get page template."}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Can't get page template."}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc", "status": "200", "url": "/api/htmltodoc/?doi=&customer=bmj&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing DOI"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=&project=bmjdrc&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing customer"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=&apiKey=cde4c89b-e452-4ba5-b493-01c691033b72", "method": "get", "expectedOutput": {"error": "Missing project"}, "category": "Exports", "public": false, "hidden": false}, {"message": "GET htmltodoc with customer=bmj, project=bmjdrc, doi=bmjdrc-2018-000550", "status": "200", "url": "/api/htmltodoc/?doi=bmjdrc-2018-000550&customer=bmj&project=bmjdrc&apiKey=", "method": "get", "expectedOutput": {"error": "Can't get page template."}, "category": "Exports", "public": false, "hidden": false}]