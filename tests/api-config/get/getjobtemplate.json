[{"message": "Don't return article file when project name not given", "status": "400", "expectedOutput": {"status": {"code": "400", "message": "{\"status\":500,\"message\":\"Missing project or client parameters\"}"}, "step": "add job"}, "url": "/api/getjobtemplatecustomer=bmj", "method": "get", "category": "Configuration"}, {"message": "Don't return article file when customer name not given", "status": "400", "expectedOutput": {"status": {"code": "400", "message": "{\"status\":500,\"message\":\"Missing project or client parameters\"}"}, "step": "add job"}, "url": "/api/getjobtemplateproject=thoraxjnl", "method": "get", "category": "Configuration"}, {"message": "Don't return article file when authenticaion not given", "status": "401", "expectedOutput": {"status": {"code": 401, "message": "Not authorized. You have not provided credentials to access the requested resource"}}, "url": "/api/getjobtemplate/", "method": "get", "category": "Configuration", "public": false, "hidden": false}]