[{"message": "Throw Error as the payload is not specified", "status": "403", "url": "/api/getspecificcomponent", "method": "get", "expectedOutput": "{\"error\":{\"code\":\"ERR_INVALID_ARG_TYPE\"}}", "category": "Configuration", "public": false, "hidden": false}, {"message": "GET getspecificcomponent (status 403)", "status": "403", "url": "/api/getspecificcomponent/", "method": "get", "expectedOutput": {"error": {"code": "ERR_INVALID_ARG_TYPE"}}, "category": "Configuration", "public": false, "hidden": false}, {"message": "GET getspecificcomponent", "status": "200", "url": "/api/getspecificcomponent/?componentsList=author-details-modal&componentTemplateName=peerreview_editor&notify=%5Bobject+Object%5D", "method": "get", "expectedOutput": {"componentWrapper": "<div class=\"componentWrapper\"><div class=\"modal fade\" id=\"author-details-modal\" data-name=\"author-details-modal\" tabindex=\"-1\" role=\"dialog\" aria-hidden=\"true\"> <div class=\"modal-dialog modal-dialog-centered modal-xl\" role=\"document\"> <div class=\"modal-content\"> <div class=\"modal-header\"> <div class=\"modal-title\"> <ul class=\"nav row\"> <li class=\"active\"> <a href=\"#authorDetails\" class=\"nav-link block documentDetails active author-tab\">Author details <span class=\"author-count clearPerRequest\"/></a> </li> <li class=\"\"> <a href=\"#editorReviewerDetails\" class=\"nav-link block documentDetails editor-tab\">Editor &amp; reviewer details <span class=\"editor-count clearPerRequest\"/></a> </li> </ul> </div> <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"> <span aria-hidden=\"true\">&#xD7;</span> </button> </div> <div class=\"modal-body\"> <div class=\"row\"> <div class=\"col-4 pr-0 mb-3\"> <div class=\"input-group\"> <span class=\"input-group-prepend\" style=\"background: white !important;\"> <div class=\"input-group-text border\" style=\"background: white;\"> <i class=\"fa fa-search fa-xs\" style=\"color: gray;font-weight: 600;\"/> </div> </span> <input type=\"text\" id=\"search-input\" placeholder=\"Search by name / affiliation\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;showSearchField&apos;,&apos;channel&apos;:&apos;common&apos;,&apos;topic&apos;:&apos;functions&apos;, &apos;param&apos;:{&apos;tablePath&apos;:&apos;#author-details-modal #authorDetails tbody, #author-details-modal #editorReviewerDetails tbody&apos;, &apos;eacRowPath&apos;:&apos;tr&apos;,&apos;showPath&apos;:&apos;tr&apos;,&apos;classToInclude&apos;:[&apos;.author-name&apos;,&apos;.aff-group&apos;]}}}\" autocomplete=\"off\" class=\"form-control border-left-0 border\" style=\"font-size: 12px;box-shadow: none;\"/> </div> <span class=\"note-info text-secondary font-italic\"/> </div> </div> <div class=\"row\"> <div class=\"col-12\"> <div class=\"tab-content\"> <div class=\"tab-pane active\" id=\"authorDetails\"> <div class=\"tabs\"> <table class=\"w-100\"> <thead> <tr class=\"header\"> <th class=\"col-4 sortClm\"> <div class=\"authorNameTitle\"> <span>Author</span> <span data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;sortTableOnHeaderClick&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;,&apos;param&apos;:{&apos;sortOrder&apos;:[&apos;tr[data-corresp-author=\\&apos;true\\&apos;]&apos;,&apos;tr:not([data-corresp-author=\\&apos;true\\&apos;])&apos;]}}}\"><i class=\"fa-solid fa-arrow-up-long fa-xs\"/><i class=\"fa-solid fa-arrow-down-long fa-xs\"/></span> </div> </th> <th class=\"col-6 sortClm\"> <div class=\"authorAffTitle\"> <span>Affiliations</span> <span data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;sortTableOnHeaderClick&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;,&apos;param&apos;:{&apos;sortOrder&apos;:[&apos;tr[data-corresp-author=\\&apos;true\\&apos;]&apos;,&apos;tr:not([data-corresp-author=\\&apos;true\\&apos;])&apos;]}}}\"><i class=\"fa-solid fa-arrow-up-long fa-xs\"/><i class=\"fa-solid fa-arrow-down-long fa-xs\"/></span> </div> </th> <th class=\"col-2\"> <div class=\"contactAuthorTitle\"> </div> </th> </tr> </thead> <tbody class=\"clearPerRequest\"> </tbody> </table> <div class=\"no-results-node no-results-node h2 text-muted align-center text-center pt-5 mt-5\"> No results found </div> </div> </div> <div class=\"tab-pane\" id=\"editorReviewerDetails\"> <div class=\"tabs\"> <table class=\"w-100\"> <thead> <tr class=\"header\"> <th class=\"col-4 sortClm\"> <div class=\"authorNameTitle\"> <span>Name</span> <span data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;sortTableOnHeaderClick&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;,&apos;param&apos;:{&apos;sortOrder&apos;:[&apos;tr[data-corresp-author=\\&apos;true\\&apos;]&apos;,&apos;tr:not([data-corresp-author=\\&apos;true\\&apos;])&apos;]}}}\"><i class=\"fa-solid fa-arrow-up-long fa-xs\"/><i class=\"fa-solid fa-arrow-down-long fa-xs\"/></span> </div> </th> <th class=\"col-4 sortClm\"> <div class=\"authorAffTitle\"> <span>Affiliations</span> <span data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;sortTableOnHeaderClick&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;,&apos;param&apos;:{&apos;sortOrder&apos;:[&apos;tr[data-corresp-author=\\&apos;true\\&apos;]&apos;,&apos;tr:not([data-corresp-author=\\&apos;true\\&apos;])&apos;]}}}\"><i class=\"fa-solid fa-arrow-up-long fa-xs\"/><i class=\"fa-solid fa-arrow-down-long fa-xs\"/></span> </div> </th> <th class=\"col-2 sortClm d-none\"> <div class=\"authorAffTitle\"> <span>Assigned</span> <span data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;sortTableOnHeaderClick&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;,&apos;param&apos;:{&apos;sortOrder&apos;:[&apos;tr[data-corresp-author=\\&apos;true\\&apos;]&apos;,&apos;tr:not([data-corresp-author=\\&apos;true\\&apos;])&apos;]}}}\"><i class=\"fa-solid fa-arrow-up-long fa-xs\"/><i class=\"fa-solid fa-arrow-down-long fa-xs\"/></span> </div> </th> <th class=\"col-2\"> <div class=\"contactAuthorTitle\"> </div> </th> </tr> </thead> <tbody class=\"clearPerRequest\"> </tbody> </table> <div class=\"no-results-node no-results-node h2 text-muted align-center text-center pt-5 mt-5\"> No results found </div> </div> </div> </div> </div> </div> </div> <div class=\"modal-footer\"> <div class=\"row\"> <div class=\"col-12\"> <button type=\"button\" class=\"button kriya-btn filled\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;contactAllAuthors&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;}}\"><i class=\"fa-solid fa-envelope\"/> Contact all author(s)</button> </div> </div> </div> </div> </div> </div></div>"}, "category": "Configuration", "public": false, "hidden": false}, {"message": "GET getspecificcomponent", "status": "200", "url": "/api/getspecificcomponent/?componentsList=author-email-editable-modal&componentTemplateName=peerreview_editor&notify=%5Bobject+Object%5D", "method": "get", "expectedOutput": {"componentWrapper": "<div class=\"componentWrapper\"><div class=\"modal fade\" id=\"author-email-editable-modal\" data-name=\"author-email-editable-modal\" tabindex=\"-1\" role=\"dialog\" aria-hidden=\"true\"> <div class=\"modal-dialog modal-dialog-centered modal-xl\" role=\"document\"> <div class=\"modal-content\"> <div class=\"modal-header\"> <div class=\"modal-title\">Mail to author</div> <div class=\"form-check d-none\"> <input class=\"form-check-input\" type=\"checkbox\" id=\"ccCheckbox\" checked=\"checked\"/> <label class=\"form-check-label\" for=\"ccCheckbox\">CC co-authors</label> </div> <button type=\"button\" class=\"close\" data-dismiss=\"modal\" aria-label=\"Close\"> <span aria-hidden=\"true\">&#xD7;</span> </button> </div> <div class=\"modal-body\"> <div class=\"container mail-container\"> <div class=\"sf-email-details card\"> <div class=\"row\"> <div class=\"col-12\"> <div class=\"sf-email-info\"/> </div> </div> <div class=\"row\"> <div class=\"col-12\"> <div class=\"sf-email-body\"/> </div> </div> </div> <div class=\"row attachementContainer\"/> </div> </div> <div class=\"modal-footer\"> <span class=\"custom-file button kriya-btn\" data-name=\"add-attachment-btn\"> <input type=\"file\" name=\"[]\" multiple=\"true\" class=\"custom-file-input\" id=\"author-editable-attch\" data-message=\"{&apos;change&apos;:{&apos;funcToCall&apos;: &apos;changeAttachFile&apos;,&apos;channel&apos;:&apos;signoff&apos;,&apos;topic&apos;:&apos;decision&apos;}}\"/> <label for=\"author-editable-attch\"><i class=\"fa fa-paperclip\" aria-hidden=\"true\"/> <span data-i18n=\"[text]messages.Add_attachments\">Add attachments</span></label> </span> <button data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;:&apos;authorSendEmail&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;actionitems&apos;}}\" type=\"button\" class=\"button kriya-btn confirmBtn filled\" data-i18n=\"[text]messages.Send\">Send</button> </div> </div> </div> </div></div>"}, "category": "Configuration", "public": false, "hidden": false}]