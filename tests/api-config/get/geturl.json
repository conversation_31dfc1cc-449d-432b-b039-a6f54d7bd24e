[{"message": "when validate key is null and valid url given with http extension", "url": "/api/geturl?url=http://www.google.com&validate=true", "expectedOutput": "true", "status": "200", "method": "get", "category": "Workflow"}, {"message": "when validate key is null and valid url given with https extension", "url": "/api/geturl?url=https://www.google.com&validate=true", "expectedOutput": "true", "status": "200", "method": "get", "category": "Workflow"}, {"message": "when validate key is null and valid url given with no extension", "url": "/api/geturl?url=www.google.com&validate=true", "expectedOutput": "false", "status": "200", "method": "get", "category": "Workflow"}]