[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getsectionhead", "method": "get", "category": "Issues", "public": false, "hidden": false}, {"message": "GET getsectionhead", "status": "200", "url": "/api/getsectionhead/", "method": "get", "expectedOutput": {"status": {"code": 200, "message": "One or more of required parameters (customer id, project id, xpath) is/are not provided."}}, "category": "Issues", "public": false, "hidden": false}]