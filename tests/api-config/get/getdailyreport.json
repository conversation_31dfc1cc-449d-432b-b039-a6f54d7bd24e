[{"message": "Return report when particular customer is available for typeSetter", "status": "200", "url": "/api/getdailyreportcustomer=bmj&year=2025&date=1&month=05&stageName=Typesetter Check&tickets=1", "method": "get", "expectedOutput": "<table border='1'><thead><tr><th>DOI</th><th>Art. Type</th><th>Stage</th><th>Total Time</th><th>No. of Proofs</th><th>Proof Support</th><th>Editor Support</th><th>Signed Off By</th><th>Page Count</th></tr></thead><tbody></tbody></table>", "category": "System"}, {"message": "Return report when particular customer is available for Support articles", "status": "200", "url": "/api/getdailyreportcustomer=bmj&year=2025&date=1&month=05&stageName=Support&tickets=1", "method": "get", "expectedOutput": "<table border='1'><thead><tr><th>Date</th><th>DOI</th><th>Art. Type</th><th>Stage</th><th>Cause</th><th>Category</th><th>Reported Issue</th><th>What</th><th>Who</th><th>Why</th><th>Where</th><th>Fix Type</th><th>Sub category</th><th>Signed Off By</th><th>Page Count</th></tr></thead><tbody></tbody></table>", "category": "System"}, {"message": "Return error when particular customer is not available", "expectedOutput": "", "status": "200", "url": "/api/getdailyreportyear=2025&date=1&month=05&stageName=Typesetter Check&tickets=1", "method": "get", "category": "System"}]