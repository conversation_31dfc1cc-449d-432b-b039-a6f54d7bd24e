[{"message": "Throw Error as the payload is not specified", "comments": "{\"status\":{\"code\":404,\"message\":{\"status\":{\"code\":404,\"message\":\"You have used an incorrect request methos (GET instead of POST?) or the requested resource does not exist\"}}}}", "status": "400", "url": "/api/getcustomerattributes?customer=bmj&project=bmjdrc&doi=bmjdrc-2018-000550", "method": "get", "category": "Configuration"}]