[{"message": "Return article file as JSON for bmjoq-2023-00235709", "status": "200", "url": "/api/getarticle?customer=bmj&project=bmjoq&doi=bmjoq-2023-00235709", "method": "get", "param": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "object", "properties": {"authorSuggestedReviewers": {"type": "array", "items": {"type": "string"}}, "reviewerSuggestedReviewers": {"type": "array", "items": {"type": "string"}}, "reviewerData": {"type": "object", "properties": {"name": {"type": "array", "items": {"type": "string"}}}, "required": ["name"]}, "kriya-version": {"type": "string"}, "title": {"type": "string"}, "workflowStatus": {"type": "string"}, "priority": {"type": "string"}, "stageDue": {"type": "string"}, "stageName": {"type": "string"}, "lastUpdated": {"type": "string"}, "lang-attention-paper": {"type": ["null", "string"]}, "press-release": {"type": ["null", "string"]}, "invoice-info": {"type": "object", "properties": {"invoice-no": {"type": ["null", "string"]}}, "required": ["invoice-no"]}, "doi": {"type": "string"}, "pubID": {"type": "string"}, "articleType": {"type": "string"}, "articleTemplate": {"type": ["null", "string"]}, "article-url": {"type": ["null", "string"]}, "dashboard-url": {"type": ["null", "string"]}, "word-count": {"type": "string"}, "word-count-without-ref": {"type": "string"}, "fig-count": {"type": "string"}, "table-count": {"type": ["null", "string"]}, "equation-count": {"type": "string"}, "digest-count": {"type": ["null", "string"]}, "decision-count": {"type": ["null", "string"]}, "author-response-count": {"type": ["null", "string"]}, "ref-count": {"type": "string"}, "related-article": {"type": ["null", "string"]}, "page-count": {"type": "string"}, "page-count-round": {"type": ["null", "number", "string"]}, "proof-count": {"type": ["null", "string"]}, "volume": {"type": ["null", "string"]}, "issue": {"type": ["null", "string"]}, "proofLink": {"type": "object", "properties": {"path": {"type": "string"}}, "required": ["path"]}, "notes": {"type": "object", "properties": {"count": {"type": "number"}, "replied": {"type": "number"}}, "required": ["count", "replied"]}, "acceptedDate": {"type": "string"}, "receivedDate": {"type": "string"}, "type": {"type": "string"}, "pressdate": {"type": ["null", "string"]}, "strikingImage": {"type": ["null", "string"]}, "digest": {"type": ["null", "string"]}, "no-digest-set-date": {"type": ["null", "string"]}, "decisionLetter": {"type": ["null", "string"]}, "CELevel": {"type": "string"}, "authors": {"type": "object", "properties": {"name": {"type": "array", "items": {"type": "object", "properties": {"surname": {"type": ["null", "string"]}, "given-names": {"type": ["null", "string"]}, "email": {"type": ["null", "string"]}, "affiliations": {"type": "array", "items": {"type": "string"}}, "orcid": {"type": ["null", "string"]}, "corresponding-author": {"type": ["null", "string"]}, "submitting-author": {"type": ["null", "string"]}, "deceased": {"type": ["null", "string"]}}, "required": ["surname", "given-names", "email", "affiliations"]}}}, "required": ["name"]}, "affiliations": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "institution": {"type": ["null", "string"]}, "ringgold_id": {"type": ["null", "string"]}, "ringgold_subscriber_id": {"type": ["null", "string"]}, "ror": {"type": ["null", "string"]}, "institution-type": {"type": ["null", "string"]}, "department": {"type": ["null", "string"]}, "address_line": {"type": ["null", "string"]}, "city": {"type": ["null", "string"]}, "state": {"type": ["null", "string"]}, "country": {"type": ["null", "string"]}, "postal_code": {"type": ["null", "string"]}, "phone": {"type": ["null", "string"]}, "fax": {"type": ["null", "string"]}, "identifier": {"type": ["null", "string"]}, "identifier-type": {"type": ["null", "string"]}, "corresp_aff": {"type": "boolean"}}, "required": ["id", "corresp_aff"]}}, "chaser-info": {"type": "object", "properties": {"chaser": {"type": "array", "items": {"type": "string"}}}, "required": ["chaser"]}, "license-type": {"type": "string"}, "stage": {"type": "array", "items": {"type": "object", "properties": {"iteration": {"type": ["null", "string"]}, "page-count": {"type": ["null", "string"]}, "page-count-round": {"type": ["null", "string"]}, "start-word-count": {"type": ["null", "string"]}, "start-word-count-without-ref": {"type": ["null", "string"]}, "name": {"type": "string"}, "customer-stage-name": {"type": ["null", "string"]}, "start-date-time": {"type": ["null", "string"]}, "end-date-time": {"type": ["null", "string"]}, "start-date": {"type": "string"}, "end-date": {"type": ["null", "string"]}, "duration": {"type": "string"}, "end-word-count-without-ref": {"type": "string"}, "end-word-count": {"type": "string"}, "status": {"type": "string"}, "comments": {"type": ["null", "string"]}, "assigned": {"type": "object", "properties": {"to": {"type": ["null", "string"]}, "by": {"type": ["null", "string"]}, "on": {"type": ["null", "string"]}, "reason": {"type": ["null", "string"]}}, "required": ["to", "by"]}, "time-log": {"type": "object", "properties": {"log": {"type": "object", "properties": {"start": {"type": ["null", "string"]}, "end": {"type": ["null", "string"]}, "duration": {"type": ["null", "string"]}, "status": {"anyOf": [{"type": "null"}, {"type": "object", "properties": {"type": {"type": "string"}, "#text": {"type": ["null", "string"]}}, "required": ["type"]}]}}}}, "required": ["log"]}, "error": {"type": ["null", "string"]}, "object": {"type": "object", "properties": {"figure": {"type": ["null", "string"]}, "table": {"type": ["null", "string"]}, "box": {"type": ["null", "string"]}, "user": {"type": "object", "properties": {"name": {"type": ["null", "string"]}, "id": {"type": ["null", "string"]}, "inserted": {"type": ["null", "string"]}, "deleted": {"type": ["null", "string"]}}}, "proof": {"anyOf": [{"type": "null"}, {"type": "object", "properties": {"pdf": {"type": "array", "items": {"type": "object", "properties": {"user": {"type": "string"}, "role": {"type": "string"}, "time": {"type": "string"}, "path": {"type": "string"}}, "required": ["user", "role", "time", "path"]}}}, "required": ["pdf"]}]}}}, "job-logs": {"type": ["null", "object"], "properties": {"log": {"anyOf": [{"type": "array", "items": {"type": "object", "properties": {"username": {"type": "string"}, "useremail": {"type": "string"}, "start-date": {"type": "string"}, "start-time": {"type": "string"}, "end-date": {"type": "string"}, "end-time": {"type": "string"}, "status": {"type": "object", "properties": {"type": {"type": "string"}, "#text": {"type": ["null", "string"]}}, "required": ["type"]}, "data-doi": {"type": ["null", "string"]}, "data-project": {"type": ["null", "string"]}, "data-customer": {"type": ["null", "string"]}}, "required": ["username", "useremail", "start-date", "start-time", "end-date", "end-time", "status"]}}, {"type": "object", "properties": {"username": {"type": "string"}, "useremail": {"type": "string"}, "start-date": {"type": "string"}, "start-time": {"type": "string"}, "end-date": {"type": "string"}, "end-time": {"type": "string"}, "status": {"type": "object", "properties": {"type": {"type": "string"}, "#text": {"type": ["null", "string"]}}, "required": ["type"]}}, "required": ["username", "useremail", "start-date", "start-time", "end-date", "end-time", "status"]}]}}}, "held-stage": {"type": ["null", "string"]}, "hold-state": {"type": ["null", "string"]}}}}, "labels": {"type": "array", "items": {"type": "string"}}, "requiredReviewersCount": {"type": ["null", "number"]}, "submittingAuthor": {"type": ["null", "string"]}, "system-labels": {"type": "array", "items": {"type": "object", "properties": {"metaName": {"type": "string"}, "metaValue": {"type": "string"}}, "required": ["metaName", "metaValue"]}}, "corresp-info": {"type": "object", "properties": {"name": {"type": "array", "items": {"type": "object", "properties": {"surname": {"type": "string"}, "given-names": {"type": "string"}, "email": {"type": "string"}, "affiliations": {"type": "array", "items": {"type": "string"}}, "orcid": {"type": ["null", "string"]}, "corresponding-author": {"type": ["null", "string"]}, "submitting-author": {"type": ["null", "string"]}, "deceased": {"type": ["null", "string"]}}, "required": ["surname", "given-names", "email", "affiliations"]}}}, "required": ["name"]}, "copyright-statement": {"type": "string"}, "press-release-date": {"type": ["null", "string"]}, "press-release-applied-date": {"type": ["null", "string"]}, "color-in-print": {"type": ["null", "string"]}, "special-issue": {"type": ["null", "string"]}, "digest-load-date": {"type": ["null", "string"]}, "decision-letter-load-date": {"type": ["null", "string"]}, "press-release-status": {"type": ["null", "string"]}, "manuscript-type": {"type": ["null", "string"]}, "special-feature": {"type": ["null", "string"]}, "RSSfeed": {"type": ["null", "string"]}, "article-version": {"type": ["null", "string"]}, "keywords": {"type": "array", "items": {"type": "string"}}, "submission-decision": {"type": ["null", "string"]}, "review-status": {"type": ["null", "string"]}, "epubDate": {"type": ["null", "string"]}}, "required": ["author<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ie<PERSON><PERSON>", "reviewerSuggestedReviewers", "reviewerData", "kriya-version", "title", "workflowStatus", "priority", "stageDue", "stageName", "lastUpdated", "invoice-info", "doi", "pubID", "articleType", "word-count", "word-count-without-ref", "fig-count", "ref-count", "page-count", "page-count-round", "volume", "proofLink", "notes", "acceptedDate", "receivedDate", "type", "CELevel", "authors", "affiliations", "chaser-info", "license-type", "stage", "labels", "system-labels", "corresp-info", "copyright-statement", "manuscript-type", "keywords"]}, "category": "Metadata and Reports"}]