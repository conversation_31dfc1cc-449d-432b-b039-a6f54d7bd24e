[{"message": "Return reviewer list for invalid customer", "status": "204", "url": "/api/getreviewerlistcustomer=unknown", "method": "get", "category": "Users"}, {"message": "Return reviewer list for invalid customer with XML format", "status": "204", "url": "/api/getreviewerlistcustomer=unknown&format=xml", "method": "get", "category": "Users"}, {"message": "Do not return reviewer list when customer is missing", "status": "204", "url": "/api/getreviewerlist", "method": "get", "category": "Users", "public": false, "hidden": false}, {"message": "GET getreviewerlist (status 204)", "status": "204", "url": "/api/getreviewerlist/?from=0", "method": "get", "expectedOutput": "Failed to get artreviewerlist", "category": "Users", "public": false, "hidden": false}]