[{"message": "Return all user data when user with have login credentials for a particular customer - BMJ", "status": "200", "url": "/api/getassignuserdata?customer=bmj", "param": {"parameters": [{"name": "name", "type": "string"}, {"name": "email", "type": "string"}, {"name": "additionalDetails", "type": "object"}, {"name": "additionalDetails", "element": "customer-name", "type": "string"}]}, "method": "get", "category": "Users"}, {"message": "GET getassignuserdata with customer=bmj", "status": "200", "url": "/api/getassignuserdata?customer=bmj", "method": "get", "param": {"$schema": "http://json-schema.org/draft-07/schema#", "type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "email": {"type": "string"}, "additionalDetails": {"type": "object", "properties": {"role-type": {"type": "string"}, "customer-name": {"type": "string"}, "access-level": {"type": "string"}, "skill-level": {"type": "string"}, "page-limit": {"type": ["string", "number"]}, "assigned-pages": {"type": ["number", "string"]}, "access-control": {"type": "object", "properties": {"dashboard-menus": {"type": "array", "items": {"type": "string"}}}, "required": ["dashboard-menus"]}}, "additionalProperties": true}, "roles": {"type": "array", "items": {"type": "object", "properties": {"role-type": {"type": "string"}, "customer-name": {"anyOf": [{"type": "string"}, {"type": "array", "items": {"type": "string"}}]}, "access-level": {"type": "string"}}, "required": ["role-type", "access-level", "customer-name"]}}}, "required": ["name", "email"], "additionalProperties": true}}, "category": "Users", "public": false, "hidden": false}]