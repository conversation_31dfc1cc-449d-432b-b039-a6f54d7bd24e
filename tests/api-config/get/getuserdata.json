[{"message": "Return user data when user with have login credentials", "param": {"parameters": [{"name": "name", "type": "object"}, {"name": "email", "type": "string"}, {"name": "roles", "type": "array"}]}, "status": "200", "url": "/api/getuserdata?customer=bmj", "method": "get", "category": "Users"}, {"message": "Return user data when user with have login credentials for production role- bmj customer", "param": {"parameters": [{"name": "name", "type": "object"}, {"name": "email", "type": "string"}, {"name": "roles", "type": "array"}, {"name": "roles", "element": "role-type", "type": "string"}]}, "status": "200", "url": "/api/getuserdata?customer=bmj&roleType=production", "method": "get", "category": "Users"}, {"message": "Return user data when user with have login credentials for production role- all customer", "param": {"parameters": [{"name": "name", "type": "object"}, {"name": "email", "type": "string"}, {"name": "roles", "type": "array"}]}, "status": "200", "url": "/api/getuserdata?customer=all&roleType=production", "method": "get", "category": "Users"}]