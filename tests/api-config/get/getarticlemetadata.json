[{"message": "Throw Error as the payload is not specified", "status": "400", "url": "/api/getarticlemetadata", "method": "get", "category": "Articles", "public": false, "hidden": false}, {"message": "GET getarticlemetadata (status 400)", "status": "400", "url": "/api/getarticlemetadata/", "method": "get", "expectedOutput": "Customer is empty", "category": "Articles", "public": false, "hidden": false}]