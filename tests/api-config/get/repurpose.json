[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/repurpose", "method": "get", "category": "Workflow"}, {"message": "GET repurpose", "status": "200", "url": "/api/repurpose/", "method": "get", "expectedOutput": {"status": {"code": 400, "message": "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage  cannot be done. Unexpected input"}}, "category": "Workflow"}]