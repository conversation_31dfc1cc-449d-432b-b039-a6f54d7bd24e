[{"message": "Throw Error as the payload is not specified", "status": "200", "url": "/api/getsubmission", "method": "get", "expectedOutput": {"status": {"code": 500, "message": "One or more of required parameters (customer id, project id, email or orcid) is/are not provided"}}, "category": "Articles", "public": false, "hidden": false}, {"message": "GET getsubmission", "status": "200", "url": "/api/getsubmission/", "method": "get", "expectedOutput": {"status": {"code": 500, "message": "One or more of required parameters (customer id, project id, email or orcid) is/are not provided"}}, "category": "Articles", "public": false, "hidden": false}]