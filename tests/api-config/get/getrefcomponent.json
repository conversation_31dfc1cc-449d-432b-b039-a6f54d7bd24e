[{"message": "GET getrefcomponent with customer=bmj, project=bjophthalmol (status 400)", "status": "400", "url": "/api/getrefcomponent/?customer=bmj&project=bjophthalmol&type=getCitation", "method": "get", "expectedOutput": {"status": {"code": "404", "message": "Internal error"}, "message": "File not found"}, "category": "Configuration", "public": false, "hidden": false}, {"message": "GET getrefcomponent with customer=bmj, project=bmjoq", "status": "200", "url": "/api/getrefcomponent/?customer=bmj&project=bmjoq&type=modal", "method": "get", "expectedOutput": "<div data-ref-type=\"Interview\" data-if-selector=\"!.//*[@class=&apos;RefJournalTitle&apos;]|!.//*[@class=&apos;RefVolume&apos;]|.//*[@class=&apos;RefInterviewTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefInterviewTitle\">Article title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefInterviewTitle\" data-selector=\".//*[@class=&apos;RefInterviewTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"9\"><div class=\"col s2\"><label data-label=\"RefAOP\">AOP</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefAOP\" data-selector=\".//*[@class=&apos;RefAOP&apos;]\"/></div></div></div><div data-ref-type=\"Journal\" data-if-selector=\"!.//*[@class=&apos;RefSoftName&apos;]|!.//*[@class=&apos;RefSoftVer&apos;]|.//*[@class=&apos;RefJournalTitle&apos; or @class=&apos;RefArticleTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row\" row-index=\"3\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\"/><label data-label=\"RefYear\">Year</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefMonth\" data-selector=\".//*[@class=&apos;RefMonth&apos;]\"/><label data-label=\"RefMonth\">Month</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDay\" data-selector=\".//*[@class=&apos;RefDay&apos;]\"/><label data-label=\"RefDay\">Day</label></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefArticleTitle\">Article title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefArticleTitle\" data-selector=\".//*[@class=&apos;RefArticleTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefJournalTitle\">Journal title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefJournalTitle\" data-selector=\".//*[@class=&apos;RefJournalTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefVolume\" data-selector=\".//*[@class=&apos;RefVolume&apos;]\"/><label data-label=\"RefVolume\">Volume</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefIssue\" data-selector=\".//*[@class=&apos;RefIssue&apos;]\"/><label data-label=\"RefIssue\">Issue</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefELocation\" data-selector=\".//*[@class=&apos;RefELocation&apos;]\"/><label data-label=\"RefELocation\">Elocation</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefFPage\" data-selector=\".//*[@class=&apos;RefFPage&apos;]\"/><label data-label=\"RefFPage\">First Page</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLPage\" data-selector=\".//*[@class=&apos;RefLPage&apos;]\"/><label data-label=\"RefLPage\">Last page</label></div></div><div class=\"row input-field\" row-index=\"8\"><div class=\"col s2\"><label data-label=\"RefTransTitle\">Translate Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefTransTitle\" data-selector=\".//*[@class=&apos;RefTransTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"9\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPMID\" data-selector=\"./@data-pmid\"/><label data-label=\"RefPMID\">PMID</label></div></div><div class=\"row input-field\" row-index=\"9\"><div class=\"col s2\"><label data-label=\"RefGenre\">Genre</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefGenre\" data-selector=\".//*[@class=&apos;RefGenre&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"10\"><div class=\"col s2\"><label data-label=\"RefAOP\">AOP</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefAOP\" data-selector=\".//*[@class=&apos;RefAOP&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"11\"><div class=\"col s2\"><label data-label=\"RefWebSite\">URL</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/></div></div></div><div data-ref-type=\"Book\" data-if-selector=\".//*[@class=&apos;RefBookTitle&apos; or @class=&apos;RefChapterTitle&apos;]|.//*[@class=&apos;RefAuthor&apos; or @class=&apos;RefCollaboration&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefChapterTitle\">Chapter title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefChapterTitle\" data-selector=\".//*[@class=&apos;RefChapterTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefBookTitle\">Book title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefBookTitle\" data-selector=\".//*[@class=&apos;RefBookTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"6\"><div class=\"col s2\"><label data-label=\"RefSeriesTitle\">Series Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSeriesTitle\" data-selector=\".//*[@class=&apos;RefSeriesTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefVolume\" data-selector=\".//*[@class=&apos;RefVolume&apos;]\"/><label data-label=\"RefVolume\">Volume</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefEdition\" data-selector=\".//*[@class=&apos;RefEdition&apos;]\"/><label data-label=\"RefEdition\">Edition</label></div></div><div class=\"row input-field\" row-index=\"8\"><div class=\"col s2\"><label data-label=\"RefEditor\">Editor</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefEditor\" data-selector=\".//*[@class=&apos;RefEditor&apos;]\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row\" row-index=\"9\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/><label data-label=\"RefYear\">Year</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherLoc\" data-selector=\".//*[@class=&apos;RefPublisherLoc&apos;]\"/><label data-label=\"RefPublisherLoc\">Publisher location</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/><label data-label=\"RefPublisherName\">Publisher name</label></div></div><div class=\"row\" row-index=\"10\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefFPage\" data-selector=\".//*[@class=&apos;RefFPage&apos;]\"/><label data-label=\"RefFPage\">First page</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLPage\" data-selector=\".//*[@class=&apos;RefLPage&apos;]\"/><label data-label=\"RefLPage\">Last page</label></div></div><div class=\"row\" row-index=\"11\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefISBN\" data-selector=\".//*[@class=&apos;RefISBN&apos;]\"/><label data-label=\"RefISBN\">ISBN</label></div></div><div class=\"row input-field\" row-index=\"12\"><div class=\"col s2\"><label data-label=\"RefWebSite\">Website</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/></div></div></div><div data-ref-type=\"Book_Editor\" data-if-selector=\"!.//*[@class=&apos;RefAuthor&apos;]|!.//*[@class=&apos;RefCollaboration&apos;]|.//*[@class=&apos;RefBookTitle&apos; or @class=&apos;RefChapterTitle&apos;]|.//*[@class=&apos;RefEditor&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefEditor\">Editor</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefEditor\" data-selector=\".//*[@class=&apos;RefEditor&apos;]\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"2\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefBookTitle\">Book title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefBookTitle\" data-selector=\".//*[@class=&apos;RefBookTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefSeriesTitle\">Series Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSeriesTitle\" data-selector=\".//*[@class=&apos;RefSeriesTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"5\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefVolume\" data-selector=\".//*[@class=&apos;RefVolume&apos;]\"/><label data-label=\"RefVolume\">Volume</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefEdition\" data-selector=\".//*[@class=&apos;RefEdition&apos;]\"/><label data-label=\"RefEdition\">Edition</label></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherLoc\" data-selector=\".//*[@class=&apos;RefPublisherLoc&apos;]\"/><label data-label=\"RefPublisherLoc\">Publisher location</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/><label data-label=\"RefPublisherName\">Publisher name</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefISBN\" data-selector=\".//*[@class=&apos;RefISBN&apos;]\"/><label data-label=\"RefISBN\">ISBN</label></div></div><div class=\"row input-field\" row-index=\"8\"><div class=\"col s2\"><label data-label=\"RefWebSite\">Website</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/></div></div></div><div data-ref-type=\"Thesis\" data-if-selector=\".//*[@class=&apos;RefThesisTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefThesisTitle\">Thesis title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefThesisTitle\" data-selector=\".//*[@class=&apos;RefThesisTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefVolume\">Database</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefVolume\" data-selector=\".//*[@class=&apos;RefVolume&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"6\"><div class=\"col s2\"><label data-label=\"RefThesis\">Thesis type</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefThesis\" data-selector=\".//*[@class=&apos;RefThesis&apos;]\"/></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherLoc\" data-selector=\".//*[@class=&apos;RefPublisherLoc&apos;]\"/><label data-label=\"RefPublisherLoc\">Publisher location</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/><label data-label=\"RefPublisherName\">Publisher name</label></div></div><div class=\"row input-field\" row-index=\"8\"><div class=\"col s2\"><label data-label=\"RefWebSite\">Website</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"9\"><div class=\"col s2\"><label data-label=\"RefLAD\">Accessed Date</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLAD\" data-selector=\".//*[@class=&apos;RefLAD&apos;]\"/></div></div></div><div data-ref-type=\"Data\" data-if-selector=\".//*[@class=&apos;RefDataTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefDataTitle\">Data title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDataTitle\" data-selector=\".//*[@class=&apos;RefDataTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"5\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDataSource\" data-selector=\".//*[@class=&apos;RefDataSource&apos;]\"/><label data-label=\"RefDataSource\">Data source</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefVersion\" data-selector=\".//*[@class=&apos;RefVersion&apos;]\"/><label data-label=\"RefVersion\">Version</label></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div></div></div><div data-ref-type=\"Conference\" data-if-selector=\".//*[@class=&apos;RefConfName&apos; or @class=&apos;RefConferenceName&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"2\"><div class=\"col s2\"><label data-label=\"RefCollaboration\">Collaboration</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefCollaboration\" data-selector=\".//*[@class=&apos;RefCollaboration&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefConfArticleTitle\">Article title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefConfArticleTitle\" data-selector=\".//*[@class=&apos;RefConfArticleTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"5\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefConfName\" data-selector=\".//*[@class=&apos;RefConfName&apos;]\"/><label data-label=\"RefConfName\">Conference title</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefConfLoc\" data-selector=\".//*[@class=&apos;RefConfLoc&apos;]\"/><label data-label=\"RefConfLoc\">Conference location</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefConfDate\" data-selector=\".//*[@class=&apos;RefConfDate&apos;]\"/><label data-label=\"RefConfDate\">Conference date</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefFPage\" data-selector=\".//*[@class=&apos;RefFPage&apos;]\"/><label data-label=\"RefFPage\">First page</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLPage\" data-selector=\".//*[@class=&apos;RefLPage&apos;]\"/><label data-label=\"RefLPage\">Last page</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div></div></div><div data-ref-type=\"Software\" data-if-selector=\".//*[@class=&apos;RefSoftName&apos; or @class=&apos;RefSoftTitle&apos; or @class=&apos;RefSoftVer&apos; or @class=&apos;RefSoftwareName&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefSoftTitle\">Software title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSoftTitle\" data-selector=\".//*[@class=&apos;RefSoftTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefSoftVer\">Version</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSoftVer\" data-selector=\".//*[@class=&apos;RefSoftVer&apos;]\"/></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSoftManLoc\" data-selector=\".//*[@class=&apos;RefSoftManLoc&apos;]\"/><label data-label=\"RefSoftManLoc\">Publisher location</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefSoftManName\" data-selector=\".//*[@class=&apos;RefSoftManName&apos;]\"/><label data-label=\"RefSoftManName\">Publisher name</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div></div></div><div data-ref-type=\"Patent\" data-if-selector=\".//*[@class=&apos;RefPatentTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefPatentTitle\">Patent title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPatentTitle\" data-selector=\".//*[@class=&apos;RefPatentTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefPatentNum\">Patent number</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPatentNum\" data-selector=\".//*[@class=&apos;RefPatentNum&apos;]\"/></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherLoc\" data-selector=\".//*[@class=&apos;RefPublisherLoc&apos;]\"/><label data-label=\"RefPublisherLoc\">Publisher location</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/><label data-label=\"RefPublisherName\">Publisher name</label></div></div><div class=\"row input-field\" row-index=\"6\"><div class=\"col s2\"><label data-label=\"RefWebSite\">Website</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/></div></div></div><div data-ref-type=\"Website\" data-if-selector=\"!.//*[@class=&apos;RefBookTitle&apos;]|!.//*[@class=&apos;RefThesisTitle&apos;]|!.//*[@class=&apos;RefPrePrintTitle&apos;]|!.//*[@class=&apos;RefClinicalTitle&apos;]|!.//*[@class=&apos;RefNewsPaperTitle&apos;]|!.//*[@class=&apos;RefNewsPaperSource&apos;]|!.//*[@class=&apos;RefJournalTitle&apos;]|!.//*[@class=&apos;RefReportTitle&apos;]|!.//*[@class=&apos;RefDataTitle&apos;]|!.//*[@class=&apos;RefPatentTitle&apos;]|.//*[@class=&apos;RefWebSiteTitle&apos; or @class=&apos;RefWebSite&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefWebSiteTitle\">Website Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSiteTitle\" data-selector=\".//*[@class=&apos;RefWebSiteTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefWebSiteSource\">Source</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSiteSource\" data-selector=\".//*[@class=&apos;RefWebSiteSource&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"6\"><div class=\"col s2\"><label data-label=\"RefPublisherName\">Organisation Name</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLAD\" data-selector=\".//*[@class=&apos;RefLAD&apos;]\"/><label data-label=\"RefLAD\">Accessed Date</label></div></div></div><div data-ref-type=\"Preprint\" data-if-selector=\".//*[@class=&apos;RefPrePrintTitle&apos; or @class=&apos;RefPreprintTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-selector=\".//*[@class=&apos;RefAuthor&apos;]\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"2\"><div class=\"col s2\"><label data-label=\"RefPrePrintSource\">Preprint Source</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPrePrintSource\" data-selector=\".//*[@class=&apos;RefPrePrintSource&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row\" row-index=\"4\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPrePrintLink\" data-selector=\".//*[@class=&apos;RefPrePrintLink&apos;]\"/><label data-label=\"RefPrePrintLink\">Preprink Link</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div></div></div><div data-ref-type=\"Clinicaltrial\" data-if-selector=\".//*[@class=&apos;RefClinicalTitle&apos; or @class=&apos;RefClinicaltrialTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-selector=\".//*[@class=&apos;RefAuthor&apos;]\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"2\"><div class=\"col s2\"><label data-label=\"RefClinicalTitle\">Clinical Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefClinicalTitle\" data-selector=\".//*[@class=&apos;RefClinicalTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row\" row-index=\"4\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefClinicalLink\" data-selector=\".//*[@class=&apos;RefClinicalLink&apos;]\"/><label data-label=\"RefClinicalLink\">Clinical Link</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div></div></div><div data-ref-type=\"Report\" data-if-selector=\".//*[@class=&apos;RefReportTitle&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author/Collab</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-jq-selector=\".RefAuthor,.RefCollaboration\" data-class-tmp=\"true\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefReportNumber\">Report No</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefReportNumber\" data-selector=\".//*[@class=&apos;RefReportNumber&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefReportTitle\">Report Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefReportTitle\" data-selector=\".//*[@class=&apos;RefReportTitle&apos;]\"/></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherLoc\" data-selector=\".//*[@class=&apos;RefPublisherLoc&apos;]\"/><label data-label=\"RefPublisherLoc\">Publisher location</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefPublisherName\" data-selector=\".//*[@class=&apos;RefPublisherName&apos;]\"/><label data-label=\"RefPublisherName\">Publisher name</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefFPage\" data-selector=\".//*[@class=&apos;RefFPage&apos;]\"/><label data-label=\"RefFPage\">First page</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLPage\" data-selector=\".//*[@class=&apos;RefLPage&apos;]\"/><label data-label=\"RefLPage\">Last page</label></div></div><div class=\"row\" row-index=\"8\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLAD\" data-selector=\".//*[@class=&apos;RefLAD&apos;]\"/><label data-label=\"RefLAD\">Accessed date</label></div></div><div class=\"row\" row-index=\"9\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDOI\" data-selector=\".//*[@class=&apos;RefDOI&apos;]\" data-message=\"{&apos;keyup&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\"/><label data-label=\"RefDOI\">DOI</label> <span class=\"retain-doi\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;retainDoi&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" style=\"position: absolute;left: 18%;bottom: 100%;\"><input class=\"filled-in\" type=\"checkbox\" id=\"retain-doi\" name=\"retain-doi\" value=\"retain-doi\"/><label for=\"retain-doi\" style=\"padding-left: 25px;\">Retain DOI</label></span> </div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefISBN\" data-selector=\".//*[@class=&apos;RefISBN&apos;]\"/><label data-label=\"RefISBN\">ISBN</label></div></div></div><div data-ref-type=\"Newspaper\" data-if-selector=\".//*[@class=&apos;RefNewsPaperTitie&apos; or @class=&apos;RefNewsPaperSource&apos;]\"><div class=\"row input-field\" row-index=\"1\"><div class=\"col s2\"><label data-label=\"RefAuthor\">Author</label></div><div class=\"col s10\"> <span data-template=\"true\" data-placeholder=\"Surname Initials\" data-class=\"RefAuthor\" data-selector=\".//*[@class=&apos;RefAuthor&apos;]\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> <span data-template=\"true\" data-placeholder=\"Collaboration\" data-class=\"RefCollaboration\" data-type=\"htmlComponent\" data-message=\"{&apos;click&apos;:{&apos;funcToCall&apos;: &apos;addAuthor&apos;,&apos;channel&apos;:&apos;components&apos;,&apos;topic&apos;:&apos;reference&apos;}}\" data-validate=\"required\" contenteditable=\"true\"/> </div></div><div class=\"row input-field\" row-index=\"2\"><div class=\"col s2\"><label data-label=\"RefCollaboration\">Collaboration</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefCollaboration\" data-selector=\".//*[@class=&apos;RefCollaboration&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"3\"><div class=\"col s2\"><label data-label=\"RefYear\" data-required=\"true\">Year</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefYear\" data-selector=\".//*[@class=&apos;RefYear&apos;]\" data-validate=\"required\"/></div></div><div class=\"row input-field\" row-index=\"4\"><div class=\"col s2\"><label data-label=\"RefNewsPaperTitle\">News Title</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefNewsPaperTitle\" data-selector=\".//*[@class=&apos;RefNewsPaperTitle&apos;]\"/></div></div><div class=\"row input-field\" row-index=\"5\"><div class=\"col s2\"><label data-label=\"RefNewsPaperSource\">Source</label></div><div class=\"col s10\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefNewsPaperSource\" data-selector=\".//*[@class=&apos;RefNewsPaperSource&apos;]\"/></div></div><div class=\"row\" row-index=\"6\"><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefFPage\" data-selector=\".//*[@class=&apos;RefFPage&apos;]\"/><label data-label=\"RefFPage\">Fpage</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefMonth\" data-selector=\".//*[@class=&apos;RefMonth&apos;]\"/><label data-label=\"RefMonth\">Month</label></div><div class=\"input-field col s4\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefDay\" data-selector=\".//*[@class=&apos;RefDay&apos;]\"/><label data-label=\"RefDay\">Day</label></div></div><div class=\"row\" row-index=\"7\"><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefWebSite\" data-selector=\".//*[@class=&apos;RefWebSite&apos;]\"/><label data-label=\"RefWebSite\">Website</label></div><div class=\"input-field col s6\"><p data-type=\"htmlComponent\" type=\"text\" contenteditable=\"true\" class=\"text-line\" data-class=\"RefLAD\" data-selector=\".//*[@class=&apos;RefLAD&apos;]\"/><label data-label=\"RefLAD\">Accessed date</label></div></div></div>", "category": "Configuration", "public": false, "hidden": false}, {"message": "GET getrefcomponent with customer=bmj, project=bmjoq, doi=bmjoq-2023-00235709", "status": "200", "url": "/api/getrefcomponent/?customer=bmj&project=bmjoq&doi=bmjoq-2023-00235709&type=getCitation", "method": "get", "expectedOutput": {"citation": {"includeFloatCitationsAfterBody": "true", "data-trigger-stage": "Pre-editing", "jrnlBibRef": {"type": "R", "startPunc": "", "endPunc": " ", "interPunc": " ", "rangeChar": "&#x2013;", "formatting": "sup", "citationType": "0", "formatType": "4", "separator": " ", "prefix": "", "suffix": "", "labelSuffix": "", "flipPunc": ".,", "flipNotPunc": ";:", "in-text": "false", "ignoreSubarticleID": "true", "cslStyle": "BMJ-Vancouver.csl", "skipValidationAPH": "true"}, "jrnlVidRef": {"type": "V", "singularString": "video ", "sentenceStartSingular": "Video ", "sentenceStartPlural": "Videos ", "prefix": "", "suffix": "", "pluralString": "videos ", "interPunc": ", ", "labelString": "Video ", "startPunc": "(", "endPunc": ")", "penultimatePunc": " and ", "labelSuffix": ""}, "jrnlFigRef": {"type": "F", "singularString": "figure ", "sentenceStartSingular": "Figure ", "sentenceStartPlural": "Figures ", "prefix": "", "suffix": "", "pluralString": "figures ", "labelString": "Figure ", "flipPunc": "", "interPunc": ", ", "startPunc": "(", "endPunc": ")", "penultimatePunc": " and ", "labelSuffix": "", "replaceUsingRegex": "/[\\-]/|||&#x2013;", "partLabelSpacing": "false", "partLabelRange": "&#x2013;", "format": [{"type": "S", "singularString": "S ", "pluralString": "S ", "startPunc": "", "endPunc": "", "renumber": "false"}, {"type": "SC", "singularString": "source code ", "pluralString": "source codes ", "startPunc": "", "endPunc": "", "prefix": "&#x2014;", "suffix": "", "renumber": "false"}, {"type": "SD", "singularString": "source data ", "pluralString": "source datas ", "startPunc": "", "endPunc": "", "prefix": "&#x2014;", "suffix": "", "renumber": "false"}]}, "jrnlTblRef": {"type": "T", "singularString": "table ", "sentenceStartSingular": "Table ", "sentenceStartPlural": "Tables ", "prefix": "", "suffix": "", "pluralString": "tables ", "labelString": "Table ", "interPunc": ", ", "startPunc": "(", "endPunc": ")", "penultimatePunc": " and ", "labelSuffix": "", "replaceUsingRegex": "/[\\-]/|||&#x2013;", "format": [{"type": "SC", "singularString": "source code ", "pluralString": "source codes ", "startPunc": "", "endPunc": "", "prefix": "&#x2014;", "suffix": "", "renumber": "false"}, {"type": "SD", "singularString": "source data ", "pluralString": "source datas ", "startPunc": "", "endPunc": "", "prefix": "&#x2014;", "suffix": "", "renumber": "false"}]}, "jrnlBoxRef": {"type": "B", "singularString": "Box ", "prefix": "", "suffix": "", "pluralString": "Boxes ", "interPunc": ", ", "startPunc": "", "endPunc": "", "penultimatePunc": " and ", "labelSuffix": "", "replaceUsingRegex": "/[\\-]/|||&#x2013;"}, "jrnlSupplRef": [{"type": "SF", "singularString": "online supplemental figure ", "sentenceStartSingular": "Online supplemental figure ", "sentenceStartPlural": "online supplemental figures ", "prefix": "", "suffix": "", "pluralString": "online supplemental figures ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false", "replaceUsingRegex": "/[\\-]/|||&#x2013;"}, {"type": "ST", "singularString": "online supplemental table ", "sentenceStartSingular": "Online supplemental table ", "sentenceStartPlural": "online supplemental tables ", "prefix": "", "suffix": "", "pluralString": "online supplemental tables ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false", "replaceUsingRegex": "/[\\-]/|||&#x2013;"}, {"type": "SA", "singularString": "online supplemental audio ", "sentenceStartSingular": "Online supplemental audio ", "sentenceStartPlural": "online supplemental audios ", "prefix": "", "suffix": "", "pluralString": "online supplemental audios ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false"}, {"type": "SV", "singularString": "online supplemental video ", "sentenceStartSingular": "Online supplemental video ", "sentenceStartPlural": "online supplemental videos ", "prefix": "", "suffix": "", "pluralString": "online supplemental videos ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false"}, {"type": "SM", "singularString": "online supplemental material ", "sentenceStartSingular": "Online supplemental material ", "sentenceStartPlural": "online supplemental materials ", "penultimatePunc": " and ", "prefix": "", "suffix": "", "pluralString": "online supplemental materials ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false"}, {"type": "SAP", "singularString": "online supplemental appendix ", "sentenceStartSingular": "Online supplemental appendix ", "sentenceStartPlural": "online supplemental appendixs ", "prefix": "", "suffix": "", "pluralString": "online supplemental appendixs ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false"}, {"type": "SP", "singularString": "online supplemental file ", "sentenceStartSingular": "Online supplemental file ", "sentenceStartPlural": "online supplemental files ", "prefix": "", "suffix": "", "pluralString": "online supplemental files ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false"}, {"type": "AT", "singularString": "Abstract translation ", "sentenceStartSingular": "Abstract translation ", "sentenceStartPlural": "abstract translations ", "prefix": "", "suffix": "", "pluralString": "Abstract translations ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false", "citationNotNeed": "true"}, {"type": "VA", "singularString": "Visual Abstract ", "sentenceStartSingular": "Visual Abstract ", "sentenceStartPlural": "Visual Abstracts ", "prefix": "", "suffix": "", "pluralString": "Abstract translations ", "labelSuffix": "", "startPunc": "(", "endPunc": ")", "renumber": "false", "citationNotNeed": "true"}]}}, "category": "Configuration", "public": false, "hidden": false}]