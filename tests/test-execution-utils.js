/**
 * Test Execution Utilities
 * 
 * This file contains the main test execution logic extracted from api.test.js
 * to be shared across all endpoint test files.
 */

const fs = require('fs');
const path = require('path');
const libxmljs = require('libxmljs');
const { makeRequest, replacePlaceholders } = require('./test-utils');
require('dotenv').config();

/**
 * Execute a single test case
 * @param {Object} iterator - Test case configuration
 * @param {Object|string} target - Either Express app instance or base URL
 * @returns {Promise<Object>} Test execution result
 */
async function executeTestCase(iterator, target) {
    // Handle file encoding if needed
    if (iterator.encode == true) {
        if (iterator.data.content) {
            iterator.data.content = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
            iterator.data.content = encodeURIComponent(iterator.data.content);
        } else if (iterator.data.fileData) {
            iterator.data.fileData = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.fileData, 'utf8');
            iterator.data.fileData = encodeURIComponent(iterator.data.fileData);
        }
    } else if (iterator.encode == false) {
        if (/applyhouserules/g.test(iterator.url)) {
            iterator.data.content = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
        } else {
            iterator.data.data = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.data.content, 'utf8');
        }
    }
    
    // Replace placeholders
    iterator = replacePlaceholders(iterator);
    
    let response;
    
    // Make the request
    try {
        response = await makeRequest(iterator, target);
    } catch (e) {
        console.log(e, "Request Failed");
        throw e;
    }
    
    // console.log({
    //     url: iterator.url, 
    //     status: response.status, 
    //     text: response.text, 
    //     iterator
    // });
    
    return { iterator, response };
}

/**
 * Validate test case response
 * @param {Object} iterator - Test case configuration
 * @param {Object} response - HTTP response
 * @returns {Array} Array of validation errors (empty if all pass)
 */
function toType(x) {
    if (x === null) return 'null';
    if (Array.isArray(x)) return 'array';
    return typeof x;
}

function buildSchema(sample) {
    const t = toType(sample);
    if (t === 'array') {
        if (sample.length === 0) return { type: 'array', items: 'any' };
        return { type: 'array', items: buildSchema(sample[0]) };
    }
    if (t === 'object') {
        const props = {};
        for (const k of Object.keys(sample)) props[k] = buildSchema(sample[k]);
        return { type: 'object', properties: props };
    }
    return { type: t };
}

function validateAgainstSchema(schema, data, errors, path = '') {
    const addErr = (msg) => errors.push(new Error((path ? path + ': ' : '') + msg));
    if (!schema) return true;
    const t = toType(data);
    if (schema.type === 'array') {
        if (t !== 'array') return addErr(`Expected array, got ${t}`);
        if (schema.items === 'any') return true;
        for (let i = 0; i < data.length; i++) {
            validateAgainstSchema(schema.items, data[i], errors, path + `[${i}]`);
        }
        return true;
    }
    if (schema.type === 'object') {
        if (t !== 'object') return addErr(`Expected object, got ${t}`);
        const props = schema.properties || {};
        for (const k of Object.keys(props)) {
            validateAgainstSchema(props[k], data?.[k], errors, path ? path + '.' + k : k);
        }
        return true;
    }
    if (schema.type !== t) return addErr(`Expected ${schema.type}, got ${t}`);
    return true;
}

function validateResponse(iterator, response) {
    const errors = [];

    // Validate status code
    try {
        expect(response.status).toEqual(Number(iterator.status));
    } catch (e) {
        errors.push(e);
    }

    // Schema-only validation path
    const schemaOnly = iterator.schemaOnly === true || process.env.SCHEMA_ONLY === '1';
    if (schemaOnly && Number(iterator.status) === 200) {
        let data = undefined;
        if (typeof response.body !== 'undefined' && response.body !== null && response.body !== '') {
            data = response.body;
        } else {
            try { data = JSON.parse(response.text); } catch (_) { data = undefined; }
        }
        if (data !== undefined) {
            try {
                if (iterator.schema) {
                    validateAgainstSchema(iterator.schema, data, errors);
                } else if (iterator.expectedOutput) {
                    const schema = buildSchema(iterator.expectedOutput);
                    validateAgainstSchema(schema, data, errors);
                }
            } catch (e) {
                errors.push(new Error('Schema validation failed: ' + e.message));
            }
        }
        return errors;
    }

    // Validate expected output
    if (iterator.expectedOutput) {
        if (typeof iterator.expectedOutput === typeof response.text) {
            try {
                expect(response.text).toEqual(iterator.expectedOutput);
            } catch (e) {
                errors.push(e);
            }
        } else {
            try {
                expect(response.text).toBe(JSON.stringify(iterator.expectedOutput));
            } catch (e) {
                errors.push(e);
            }
        }
    }
    // Validate HTML file output
    else if (iterator.fileLocation && iterator.fileLocation.includes('.html')) {
        let responseText = response.text;
        
        // Apply text transformations for specific endpoints
        if (iterator.fileLocation.includes('applyhouserules') || iterator.fileLocation.includes('structurecontent')) {
            responseText = responseText.replace(/Job ID: (.*?)(Table|\<res|\n|\s)/g, 'Job ID: Test-1234567890$2');
            responseText = responseText.replace(/\<span class\=(\"|\&quot\;)query\-time(\"|\&quot\;)\>(.*?)\<\/span\>/g, '<span class="query-time">Nov 02 1996 (06:12)</span>');
            responseText = responseText.replace(/\sdata\-date\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, ' data-date="Nov 02 1996 (06:12)"');
            responseText = responseText.replace(/data\-time\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, 'data-time="Test-time"');
            responseText = responseText.replace(/\sid\=(\"|\&quot\;)(\d.*?)(\"|\&quot\;)/g, ' id="Test-id"');
            responseText = responseText.replace(/data\-cid\=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, 'data-cid="Test-cid"');
            responseText = responseText.replace(/\sdata-rid=(\"|\&quot\;)(.*?)(\"|\&quot\;)/g, ' data-rid="test-12345678"');
            responseText = responseText.replace(/\stitle="Inserted(.*?)(\"|\&quot\;)/g, ' title="Inserted by cross-ref - test-date"');
            responseText = responseText.replace(/\stitle="Inserted(.*?)(\"|\&quot\;)/g, ' data-rid="test-12345678"');
            responseText = responseText.replace(/\stitle="Delete(.*?)(\"|\&quot\;)/g, ' title="Deleted by cross-ref - test-date"');
        }
        
        // Write output file if specified
        if (iterator.outputfileLocation) {
            fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, responseText, {
                mode: 0o755
            });
        }
        
        // Compare with expected file
        let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8');
        try {
            expect(file.trim()).toEqual(responseText?.trim() || response.body?.toString()?.trim());
        } catch (e) {
            errors.push(e);
        }
    } 
    // Validate JSON file output
    else if (iterator.fileLocation && iterator.fileLocation.includes('.json')) {
        let responseText = response.text.replace(/\"took\":\d+\,/g, '');
        let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8');
        try {
            expect(JSON.parse(file.trim())).toEqual(JSON.parse(responseText.trim()));
        } catch (e) {
            errors.push(e);
        }
    } 
    // Validate other file outputs
    else if (iterator.fileLocation) {
        if (iterator.outputfileLocation) {
            fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, response.text, {
                mode: 0o755
            });
        }
        let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.fileLocation, 'utf8');
        try {
            expect(file.trim()).toEqual(response.text.trim());
        } catch (e) {
            errors.push(e);
        }
    } 
    // Validate JSON parameters
    else if (iterator.params) {
        try {
            const parsedResponse = JSON.parse(response.text);
            let dataToValidate;

            // Extract the portion of the JSON to validate based on key path
            if (iterator.param && iterator.param.key) {
                const keyPath = Array.isArray(iterator.param.key) ? iterator.param.key : iterator.param.key.split('.');
                dataToValidate = keyPath.reduce((obj, key) => obj?.[key], parsedResponse);
            } else {
                dataToValidate = parsedResponse;
            }

            // Convert to array if not already
            if (!Array.isArray(dataToValidate)) {
                dataToValidate = [dataToValidate];
            }

            // Check all specified keys exist in the response
            if (iterator.param && iterator.param.parameters && iterator.param.parameters.length > 0) {
                for (const obj of dataToValidate) {
                    for (const param of iterator.param.parameters) {
                        if (typeof param.name === 'string' && !param.name.includes('.')) {
                            try {
                                expect(obj).toHaveProperty(param.name);
                                
                                if (param.hasOwnProperty('value')) {
                                    try {
                                        expect(obj[param.name]).toEqual(param.value);
                                    } catch (e) {
                                        errors.push(e);
                                    }
                                }
                            } catch (e) {
                                errors.push(e);
                            }
                        }
                    }
                }
            }
        } catch (e) {
            errors.push(e);
        }
    } 
    // Validate XPath operations
    else if (iterator.xpath_name) {
        try {
            const doc = libxmljs.parseXml(response.text);
            
            if (iterator.xpath_name.remove_notes) {
                for (const element of iterator.xpath_name.remove_notes) {
                    let matchElements = doc.find(element);
                    for (const matchElement of matchElements) {
                        matchElement.text("");
                    }
                }
            }
            
            const article = doc.root().toString();
            
            if (iterator.outputfileLocation) {
                fs.writeFileSync(process.env.SUPERTEST_BASE_PATH + iterator.outputfileLocation, article, {
                    mode: 0o755
                });
            }
            
            let file = fs.readFileSync(process.env.SUPERTEST_BASE_PATH + iterator.xpath_name.fileLocation, 'utf8');
            try {
                expect(file.trim()).toEqual(article.trim());
            } catch (e) {
                errors.push(e);
            }
        } catch (e) {
            errors.push(e);
        }
    }
    
    return errors;
}

/**
 * Create a test suite for a configuration file
 * @param {string} configFile - Configuration file name
 * @param {Array} configJSON - Array of test cases
 * @param {Object|string} target - Either Express app instance or base URL
 * @returns {Function} Jest describe function
 */
function createTestSuite(configFile, configJSON, target) {
    return describe(`/ Validate ${configFile} API`, () => {
        for (let iterator of configJSON) {
            const only200 = process.env.ONLY_200 === '1';
            const statusNum = Number(iterator.status);
            const testFn = only200 && statusNum !== 200 ? it.skip : it;
            testFn(`${iterator.message}`, async () => {
                console.log(`Running test: ${iterator.message}`);
                const { iterator: processedIterator, response } = await executeTestCase(iterator, target);
                const errors = validateResponse(processedIterator, response);

                if (errors.length > 0) {
                    throw new Error(errors.map(e => e.message).join('\n'));
                }
            });
        }
    });
}

module.exports = {
    executeTestCase,
    validateResponse,
    createTestSuite
};
