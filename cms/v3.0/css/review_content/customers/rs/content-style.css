@import url("museosans.css");

/** Start of Meta and front **/
#contentContainer {
    color: #212121;
    font-family: Georgia, serif;
    font-size: 16px;
    line-height: 24px;
    margin: 0 24px;
    padding: 0;
}
.jrnlAuthorGroup .jrnlOrcid, .contrib-section .contrib-lists [for="other"]{
    display: none
  }
#contentContainer .jrnlPresAdd .jrnlFNPara{
	margin: 0px;
	line-height: 18px;
}
#contentContainer .jrnlPresAdd .label:after {
    content: "\A0Present Address:\A0\A0";
    font-weight: bold;
	font-size: 13px;
}
    #contentContainer .jrnlArtSubTitle, .jrnlRRH, .jrnlLRH{
        display: none;
    }
#contentContainer ul, #contentContainer ol {
    margin: 0 0 24px;
    padding-left: 36px;
}
#contentContainer ul li, #contentContainer ol li {
    line-height: : 24px;
}
#contentDivNode .jrnlOtherFNGroup{
    display: none;
}
#contentContainer ul {
    list-style: disc;
}
#contentContainer ol {
    list-style: decimal;
}
span.jrnlDefTerm + span.jrnlDefinition:before {
    content: ', ';
}
#contentDivNode span.jrnlDefItem:not(:last-child):after {
    content: '; ';
}
#contentDivNode span.jrnlDefItem:last-child:after {
    content: '.';
}
#contentContainer #contentDivNode .jrnlNotesDefList .jrnlDefTerm:after{
    content: " ";
}
#contentContainer #contentDivNode .jrnlNotesDefList .jrnlDefTerm{
   font-weight: bold;
}
#contentContainer #contentDivNode .jrnlNotesDefList .removeNode, #contentContainer div[data-inline] .citObject, #contentContainer div[data-inline] .citeCount{
   visibility:hidden;
}
#contentContainer .jrnlID, #contentContainer .jrnlDOI {
    color: #888;
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 11px;
    line-height: 24px;
    margin: 0;
    padding: 0;
    letter-spacing: .5px;
}
#contentDivNode .contentBtn {
    background-color: #4caf50;
    border-radius: 21px;
    padding-left: 5px;
    padding-right: 5px;
    font-size: 0.9rem !important;
}
#contentDivNode .jrnlHead1, #contentDivNode h1 {
    clear: both;
    color: #020202;
    font-family: "Avenir Next", arial, sans-serif;
    font-size: 25px;
    font-weight: 700;
    line-height: 27px;
    margin: 0px;
    padding: 35px 0px 18px;
}
#contentContainer .jrnlArtTitle{
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 2.5rem;
    line-height: 48px;
    //text-align: center;
}
#contentContainer .jrnlAuthors,#contentContainer .jrnlGroupAuthors{
    font-size: 16px;
    line-height: 30px;
    margin: 0;
    text-align: center;
}
#contentContainer .jrnlAuthorGroup, #contentContainer .jrnlGroupAuthor{
    font-family: "Avenir Next", arial, sans-serif;
    display: inline-block;
    height: 32px;
    font-size: 14px;
    font-weight: 500;
    line-height: 32px;
    padding: 0 8px;
    color: black;
	cursor:pointer;
    border-radius: 16px;
    background-color: #beebff;
    margin-bottom: 6px;
    margin-right: 10px;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentContainer [class*=Author] .jrnlPresAddRef, #contentContainer [class*=Author] .jrnlFNRef, #contentContainer [class*=Author] .jrnlEqContribRef, [data-component="jrnlAuthorGroup_edit"] .footnote-section{
	font-size: 75%;
    vertical-align: super;
    line-height: 0px;
    margin-left: 2px;
}
#contentContainer .jrnlAuthorGroup:hover{
    box-shadow:0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentContainer .jrnlAffGroup, #contentContainer .jrnlPresAddGroup {
    margin-top: 12px;
    color: black !important;
    text-align: center;
}
#contentContainer .jrnlAffGroup:nth-of-type(2){
    //display: none;
}
#contentDivNode  .jrnlAffGroup .jrnlAff:hover, #contentDivNode  .jrnlPresAddGroup .jrnlPresAdd:hover {
    box-shadow:0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentContainer .jrnlPresAdd .jrnlFNPara{
	margin: 0px;
	line-height: 18px;
}
.pdfViewHeader span [data-name="Proofview-ExportPDFAHS"]{
    display:inline;
  }
#contentContainer[data-role="author"] .jrnlTblBlock .floatHeader span[data-tablesetter="new"]{
    display:none;
  }
  #contentContainer[data-role="author"] .jrnlFigMeta{
    display:none;
  }
  #contentContainer[data-role="author"] .jrnlDefList, #contentContainer[data-role="publisher"] .jrnlDefList{
    display:none;
  }  
#contentContainer .jrnlFundingGroup:not([data-topic=PopUp]) .jrnlFN {
    width:100% !important;
    background-color: #effaff;
    cursor: pointer !important;
    padding: 0px 8px !important;
    background-color: rgba(190,235,255,0.25);
    font-family: "Avenir Next", arial, sans-serif !important;
    font-weight: 300 !important;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12) !important;
    text-align: left !important;
    border-radius: 13px !important;
    margin-bottom: 10px !important;
}
.jrnlConFNGroup .jrnlFNPara{
	display: none;
}
.datasamerole{
    display: inline !important;
  }
.jrnlPermission .jrnlLicensePara:before {
	content: ' '
}
.jrnlPermission .jrnlLicensePara {
	display: inline;
}  
#contentContainer .jrnlFundingGroup .jrnlFN .jrnlFNPara:empty:before {
    content: 'Funding Statement';
    font-family: "Avenir Next", arial, sans-serif !important;
    font-weight: bold;
    font-size: 1.5rem;
}
.templates [data-name="jrnlBibRef"] .row [data-label="jrnlCitePageNum"]{
    display:none
  }
  .templates [data-name="jrnlBibRef"] .row [data-class="jrnlCitePageNum"]{
    display:none
  }
#contentContainer .jrnlAff, #contentContainer .jrnlPresAdd {
	font-family: "Avenir Next", arial, sans-serif;
    font-weight: 300;
    font-size: 14px;
    line-height: 24px !important;
    color: black !important;
    margin: 0;
    margin-bottom: 8px !important;
    margin-right: 12px !important;
    padding: 0px 6px 0px 1rem !important;
	//border: 1px solid #ddd;
    border-radius: 15px;
    background-color: rgba(190, 235, 255, 0.25);
    display: inline-block;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    text-align: left;
}
#contentContainer .jrnlPresAdd{
	background-color: rgba(220, 220, 220, 0.25);
}
#contentDivNode  .jrnlPubDetails{
    border: 1px solid #ddd !important;
    border-left: none !important;
    border-right: none !important;
}
#contentDivNode .jrnlAbsGroup{
    border-top: 1px solid gray !important;
    border-bottom: 1px solid gray !important;
    margin-top: 15px !important;
    margin-bottom: 15px !important;
}
#contentDivNode .jrnlSubject[data-template-button]{
	padding: 3px 4px;
}

#contentDivNode .jrnlMajorDatasets{
    border-top: 1px solid gray !important;
    margin-top: 15px !important;
    margin-bottom: 15px !important;
}
#contentDivNode .award-group, #contentDivNode .jrnlDatasets {
    background-color: #effaff;
    //border: 1px solid #ddd;
    padding: 0px 8px;
    background-color: rgba(190, 235, 255, 0.25);
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 300;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    text-align: left;
    border-radius: 13px;
    margin-bottom: 10px;
}
#contentDivNode .award-group .jrnlAwardRecipients{
	display: none
}
#contentDivNode .jrnlDatasets > span + span:before {
    content: ', ';
}
#contentDivNode .jrnlDataAuthors > span + span:before{
	content: ', ';
}
[data-stage-name="Author review"] .jrnlAuthorContributorsGroup{

    pointer-events:none;	
    
    }
#contentDivNode[data-role="author"] .jrnlThemeGroup{
    pointer-events:none;    
}  
#contentDivNode .jrnlDatasets .jrnlExtLink, #contentDivNode .jrnlDatasets .jrnlDOI{
	display: none;
}
#contentDivNode  .jrnlFundingGroup .institution{
  margin-top: 0px;
  margin-bottom: 8px;
}
#contentDivNode .jrnlFundingGroup ul, #contentDivNode  .jrnlFundingGroup ol{
  margin: 0px;
}
#contentDivNode .award-group .institution + span.jrnlAwardID:before {
    content: ' (';
}
#contentDivNode .award-group .jrnlAwardID + span.jrnlAwardID:before {
    content: ', ';
}
#contentDivNode .award-group span.jrnlAwardID:last-of-type:after {
    content: ')';
}
#contentDivNode .jrnlAuthorGroup + .jrnlAuthorGroup:before{
  display: none;
}

#contentContainer h1, #contentContainer .jrnlHead1, #contentContainer .jrnlHead2, #contentContainer .jrnlHead3, #contentContainer .jrnlHead4, #contentContainer .jrnlHead5, #contentContainer blockquote, #contentContainer .jrnlAbsTitle {
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    margin: 5px 0px 5px;
    padding: 0;
}
#contentContainer .jrnlFundingHead, #contentContainer .jrnlAckHead {
    margin: 24px 0 12px;
    padding-top: 0px;
}
#contentContainer .jrnlFundingHead{
  margin: 0px 0px 10px !important;
}
#contentContainer *[class^="jrnlHead"] .label:after {
    content: " ";
}
#contentContainer .jrnlHead2, #contentContainer .jrnlAbsTitle{
    padding-top: 0px;
    font-size: 22px;
    line-height: 24px;
    padding-top: 12px;
    margin-bottom: 12px;
}
#contentContainer .jrnlHead3{
    font-size: 20px;
    line-height: 24px;
    padding-top: 12px;
    margin-bottom: 12px;
}
#contentContainer .jrnlHead4{
    font-size: 18px;
    line-height: 24px;
    padding-top: 12px;
    margin-bottom: 12px;
}
#contentContainer .jrnlHead5{
    font-size: 16px;
    line-height: 24px;
    padding-top: 10px;
    margin-bottom: 14px;
}
#contentContainer blockquote {
    font-size: 20px;
    line-height: 24px;
    padding-top: 48px;
}
table blockquote {
    font-size: 13px !important;
}
#contentContainer p:last-child{
    margin: 0px !important;
}
#contentContainer strong, #contentContainer [class*="Caption"] .label {
    font-weight:bold;
}
#contentContainer .jrnlTblBlock thead {
    font-weight:bold;
}
.datepickpopup{
  margin-bottom: 0px !important;
  margin-top: 0px;
  border: 1px solid #16a1e7;
  cursor: pointer;
  border-radius: 3px;
  margin-left: 4%;
  background-color: #03a9f4;
  color: white;
  box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
  padding-left: 5%;
  line-height: 17px;
  padding-top: 0px !important;
  height: 52%;
  width: 40% !important;
}
#flexdate{
  display: flex !important;
}
#contentDivNode .jrnlAbsGroup .jrnlAbsPara{
    /*color: #04010a;
    font-family: Helvetica,Arial,Verdana,sans-serif;
    font-size: 15px;
    line-height: 1.75em;
    font-weight: 300;
    padding-top: 20px;*/ /*Commented by siva kumar on Mar-22-2017*/
    margin: 0;
    padding: 6px 0 6px;
}
#contentContainer .jrnlSecPara, #contentContainer .jrnlPara{
    /*color: #04010a;
    font-family: Helvetica,Arial,Verdana,sans-serif;
    font-size: 15px;
    line-height: 1.75em;
    font-weight: 300;
    margin: 0 0 10px;*/ /*Commented by siva kumar on Mar-22-2017*/
    margin: 0 0 10px;
    padding: 0;
}
#contentContainer .jrnlBibRef, #contentContainer .jrnlFigRef, #contentContainer .jrnlTblRef, #contentContainer .jrnlVidRef,#contentDivNode .jrnlTblFNRef, #contentDivNode .jrnlSupplRef, #contentDivNode .jrnlBoxRef, #contentDivNode .jrnlEqnRef, #contentDivNode .jrnlSchRef{
    /*color:#d54449 !important;*/
    color: #0288D1 !important;
    cursor:pointer;
}
#contentContainer .jrnlFigCaption {
    line-height: 24px;
    margin: 0 0 12px;
    padding: 0;
}
#contentDivNode .jrnlFigBlock {
    padding: 7px;
    margin:5px 0px 10px;
    background: rgba(238, 238, 238, 0.25);
    border: 1px solid #bfb9b9;
    font-size: 13px;
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 500;
}

#contentDivNode .jrnlFigBlock > img {
    margin-bottom: 15px;
}
#contentDivNode .jrnlFigRef:hover, #contentDivNode .jrnlTblRef:hover, #contentDivNode .jrnlVidRef:hover, #contentDivNode .jrnlEqnRef:hover{
    background: #BDBDBD;
}

#contentDivNode .jrnlTblBlock ,#contentDivNode .jrnlInlineTable {
    border: 1px solid #eee;
    margin:5px 0px 10px;
    padding: 7px;
    font-size: 13px;
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 500;
    //background: #efefef;
}

#contentDivNode .jrnlSupplBlock {
    border: 1px solid #b4b3b3;
    margin: 20px 0px 5px;
    padding: 7px;
}

#contentContainer .jrnlTblBlock .label:after, #contentContainer .jrnlFigBlock .label:after, #contentContainer .jrnlSchBlock .label:after {
    content: ' ';
}
#navigationContentDiv .jrnlTblBlock .label, #navigationContentDiv .jrnlFigBlock .label {
    /*display:block;
    background:#eee;
    padding:2px 4px;
    font-size:75%;
    margin-bottom:4px;*/  /*Commented by siva kumar on Mar-22-2017*/
    background:#E0E0E0;
    font-weight: 600;
}
div#changesDivNode * {
    font-size: 85%;
}
#contentContainer table {
    border-collapse: collapse;
    border-spacing: 0;
}

#contentContainer .jrnlEqnPara{
    padding: 18px 0px;
}

#contentDivNode .ReceAccDate{
    margin-bottom: 1rem !important;
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 11px !important;
    border: 1px solid #ddd;
    padding-top: 10px !important;
    display: flex !important;
    width: 100%;
    border-left: none;
    border-right: none;
    padding-bottom: 10px !important;
    }
#contentContainer .jrnlSubjectTitle{
    float: right;
}
#contentContainer p.jrnlPubDetails{
    color: #888;
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 11px;
    padding: 0;
    padding-top: 12px;
    padding-bottom: 13px;
    letter-spacing: .5px;
}
#contentContainer .jrnlSectionHead{
    content: "Sections:\A0\A0";
    font-family: "Avenir Next", arial, sans-serif;
    font-weight:bold !important;
    color: #3e3d40;
    font-size: 18px;
    padding-top: 24px;
    padding-bottom: 18px;
}
#contentContainer p.jrnlPubDetails .jrnlPubDate:before{
    content: ' ';
}
#contentContainer p.jrnlFundingStatement, #contentContainer p.jrnlLicense{
    font-size: 15px;
}
#contentContainer span.RefJournalTitle{
    color: #212121;
}
/** START BY SIVA KUMAR MAR-22-2017 **/
#contentContainer .jrnlRefHead{
    /*color: #888;*/
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 26px;
    line-height: 24px;
    /* text-transform: uppercase; */
    line-height: 24px;
    margin: 24px 0px 12px;
    padding: 0;
    letter-spacing: .5px;
}

#contentContainer .jrnlRefText{
    /*color: #3E3D40;
    font-family: Helvetica,Arial,Verdana,sans-serif;*/
    font-family: "Avenir Next", arial, sans-serif;
    font-size: 15px !important;
    line-height: 160% !important;
    font-weight: 300;
    margin: 12px 0;  /*Commented by siva kumar on Mar-22-2017*/
}
#contentContainer .RefArticleTitle{
    font-family: inherit;
    color: #212121;
}
#contentContainer .jrnlRefText .searchLinks:before{
    content: "\a\a";
    white-space: pre;
    line-height: 0px;
}
#contentContainer a{
    color: #0288D1;
}
#contentContainer a:hover{
    color: #0277BD;
}
#contentContainer .jrnlRefText .searchLinks a{
   /* color: #d54449 !important;
    font-size: 12px;*/  /*Commented by siva kumar on Mar-22-2017*/
    color: #0288D1;
}
#contentContainer .RefYear, #contentContainer .RefBookTitle, #contentContainer .RefVolume, #contentContainer .RefFPage, #contentContainer .RefLPage, #contentContainer .RefDOI, #contentContainer .RefPMID{
    font-family: "Avenir Next", arial, sans-serif;
    color: #212121;
}
#contentContainer .RefAuthor, #contentContainer .RefBookTitle{
    border: 0;
}
/** End of Back **/

/** misc div **/
.jrnlReDate > span + span:before,[data-class="jrnlReDate"] > span + span:before, .jrnlAcDate > span + span:before, [data-class="jrnlAcDate"] > span + span:before, .jrnlPubDate > span + span:before, [data-class="jrnlPubDate"] > span + span:before, .jrnlVorDate > span + span:before, .jrnlPoaDate > span + span:before {
    content: '\A0';
}

#contentContainer .misc *, #contentContainer .jrnlKwdGroup *, #contentContainer .jrnlReDate, #contentContainer .jrnlAcDate, , #contentContainer .jrnlPubDate{
    font-size: 13px !important;
    line-height: 19px !important;
    font-family: Helvetica,Arial,Verdana,sans-serif !important;
    font-weight: 300 !important;
    margin:0px;
}
#contentContainer .misc{
    padding-top: 24px;
    padding-bottom: 18px;
    margin-top: 30px;
    margin-bottom: 24px;
    border-top: 1px solid #b8b8b8;
    border-bottom: 1px solid #b8b8b8;
}
#contentContainer .misc > div{
    margin:10px 0px;
}

#contentContainer .jrnlHistory p{
    display: inline-block;
}
#contentContainer .jrnlHistory .jrnlReDate:before {
    content: 'Received: ';
    font-weight: 900;
}
#contentContainer .jrnlHistory .jrnlPoaDate:before {
    content: 'POA: ';
    font-weight: 900;
}
#contentContainer .jrnlHistory .jrnlVorDate:before {
    content: 'VOR: ';
    font-weight: 900;
}
#contentContainer .jrnlHistory .jrnlAcDate:before {
    content: '\a Accepted: ';
    font-weight: 900;
    white-space: pre;
}
#contentContainer .jrnlPubDate:before {
    content: 'Published: ';
    font-weight: 900;
}
#contentContainer .jrnlPermission p {
    display: inline;
}
#contentContainer .jrnlPermission .jrnlCopyrightStmt + .jrnlLicense:before {
    content: ' ';
    font-weight: 900;
}
#contentContainer table p {
    margin: 0px !important;
}
#contentContainer .body sup, #contentContainer .back sup {
    vertical-align: baseline;
}
#contentContainer .sub-article-id {
    display: none;
}
#contentContainer .body sub, #contentContainer .back sub{
    vertical-align: sub;
}
#contentDivNode p.jrnlKeywordPara:before {
    font-weight: 900;
}
#contentDivNode div.jrnlKwdGroup[data-kwd-group-type="author-keywords"]{
    border-top: 1px solid gray;
    font-size: 13px;
    margin-top: 20px;
}
#contentDivNode .jrnlGroupAuthorHead {
	margin-bottom: 25px;
    font-size: 22px;
    color: #696969;
    background: inherit;
}
#contentDivNode .jrnlGroupAuthorsGroup {
	padding: 5px 10px;
    margin: 12px 0px;
    background: rgba(245, 245, 245, 0.8);
    border-radius: 13px;
}
/*#contentDivNode .jrnlGroupAuthors: <AUTHORS>
	content: "Other contributors\a";
    white-space: pre;
    color: #0560ab;
    padding: 10px 0px;
    display: block;
}*/
#contentDivNode .jrnlGroupAuthors .jrnlAffRef{
	//display: none;
}
#contentDivNode .jrnlGroupAuthors .jrnlRole{
    display: none;
    //color: #6f6f6f;
    //margin-bottom: 10px;
}
#contentContainer .jrnlEditorGroup, #contentContainer .jrnlReviewerGroup, #contentContainer .jrnlEditorGroup .jrnlAff{
    color: #04010a;
    font-family: Helvetica,Arial,Verdana,sans-serif !important;
    font-size: 15px !important;
    line-height: 1.75em;
    font-weight: 300;
    margin: 0 0 10px;
}
#contentContainer .jrnlEditorGroup .jrnlEditor + .jrnlRole:before, #contentContainer .jrnlReviewerGroup .jrnlReviewer + .jrnlAff:before, #contentContainer .jrnlEditorGroup .jrnlEditor + .jrnlAff:before, #contentContainer .jrnlReviewerGroup .jrnlReviewer + .jrnlAff:before, #contentContainer .jrnlEditorGroup .jrnlRole + .jrnlAff:before, #contentContainer .jrnlReviewerGroup .jrnlReviewer + .jrnlAff:before{
    content: ', ';
}
#contentDivNode  .jrnlAffGroup .jrnlAff  .jrnlPostalCode:before {
    content: '\A0';
}
.jrnlGroupMember:after {
    content: " ";
}
.jrnlGroupMember .jrnlAff:after {
    content: ", ";
}
.jrnlGroupMember .jrnlInstitution:after {
    content: ", ";
}
.jrnlGroupMember .jrnlCity:after {
    content: " ";
}
#contentContainer p.jrnlBoxText{
    font-size: 15px;
}
#contentContainer .sub-article p.jrnlBoxText{
    background: #e9f5fb;
    font-size: 14px;
    padding: 15px 20px;
    //margin-bottom: 15px !important;
}
#compDivContent *[data-type="popUp"]:not([data-component*="_edit"]) *[data-class*="Caption"] .label:after {
  content: " ";
  font-weight: bold;
}
#contentContainer span.jrnlFigRef{
    background-color: transparent !important;
}
#contentContainer span.jrnlFigRef:hover{
    color: #0277BD !important;
}
/*jagan*/
[type="checkbox"].filled-in:not(:checked)+label:after{
    height: 16px;
    width: 16px;
    background-color: #beebff !important;
    border: 1px solid #03a9f4 !important;
    top: 3px;
    z-index: 0;
}
[type="checkbox"].filled-in:checked+label:before{
  width: 6px !important;
  height: 11px !important;
  top: 3px !important;
}
[type="checkbox"].filled-in:checked+label:after{
    top: 2px;
    width: 16px;
    height: 16px;
    border: 2px solid #03a9f4 !important;
    background-color: #03a9f4 !important;
    z-index: 0;
}
#compDivContent label[for="corresp-checkbox"]{
    color: #020202 !important;
    font-size: 14px !important;
    padding-left: 23px !important;
    margin-bottom: 15px !important;
}
#compDivContent .author-section, #compDivContent .footnote-section, #compDivContent .footnote-section{
	margin: 10px 0px 10px !important;
}
#compDivContent .aff-section, #compDivContent .pa-section{
	margin: 15px 0px 10px !important;
}
#compDivContent .corresp-section label, #compDivContent .contrib-section label, #compDivContent .competing-section label, #compDivContent .footnote-section label{
	color: #000;
}
#compDivContent .contrib-section{
  margin: 10px 5px 10px 0px !important;
}
#compDivContent .contrib-section .contrib-lists{
  border: 1px solid #9e9e9e;
  border-radius: 4px;
  padding: 15px 25px 15px !important;
  margin-top: 5px !important;
}
#otherArea{
  margin: 0px !important;
  width: 200% !important;
  border: 1px solid #ddd !important;
  padding-top: 6px !important;
  padding-left: 6px !important;
  margin-bottom: 0px !important;
   min-height: 6rem !important;
  border-radius: 4px !important;
  }
#Commentbtn{
    position: absolute;
    top: -1.7rem;
    right: 6.5rem;
    background-color: #03a9f4 !important;
}
#commentbtn:hover{
    background-color: #03a9f4 !important;
}
#addnewbtn{
    font-weight: 600;
    margin-left: 8px !important;
}
#addExistingbtn {
  font-weight: 600;
   display: inline-block;
   position: relative !important;
   margin-left: 8px !important;
}
#addExistingbtn ul{
   font-weight: 500 !important;
}

#addFoot{
    background-color: #bdbdbd;
    display: none;
    padding-top: 7px;
    padding-bottom: 7px;
    padding-left: 7px;
    padding: 7px;
    margin-right: 5px;
    margin-left: 22px;
    color: black;
    text-transform: none;
}
.ContriTextArea{
  border: 1px solid #ddd;
  padding: 3px !important;
  border-radius: 3px;
  min-height: 57px !important;
  width: 70%;
}
#addAnotherFoo{
    color: black;
    background-color: #bdbdbd;
    padding: 7px;
    display: none;
    text-transform: none;
}
#corresMail{
  margin-top: 1rem !important;
}
#other:not(:checked) ~ .input-field, .competing-section #none:checked ~ .input-field, #footnoteNone:checked ~ .input-field{
  display: none;
}
#other:checked ~ .input-field{
	display: inline-block;
    width: 75%;
    margin-left: 5px;
}
#other:checked ~ .input-field .text-box{
	margin: 0px;
    min-height: 2rem;
}
.ethics{
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 26px;
	margin-top: 1rem;
	color: #212121;
}
.subjname{
    font-family: "Avenir Next", arial, sans-serif;
    font-size: 14px;
}
.EthicsType{
  font-size: 15px;
  border: 1px solid #ddd;
  margin-top: 6px;
  padding: 4px;
  border-radius: 5px;
  min-height: 92px;
  padding-left: 11px;
  color:rgba(33, 33, 33, 0.86) !important;
  font-weight: 100;
}
.EthicsType:hover{
  background-color: #f5f5f5 !important;
  color:black !important;
}
.ethicstopic{
  font-size: 15px;
  padding-left: 10px;
  font-weight: 500;
  word-break: break-all;
  font-family: "Avenir Next", arial, sans-serif;
}
.presentAddStyle{
    color: #f5f5f5;
    font-weight: 600;
    background-color: #4caf50;
    border: 1px solid;
    height: 32px;
    line-height: 30px;
    border-radius: 6px;
}
.datetypes{
    margin-top: 10px !important;
}
.tocc{
        padding-left: 30px !important;
        line-height: 18px !important;
}
.Datespace{
    margin-left: 17px !important;
}
.Dateaheader{
   font-size: 20px !important;
    border: 1px solid #ddd;
    color: #03a9f4;
    border-radius: 9px;
    padding-left: 7px;
    background-color: #eee;
}
.deleteDatebtn{
    border: 2px solid #ddd;
    padding-top: 2px;
    padding-bottom: 2px;
    padding-left: 2px;
    padding-right: 2px;
    border-radius: 5px;
    background-color: rgba(255, 0, 0, 0.76) !important;
    color: white;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    font-size: 13px !important;
    display:none;
}
#Datetypestyle{
    background-color: #4caf50;
    border-radius: 21px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: 600;
    cursor: pointer;
    color: white;

}
#Datetypestyle:hover{
    color: white;
    background-color: #4caf50 !important;
}
.AddDatetype{
        border-radius: 21px;
    padding-left: 5px;
    padding-right: 5px;
    font-weight: 500;
    border: 1px solid white;
    cursor: pointer;
    color:white;
    padding-top: 4px;
    background-color: #4caf50;
    padding-bottom: 2px;
    display:none;
}
.AddDatetype:hover{
    color: white;
    background-color: #4caf50 !important;
}
.AddDatetype + span{
    background-color: red !important;
}
#chooseAuthor{
    margin-top:0px !important;
}

#compDivContent #PresentAddressbck{
    background-color:1px solid black;
}
#compDivContent *[data-type="popUp"][data-component*="_edit"] [data-input-editable]{
    border:1px solid black !important;
}
.dateHolder{
	margin-left: 19px;
    color: #ee6e73;
    border: 1px solid #ddd;
    padding: 3px;
    padding-left: 50px;
    padding-right: 50px;
    border-radius: 5px;
}

.dateHolder [data-class="jrnlDay"]:after {
    content: " ";
}

.dateHolder [data-class="jrnlMonth"]:after {
    content: " ";
}

.addDatepick {
    margin-left: 9px;
    cursor: pointer;
}

[type="radio"]+label:before,[type="radio"]+label:after {
    margin:0px !important;
}

#contentContainer .jrnlDateComment {
    display: none;
}
#contentContainer .jrnlDateVersion:before {
    content: "V";
}
#contentContainer .jrnlDateVersion:after {
    content: ": ";
}
[data-component] [data-class="jrnlDateVersion"] {
    width: 40px !important;
    border: 1px solid #ddd !important;
    margin-left: 10px !important;
    border-radius: 4px !important;
}
#compDivContent *[data-type="popUp"] .collection{
	margin-left: 0px !important;
}
.collection-header{
  color: black !important;
}
#contentDivNode .jrnlSubject{
    font-weight: 400 !important;
    pointer-events: none;
}
#contentDivNode .jrnlPriSection{
    font-weight: 400 !important;
    display: block;
}
#contentDivNode .jrnlSubject, #contentDivNode .jrnlArtType{
    cursor: pointer;
    border: 1px solid #16a1e7;
    border-radius: 3px;
    
    /**#379 IE| Clicking Article type - Article type should be displayed as a button not as a image. - Prabakaran.A (<EMAIL>) */
    padding: 8px 8px !important;    
    /*display: inline-block;*/
    
    background-color: #03a9f4;
    color: white;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 600;
    font-size: 11px;
    line-height: 20px;
    text-transform: uppercase;
    margin: 0px 6px 0px !important;
    letter-spacing: .5px;
    text-align: center;
    height: 30px;
    
    /**#379 IE| Clicking Article type - Article type should be displayed as a button not as a image. - Prabakaran.A (<EMAIL>) */
    /*display: inline-block;*/
    /**End of #379*/
}
#contentDivNode .jrnlSecSection{
    font-weight: 400 !important;
    pointer-events: none;
}
#contentDivNode .jrnlSectionGroup .jrnlSecSection:not(:nth-of-type(2)):before{
    content:", ";
    font-weight: 600 !important;
}
#contentDivNode .jrnlSectionGroup .jrnlSecSection:nth-child(2):before{
    content:"Subject Area(s): ";
    font-weight: 600 !important;
}
#contentDivNode .jrnlPriSection:before{
    content:"Subject Category: ";
    font-weight: 600 !important;
}
#contentDivNode .plustoc{
  margin-left: 6px;
  font-weight: 900;
  color: #020202;
  cursor: pointer;
}
#contentDivNode .plustoc:hover{
  color:#4caf50;
}

#contentContainer .jrnlCollaboration_old {
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 300;
    font-size: 16px;
    line-height: 24px;
    margin: 0 0 12px;
}
span.jrnlSurName + span.jrnlGivenName:before {
    content: ' ';
}
#contentContainer span.jrnlAuthorGroup:not(:last-child):after {
    //content: '\A0';
}
#contentContainer .jrnlAffRef + .jrnlAffRef:before {
    content: ',';
}
#contentDivNode .jrnlBoxBlock, #contentDivNode .jrnlInlineBoxBlock{
    margin-top: 15px !important;
    margin-bottom: 15px !important;
    border-top: 1px solid gray;
    border-bottom: 1px solid gray;
}

#contentDivNode .jrnlSupplBlock {
    border: 1px solid #b4b3b3;
    margin: 20px 0px 5px;
    padding: 7px;
}

/*commented by jagan*/
#contentDivNode .tochead{
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    padding: 5px;
}
#contentDivNode  .jrnlAuthors .icon-plus {
    font-size: 11px;
    margin-right: 3px;
}
#contentDivNode .addPresentBtn {
  background-color: #4caf50;
    border-radius: 21px;
    display: table;
    padding-left: 5px;
    cursor: pointer;
    padding-right: 5px;
    height: 19px;
    font-weight: 600;
    font-family: sans-serif;
    color: white;
    font-size: 13px;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    line-height: 18px;
    margin-top: 5px;
    left: 41% !important;
    position: relative;
}
#contentDivNode .addPresentBtn:hover{
      box-shadow: 0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentDivNode .addaffiliBtn {
  background-color: #4caf50;
    border-radius: 21px;
    display: table;
    padding-left: 5px;
    cursor: pointer;
    padding-right: 5px;
    height: 19px;
    font-weight: 600;
    font-family: sans-serif;
    color: white;
    font-size: 13px;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    line-height: 18px;
    margin-top: 5px;
    left:43.4% !important;
    position: relative;
}

#contentDivNode .addtocdate{
      padding: 0px !important;
      height: 20px;
      cursor: pointer;
      text-transform: inherit;
      font-size: 13px;
      font-weight: 600;
      line-height: 21px;
      margin-top: 14px !important;
      padding-left: 4px !important;
      padding-right: 4px !important;
      border-radius: 16px;
      color: white;
      /* date popup*/

}

#contentDivNode .addaffiliBtn:hover{
  box-shadow: 0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentDivNode .AddAffiliBtn{
    border: 1px solid #4caf50;
    background-color: #4caf50;
    padding: 3px;
    cursor: pointer;
    color: white;
    width: 14% !important;
    font-family: Georgia, sans-serif;
    font-size: 12px;
    text-transform: uppercase;
    border-radius: 21px;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    font-weight: 600;
}
#contentDivNode .AddAffiliBtn:hover{
  box-shadow: 0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentDivNode  .tochead .chip{
    margin-bottom: 0px !important;
}
#contentDivNode  .toc{
    cursor: pointer;
    float: left;
    font-size: 15px;
    font-weight: 500;
    margin-right: 6px;
    padding: 0px;
    padding-left: 15px;
    color: #212121;
    font-family: sans-serif;
    padding-right: 15px;
    border-radius: 2px;
    line-height: 25px;
    background-color: rgba(22, 161, 231, 0.48);
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentDivNode  .toc:hover{
     box-shadow: 0 6px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
#contentDivNode .toctext{
    float: left;
    font-size: 15px;
    font-weight: 500;
    line-height: 25px;
    margin-right: 4px;
    color: #020202;
}
#contentDivNode .jrnlRefText .RefEtal, #contentDivNode .jrnlRefText .RefJournalTitle{
    font-style: italic;
}
#contentDivNode .jrnlRefText .RefVolume {
    font-weight: bold;
}

#contentDivNode .jrnlHeaderInfo {
    font-family: 'MuseoSans', "Helvetica", sans-serif !important;
    font-weight: 400;
    padding: 3px;
    margin-top: 0px;
}

#contentDivNode .jrnlHeaderInfo .jrnlPubData {
    display: none;
}

#contentDivNode .jrnlHeaderInfo span[class="jrnlDOI"]:before {
    content: ' | ';
}
#contentDivNode .back > .jrnlFN, #contentDivNode .jrnlConFNGroup, #contentDivNode .jrnlCompIntGroup{
	margin: 15px 0px 5px;
}
#contentDivNode .jrnlCitation, #contentDivNode .jrnlHistory, #contentDivNode .jrnlPermission, #contentDivNode .jrnlCorrAff, #contentDivNode .jrnlFN, #contentDivNode .jrnlKeywordPara, #contentDivNode .jrnlEqualContrib, #contentDivNode .jrnlEqContrib, #contentDivNode .jrnlPubDate {
    font-size: 15px !important;
    font-family: 'MuseoSans', Georgia, "Times New Roman", Times, sans-serif !important;
}
#contentContainer #contentDivNode .jrnlEqContrib {
    background-color: #effaff;
    cursor: pointer;
    padding: 0px 8px;
    background-color: rgba(190,235,255,0.25);
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 300;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    text-align: left;
    border-radius: 13px;
    margin-bottom: 10px;
  }
[data-name="jrnlEqContrib_edit"] .jrnlEqExtra{
    display:none;
}
#contentDivNode .jrnlCorrAff{
    display: inline;
}
#contentDivNode .jrnlCorrAff:before, [data-component="jrnlAuthorGroup"] .jrnlCorrAff:before {
    content: attr(data-symbol)'Correspondence to: ';
    font-weight:bold;
    color: #3e3d40;
}

#contentDivNode .jrnlCorrAff~.jrnlCorrAff:before{
    content:' ; '
}

.RefSlNo:after{
   content:' '; 
}

#contentContainer[data-proof-config-id="rsbm"] #contentDivNode * .RefSlNo::after {
    content: ''
}


#contentDivNode .jrnlPPubDate, #contentDivNode .jrnlPubDate, #contentDivNode .jrnlRevDate, #contentDivNode .jrnlReDate, #contentDivNode .jrnlAcDate {
    display: none;
}

#contentDivNode .jrnlGroupAuthorsGroup {
    padding: 5px 10px;
    margin: 12px 0px;
    border: 1px solid rgb(212, 208, 208);
    border-radius: 13px;
    background: rgba(238, 238, 238, 0.3);
}

.jrnlAuthorGroup .jrnlCorrRef {
    font-size: 0em;
}
.jrnlAuthorGroup .jrnlCorrRef:before {
    content: "\00a0 \e945";
    font-family: 'icomoon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    font-size:1rem
}

#compDivContent *[data-type="popUp"][data-component="jrnlAuthorGroup_edit"] #corresp-checkbox~[data-class="jrnlCorAddress"] {
    display: none;
}

#compDivContent *[data-type="popUp"][data-component="jrnlAuthorGroup_edit"] #corresp-checkbox:checked~[data-class="jrnlCorAddress"] {
    display: block;
}

#compDivContent *[data-type="popUp"][data-component="jrnlAuthorGroup_edit"] #deceased-checkbox~[data-class="jrnlDeceased"] {
    display: none;
}

#compDivContent *[data-type="popUp"][data-component="jrnlAuthorGroup_edit"] #deceased-checkbox:checked~[data-class="jrnlDeceased"] {
    display: block;
}

#contentContainer #contentDivNode .jrnlPermission {
    background-color: #effaff;
    cursor: pointer;
    padding: 0px 8px;
    background-color: rgba(190,235,255,0.25);
    font-family: "Avenir Next", arial, sans-serif;
    font-weight: 300;
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
    text-align: left;
    border-radius: 13px;
    margin-bottom: 10px;
  }

  #contentContainer .jrnlPermission .jrnlCopyrightYear,
  #contentContainer .jrnlPermission .jrnlCopyrightHolder {
    display: none;
}

#contentContainer span.jrnlDegrees:before{
    content: ' ';
}

#contentContainer .jrnlRelArt .jrnlRelArtContainer > span + span:after, #contentContainer .jrnlRelArt .jrnlRelArtContainer .jrnlRelArtAuthorGroup .jrnlRelArtAuthor:after {
    content: ", ";
}

#contentContainer .jrnlRelArt .jrnlRelArtContainer > span:last-child:after {
    content: ".";
}

#contentContainer .jrnlRelArt .jrnlRelArtAuthor .jrnlRelArtSurName:after{ 
    content: " ";
}
#compDivContent label[for="deceased-checkbox"]{
    color: #020202 !important;
    font-size: 14px !important;
    padding-left: 23px !important;
    margin-bottom: 10px !important;
}
#compDivContent label[for="orcid-prefix"]{
    color: #020202 !important;
    font-size: 14px !important;
    padding-top: 5px !important;
    margin-bottom: 10px !important;
}
.jrnlDeceased{
    margin-top: 10px !important;
    margin-bottom: 10px !important;
}
.jrnlAuthorGroup .jrnlFigure{
	display: none;
}
#navContainer .nav-action-icon#conIndexIcon{
    display: none !important;
}
.jrnlSectionGroup{
    display: block;
    font-size: 14px;
    line-height: 24px !important;
    color: #000 !important;
    pointer-events: none;
    margin: 12px 12px 8px 0px !important;
    padding: 0px 6px 0px 1rem !important;
    border-radius: 15px;
    background-color: rgba(190,235,255,0.25);
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
.secondarySections *[data-class="jrnlSubject"]:empty::before{
    content:"Search...";
    color:gray;
    font-weight: 400;
}
.secondarySections .sectionName{
    cursor: pointer;
    display: inline;
    color: #000;
    white-space: pre-wrap;
    word-break: keep-all;
}
.secondarySections .dropdown-list{
    background: #f4f4f6;
    position:unset;
}
#contentContainer .jrnlFootNoteGroup .jrnlFootNote .label + .jrnlFNPara, #compDivContent *[data-type="popUp"] .collection .label + p.jrnlFNPara {
    display: inline-block;
}
#navContainer:not([data-curr-stage="copyeditingcheck"]) .nav-action-icon#navlqcicon{
    display: none !important;
}
.row #infoDivContent:not([data-stage="copyeditingcheck"]) #lqcDivNode {
    display: none !important;
}
#navContainer .nav-action-icon#conIndexIcon, [data-query-action="condition-query"]:not(:contains("The reference doesn")) .query-action .query-delete {
    display: none !important;
}