<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mml="http://www.w3.org/1998/Math/MathML" xmlns:xlink="http://www.w3.org/1999/xlink">
  <xs:import namespace="http://www.w3.org/1999/xlink" schemaLocation="xlink.xsd"/>
	<xs:import namespace="http://www.w3.org/XML/1998/namespace" schemaLocation="xml.xsd"/>
	<xs:import namespace="http://www.niso.org/schemas/ali/1.0/" schemaLocation="ali.xsd"/>
  <xs:attributeGroup name="dataAttributes">
    <xs:attribute name="data-track" type="xs:string" use="optional"/>
    <xs:attribute name="data-username" type="xs:string" use="optional"/>
    <xs:attribute name="data-userid" type="xs:string" use="optional"/>
    <xs:attribute name="data-time" type="xs:string" use="optional"/>
    <xs:attribute name="data-cid" type="xs:string" use="optional"/>
    <xs:attribute name="node-parent-node" type="xs:string" use="optional"/>
    <xs:attribute name="node-parent-insertafter-xpath" type="xs:string" use="optional"/>
    <xs:attribute name="node-parent-insert-xpath" type="xs:string" use="optional"/>
    <xs:attribute name="node-insert-xpath" type="xs:string" use="optional"/>
    <xs:attribute name="node-insertafter-xpath" type="xs:string" use="optional"/>
    <xs:attribute name="data-edited-node" type="xs:boolean" use="optional"/>
    <xs:attribute name="data-edited" type="xs:boolean" use="optional"/>
    <xs:attribute name="data-edited-version" type="xs:string" use="optional"/>
    <xs:attribute name="data-swap-label-and-para" type="xs:boolean" use="optional"/>
    <xs:attribute name="add-data-inserted" type="xs:boolean" use="optional"/>
    <xs:attribute name="data-class" type="xs:string" use="optional"/>
  </xs:attributeGroup>
  <xs:simpleType name="EmailType">
    <xs:restriction base="xs:string">
      <xs:pattern value="[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+(,\s*[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+)*"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="orcidURL">
    <xs:restriction base="xs:string">
      <xs:pattern value="https?://orcid\.org/\d{4}-\d{4}-\d{4}-\d{3}[0-9X]"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="DateType">
    <xs:restriction base="xs:string">
      <xs:pattern value="0?[1-9]|[12][0-9]|3[01]"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="MonthType">
    <xs:restriction base="xs:string">
      <xs:pattern value="0?[1-9]|1[0-2]"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:simpleType name="YearType">
    <xs:restriction base="xs:string">
      <xs:pattern value="\d{4}"/>
    </xs:restriction>
  </xs:simpleType>
  <xs:element name="article">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="front" minOccurs="1" maxOccurs="1">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="journal-meta" minOccurs="1" maxOccurs="1">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="journal-id" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                            <xs:attribute name="journal-id-type" use="required">
                              <xs:simpleType>
                                <xs:restriction base="xs:string">
                                  <xs:enumeration value="nlm-ta"/>
                                  <xs:enumeration value="hwp"/>
                                  <xs:enumeration value="pmc"/>
                                  <xs:enumeration value="publisher-id"/>
                                </xs:restriction>
                              </xs:simpleType>
                            </xs:attribute>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="journal-title-group" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="journal-title" minOccurs="1" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="abbrev-journal-title" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="issn" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                            <xs:attribute name="pub-type" use="required">
                              <xs:simpleType>
                                <xs:restriction base="xs:string">
                                  <xs:enumeration value="epub"/>
                                  <xs:enumeration value="ppub"/>
                                </xs:restriction>
                              </xs:simpleType>
                            </xs:attribute>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="publisher" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="publisher-name" minOccurs="1" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="publisher-loc" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                  <xs:attribute name="id" type="xs:string" use="required"/>
                </xs:complexType>
              </xs:element>
              <xs:element name="article-meta" minOccurs="1" maxOccurs="1">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="article-id" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                            <xs:attribute name="pub-id-type" use="required">
                              <xs:simpleType>
                                <xs:restriction base="xs:string">
                                  <xs:enumeration value="publisher-id"/>
                                  <xs:enumeration value="doi"/>
                                </xs:restriction>
                              </xs:simpleType>
                            </xs:attribute>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="article-categories" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="subj-group" minOccurs="1" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="subject" minOccurs="1" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="subj-group-type" use="required">
                                <xs:simpleType>
                                  <xs:restriction base="xs:string">
                                    <xs:enumeration value="display-channel"/>
                                    <xs:enumeration value="heading"/>
                                    <xs:enumeration value="toc-heading"/>
                                    <xs:enumeration value="biological-terms"/>
                                  </xs:restriction>
                                </xs:simpleType>
                              </xs:attribute>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="title-group" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="article-title" minOccurs="1" maxOccurs="1">
                            <xs:complexType mixed="true">
                              <xs:sequence>
                                <xs:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="contrib-group" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="contrib" minOccurs="1" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="name" minOccurs="1" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="surname" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="given-names" minOccurs="1" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="prefix" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="suffix" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="id" type="xs:string" use="required"/>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="role" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="xref" minOccurs="0" maxOccurs="unbounded">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                        <xs:attribute name="ref-type" use="required">
                                          <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                              <xs:enumeration value="aff"/>
                                              <xs:enumeration value="other"/>
                                              <xs:enumeration value="fn"/>
                                              <xs:enumeration value="present-address"/>
                                              <xs:enumeration value="corresp"/>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                        <xs:attribute name="rid" use="required">
                                          <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                              <xs:pattern value="([a-zA-Z-]+\d+)(,\s*[a-zA-Z-]+\d+)*"/>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                        <xs:attribute name="data-con-type" type="xs:string" use="optional"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="email" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="EmailType">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="contrib-id" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="orcidURL">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                        <xs:attribute name="data-authenticated" type="xs:boolean" use="optional"/>
                                        <xs:attribute name="data-retain" type="xs:boolean" use="optional"/>
                                        <xs:attribute name="contrib-id-type" use="required">
                                          <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                              <xs:enumeration value="orcid"/>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attributeGroup ref="dataAttributes"/>
                              <xs:attribute name="contrib-type" use="required">
                                <xs:simpleType>
                                  <xs:restriction base="xs:string">
                                    <xs:enumeration value="author"/>
                                    <xs:enumeration value="senior_editor"/>
                                    <xs:enumeration value="editor"/>
                                    <xs:enumeration value="reviewer"/>
                                  </xs:restriction>
                                </xs:simpleType>
                              </xs:attribute>
                              <xs:attribute name="corresp" use="optional">
                                <xs:simpleType>
                                  <xs:restriction base="xs:string">
                                    <xs:enumeration value="yes"/>
                                  </xs:restriction>
                                </xs:simpleType>
                              </xs:attribute>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="aff" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType mixed="true">
                              <xs:sequence>
                                <xs:element name="label" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="named-content" minOccurs="1" maxOccurs="unbounded">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                        <xs:attribute name="content-type" use="required">
                                          <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                              <xs:enumeration value="institution"/>
                                              <xs:enumeration value="city"/>
                                              <xs:enumeration value="country"/>
                                            </xs:restriction>
                                          </xs:simpleType>
                                        </xs:attribute>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attributeGroup ref="dataAttributes"/>
                              <xs:attribute name="data-id" type="xs:string" use="required"/>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                        <xs:attributeGroup ref="dataAttributes"/>
                        <xs:attribute name="content-type" use="required">
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:enumeration value="author"/>
                              <xs:enumeration value="section"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:attribute>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="author-notes" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="corresp" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType mixed="true">
                              <xs:sequence>
                                <xs:element name="name" minOccurs="1" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="surname" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="given-names" minOccurs="1" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="prefix" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="suffix" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="xs:string">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="id" type="xs:string" use="required"/>
                                    <xs:attributeGroup ref="dataAttributes"/>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="email" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="EmailType">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attributeGroup ref="dataAttributes"/>
                              <xs:attribute name="data-id" type="xs:string" use="required"/>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="fn" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="label" minOccurs="0" maxOccurs="unbounded">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="p" minOccurs="0" maxOccurs="unbounded">
                                  <xs:complexType mixed="true">
                                    <xs:sequence>
                                      <xs:any processContents="lax" minOccurs="0" maxOccurs="unbounded"/>
                                    </xs:sequence>
                                    <xs:attribute name="id" type="xs:string" use="required"/>
                                    <xs:attributeGroup ref="dataAttributes"/>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attributeGroup ref="dataAttributes"/>
                              <xs:attribute name="data-id" type="xs:string" use="required"/>
                              <xs:attribute name="fn-type" type="xs:string" use="required"/>
                              <xs:attribute name="data-label" type="xs:string" use="required"/>
                              <xs:attribute name="data-con-type" type="xs:string" use="optional"/>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                        <xs:attributeGroup ref="dataAttributes"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="pub-date" minOccurs="1" maxOccurs="unbounded">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="day" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="DateType">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="month" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="MonthType">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="year" minOccurs="1" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="YearType">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                        <xs:attribute name="pub-type" use="required">
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:enumeration value="epub"/>
                              <xs:enumeration value="pub"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:attribute>
                        <xs:attribute name="publication-format" use="required">
                          <xs:simpleType>
                            <xs:restriction base="xs:string">
                              <xs:enumeration value="electronic"/>
                              <xs:enumeration value="print"/>
                            </xs:restriction>
                          </xs:simpleType>
                        </xs:attribute>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="volume" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="issue" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="fpage" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="lpage" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="elocation-id" minOccurs="0" maxOccurs="1">
                      <xs:complexType>
                        <xs:simpleContent>
                          <xs:extension base="xs:string">
                            <xs:attribute name="id" type="xs:string" use="required"/>
                          </xs:extension>
                        </xs:simpleContent>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="history" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="date" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="day" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="DateType">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="month" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="MonthType">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="year" minOccurs="1" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="YearType">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attribute name="date-type" use="required">
                                <xs:simpleType>
                                  <xs:restriction base="xs:string">
                                    <xs:enumeration value="received"/>
                                    <xs:enumeration value="accepted"/>
                                    <xs:enumeration value="rev-recd"/>
                                    <xs:enumeration value="PoA-v1"/>
                                    <xs:enumeration value="VoR-v1"/>
                                  </xs:restriction>
                                </xs:simpleType>
                              </xs:attribute>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="related-object" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="date" minOccurs="0" maxOccurs="unbounded">
                                  <xs:complexType>
                                    <xs:sequence>
                                      <xs:element name="day" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="DateType">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="month" minOccurs="0" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="MonthType">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                      <xs:element name="year" minOccurs="1" maxOccurs="1">
                                        <xs:complexType>
                                          <xs:simpleContent>
                                            <xs:extension base="YearType">
                                              <xs:attribute name="id" type="xs:string" use="required"/>
                                            </xs:extension>
                                          </xs:simpleContent>
                                        </xs:complexType>
                                      </xs:element>
                                    </xs:sequence>
                                    <xs:attribute name="id" type="xs:string" use="required"/>
                                    <xs:attribute name="date-type" use="required">
                                      <xs:simpleType>
                                        <xs:restriction base="xs:string">
                                          <xs:enumeration value="received"/>
                                          <xs:enumeration value="accepted"/>
                                          <xs:enumeration value="rev-recd"/>
                                          <xs:enumeration value="PoA-v1"/>
                                          <xs:enumeration value="VoR-v1"/>
                                        </xs:restriction>
                                      </xs:simpleType>
                                    </xs:attribute>
                                  </xs:complexType>
                                </xs:element>
                                <xs:element name="comment" minOccurs="0" maxOccurs="1">
                                  <xs:complexType>
                                    <xs:simpleContent>
                                      <xs:extension base="xs:string">
                                        <xs:attribute name="id" type="xs:string" use="required"/>
                                      </xs:extension>
                                    </xs:simpleContent>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="ext-link-type" use="required">
                                <xs:simpleType>
                                  <xs:restriction base="xs:string">
                                    <xs:enumeration value="url"/>
                                  </xs:restriction>
                                </xs:simpleType>
                              </xs:attribute>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                    <xs:element name="permissions" minOccurs="1" maxOccurs="1">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element name="copyright-statement" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="copyright-year" minOccurs="1" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="YearType">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="copyright-holder" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute name="id" type="xs:string" use="required"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                          <xs:element name="license" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                              <xs:sequence>
                                <xs:element name="license-p" minOccurs="0" maxOccurs="1">
                                  <xs:complexType mixed="true">
                                    <xs:attribute name="id" type="xs:string" use="required"/>
                                  </xs:complexType>
                                </xs:element>
                              </xs:sequence>
                              <xs:attribute name="id" type="xs:string" use="required"/>
                              <xs:attribute name="license-type" type="xs:string" use="required"/>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                        <xs:attribute name="id" type="xs:string" use="required"/>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                  <xs:attribute name="id" type="xs:string" use="required"/>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
            <xs:attribute name="id" type="xs:string" use="required"/>
            <xs:attributeGroup ref="dataAttributes"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="body" minOccurs="1" maxOccurs="1">
          <xs:complexType>
            <xs:attribute name="id" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="back" minOccurs="1" maxOccurs="1">
          <xs:complexType>
            <xs:attribute name="id" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="sub-article" minOccurs="1" maxOccurs="unbounded">
          <xs:complexType>
            <xs:attribute name="id" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="workflow" minOccurs="1" maxOccurs="unbounded">
          <xs:complexType>
            <xs:attribute name="id" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
        <xs:element name="production-notes" minOccurs="1" maxOccurs="unbounded">
          <xs:complexType>
            <xs:attribute name="id" type="xs:string" use="required"/>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute name="article-type" type="xs:string" use="required"/>
      <xs:anyAttribute namespace="http://www.w3.org/XML/1998/namespace" processContents="lax"/>
    </xs:complexType>
  </xs:element>
</xs:schema>