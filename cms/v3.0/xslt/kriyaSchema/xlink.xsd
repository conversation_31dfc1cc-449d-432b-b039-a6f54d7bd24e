<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
	xmlns:mml="http://www.w3.org/1998/Math/MathML"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xlink="http://www.w3.org/1999/xlink"
	targetNamespace="http://www.w3.org/1999/xlink" elementFormDefault="qualified">
	<!-- XSD import (no namespace) suppressed-->
	<!-- XSD import of namespace http://www.w3.org/1998/Math/MathML suppressed-->
	<!-- XSD import of namespace http://www.w3.org/2001/XMLSchema-instance suppressed-->
	<!-- XSD import of namespace http://www.w3.org/XML/1998/namespace suppressed-->
	<xs:attribute name="type">
		<xs:simpleType>
			<xs:restriction base="xs:token">
				<xs:enumeration value="simple"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:attribute>
	<xs:attribute name="href"/>
	<xs:attribute name="role"/>
	<xs:attribute name="title"/>
	<xs:attribute name="show">
		<xs:simpleType>
			<xs:restriction base="xs:token">
				<xs:enumeration value="embed"/>
				<xs:enumeration value="new"/>
				<xs:enumeration value="none"/>
				<xs:enumeration value="other"/>
				<xs:enumeration value="replace"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:attribute>
	<xs:attribute name="actuate">
		<xs:simpleType>
			<xs:restriction base="xs:token">
				<xs:enumeration value="none"/>
				<xs:enumeration value="onLoad"/>
				<xs:enumeration value="onRequest"/>
				<xs:enumeration value="other"/>
			</xs:restriction>
		</xs:simpleType>
	</xs:attribute>
</xs:schema>
