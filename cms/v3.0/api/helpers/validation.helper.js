const Ajv = require('ajv');
const addFormats = require('ajv-formats');
const ajvErrors = require('ajv-errors');
const path = require('path');

const ajv = new Ajv({ allErrors: true, coerceTypes: true });
addFormats(ajv);
ajvErrors(ajv);

/**
 * Validates request query and body parameters asynchronously.
 * @param {Object} req - Express request object
 * @returns {Promise<void>}
 */
function validateSchema(req) {
    return new Promise((resolve, reject) => {
        try {
            const pageName = req.url.toLowerCase();
            const endPointName = pageName.replace(/^\/api\/([^\/\?]+)\/?.*$/i, '$1');
            const reqMethod = req.method.toLowerCase();
            const schemaPath = path.join(__dirname, '..', reqMethod, 'schemas', `${endPointName}.schema.js`);
            const schema = require(schemaPath);

            const errorDetails = {};

            if (schema.queryParams) {
                const validateQuery = ajv.compile(schema.queryParams);
                if (!validateQuery(req.query)) {
                    validateQuery.errors?.forEach(error => {
                        const path = error.instancePath ? error.instancePath.replace(/\//g, "") : "message";
                        errorDetails[path] = error.message;
                    });
                }
            }

            if (reqMethod === 'post' && schema.body) {
                const validateBody = ajv.compile(schema.body);
                if (!validateBody(req.body)) {
                    validateBody.errors?.forEach(error => {
                        const path = error.instancePath ? error.instancePath.replace(/\//g, "") : "message";
                        errorDetails[path] = error.message;
                    });
                }
            }

            if (Object.keys(errorDetails).length > 0) {
                return reject({ status: 400, message: 'Invalid request parameters', details: errorDetails });
            }

            resolve();
        } catch (error) {
            return reject({ status: 500, message: 'Schema validation error', details: error.message });
        }
    });
}

module.exports = validateSchema;
