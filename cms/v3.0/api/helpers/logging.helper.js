const fs = require('fs');
const path = require('path');

/**
 * Helper function to log API requests and responses
 */
class APILogger {
    constructor() {
        this.logDir = path.join(process.cwd(), 'api-logs');
        this.ensureLogDirectory();
    }

    /**
     * Ensure the log directory exists
     */
    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    /**
     * Generate timestamp for filename
     */
    generateTimestamp() {
        const now = new Date();
        return now.toISOString().replace(/[:.]/g, '-').replace('T', '_').split('.')[0];
    }

    /**
     * Log API request and response
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {string} method - HTTP method (GET/POST)
     * @param {string} routeName - Route name
     * @param {Object} responseData - Response data
     * @param {number} statusCode - HTTP status code
     */
    logAPICall(req, res, method, routeName, responseData, statusCode) {
        try {
            const timestamp = this.generateTimestamp();
            const filename = `${routeName}_${timestamp}.json`;
            const filepath = path.join(this.logDir, filename);

            // Prepare log data
            const logData = {
                status: statusCode.toString(),
                url: `/api/${routeName}/`,
                method: method.toLowerCase(),
                timestamp: new Date().toISOString(),
                requestHeaders: {
                    'user-agent': req.get('User-Agent'),
                    'content-type': req.get('Content-Type'),
                    'host': req.get('Host')
                }
            };

            // Add query parameters if they exist
            if (req.query && Object.keys(req.query).length > 0) {
                logData.queryParams = req.query;
            }

            // Add body parameters if they exist
            if (req.body && Object.keys(req.body).length > 0) {
                logData.params = req.body;
            }

            // Add response data
            if (responseData) {
                logData.expectedOutput = responseData;
            }

            // Add session info if available
            if (req.session && req.session.site) {
                logData.sessionInfo = {
                    version: req.session.site.version,
                    customer: req.session.site.customer
                };
            }

            // Write log file
            fs.writeFileSync(filepath, JSON.stringify(logData, null, 4));

            // console.log(`API call logged: ${filename}`);
        } catch (error) {
            console.error('Error logging API call:', error);
        }
    }

    /**
     * Middleware to capture response data
     * @param {Object} req - Express request object
     * @param {Object} res - Express response object
     * @param {string} method - HTTP method
     * @param {string} routeName - Route name
     */
    createLoggingMiddleware(method, routeName) {
        return (req, res, next) => {
            // Store original methods
            const originalSend = res.send;
            const originalJson = res.json;
            const originalEnd = res.end;

            let responseData = null;
            let statusCode = 200;

            // Override res.send
            res.send = function(data) {
                responseData = data;
                statusCode = res.statusCode || 200;
                return originalSend.call(this, data);
            };

            // Override res.json
            res.json = function(data) {
                responseData = data;
                statusCode = res.statusCode || 200;
                return originalJson.call(this, data);
            };

            // Override res.end
            res.end = function(data) {
                if (data && !responseData) {
                    responseData = data;
                }
                statusCode = res.statusCode || 200;

                // Log the API call
                const logger = new APILogger();
                logger.logAPICall(req, res, method, routeName, responseData, statusCode);

                return originalEnd.call(this, data);
            };

            next();
        };
    }
}

module.exports = APILogger;
