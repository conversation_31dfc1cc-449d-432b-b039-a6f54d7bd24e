const bodyParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        cmsID: {
            type: 'string',
            description: 'CMS ID for the article',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        fileList: {
            type: 'string',
            description: 'XML string containing file list information'
        }
    },
    required: ['customer', 'project', 'cmsID', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully processed K1 article import",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "200"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Article loaded"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - configuration missing or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "Get xml path missing",
                                        "Did not get a response",
                                        "CMSID not found"
                                    ]
                                }
                            }
                        },
                        "step": {
                            "type": "string",
                            "example": "add job"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during processing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 500
                                },
                                "message": {
                                    "type": "string",
                                    "description": "Error details from AWS upload or file processing"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Article Import',
    description: "This endpoint handles the import and processing of K1 articles from external content management systems. It performs a comprehensive workflow that includes XML retrieval, metadata integration, resource management, and workflow initialization.\n\n**Key Features:**\n\n- **XML Retrieval**: Downloads article XML from configured K1 XML endpoints\n- **Metadata Integration**: Merges journal metadata with article content using customer templates\n- **Resource Management**: Downloads and uploads associated files (graphics, supplementary materials)\n- **Workflow Initialization**: Sets up initial workflow stages and triggers content structuring\n- **CMS Integration**: Handles CMS ID resolution and validation\n\n**Processing Workflow:**\n\n1. **XML Download**: Retrieves article XML using customer, project, and CMS ID parameters\n2. **Basic Info Update**: Integrates journal metadata from customer-specific templates\n3. **Resource Upload**: Downloads and uploads associated graphics and media files to AWS S3\n4. **File List Creation**: Generates and uploads file manifest XML\n5. **Article Loading**: Saves processed article to the database\n6. **Workflow Trigger**: Initiates structure content workflow stage\n\n**Resource Handling:**\n\n- Downloads graphics and inline supplementary materials\n- Uploads files to AWS S3 with proper bucket structure\n- Updates XML references to point to uploaded resources\n- Creates comprehensive file lists for tracking\n\n**Template Integration:**\n\n- Uses customer-specific default templates\n- Applies journal metadata and workflow configuration\n- Updates DOI and publisher ID fields\n- Generates unique IDs for all XML elements\n\n**Configuration Requirements:**\n\n- `cms.config.intResourceManager.k1XML`: K1 XML endpoint URL\n- `cms.config.intResourceManager.[customer]cmsID`: CMS ID resolution endpoint\n- AWS S3 configuration for resource storage\n- Customer-specific template files\n\n**Use Cases:**\n\n- Importing articles from legacy K1 systems\n- Migrating content between platforms\n- Automated article ingestion workflows\n- Content synchronization processes\n\nThe endpoint provides a complete solution for importing structured content with associated resources while maintaining proper workflow integration and metadata consistency.",
    summary: "Import and process K1 articles with metadata integration and resource management",
    bodyParams,
    response
};
