const queryParams = {
    type: 'object',
    properties: {
        filename: {
            type: 'string',
            description: 'Name of the file to be uploaded to S3',
            required: true,
            examples: [
                'document.pdf',
                'image.jpg',
                'data.xml',
                'presentation.pptx'
            ]
        }
    },
    required: ['filename'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully generated S3 upload credentials",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "upload_url": {
                            "type": "string",
                            "description": "S3 bucket URL for file upload",
                            "example": "https://bucket-name.s3.amazonaws.com"
                        },
                        "apiURL": {
                            "type": "string",
                            "description": "API URL for additional operations",
                            "example": "https://api.example.com"
                        },
                        "params": {
                            "type": "object",
                            "description": "Upload parameters required for S3 multipart form upload",
                            "properties": {
                                "key": {
                                    "type": "string",
                                    "description": "Unique S3 object key with UUID prefix",
                                    "example": "12345678-1234-1234-1234-123456789abc/document.pdf"
                                },
                                "acl": {
                                    "type": "string",
                                    "description": "Access control list setting for uploaded file",
                                    "example": "private"
                                },
                                "success_action_status": {
                                    "type": "string",
                                    "description": "HTTP status code to return on successful upload",
                                    "example": "201"
                                },
                                "policy": {
                                    "type": "string",
                                    "description": "Base64-encoded upload policy document",
                                    "example": "eyJleHBpcmF0aW9uIjoiMjAyNC0wMS0xNVQxMDozMDowMFoiLCJjb25kaXRpb25zIjpbXX0="
                                },
                                "x-amz-algorithm": {
                                    "type": "string",
                                    "description": "AWS signature algorithm",
                                    "example": "AWS4-HMAC-SHA256"
                                },
                                "x-amz-credential": {
                                    "type": "string",
                                    "description": "AWS credential scope for the upload",
                                    "example": "AKIAIOSFODNN7EXAMPLE/20240115/us-east-1/s3/aws4_request"
                                },
                                "x-amz-date": {
                                    "type": "string",
                                    "description": "Date stamp for the upload request",
                                    "example": "20240115T000000Z"
                                },
                                "x-amz-signature": {
                                    "type": "string",
                                    "description": "Calculated signature for the upload request",
                                    "example": "abcdef1234567890abcdef1234567890abcdef12"
                                }
                            },
                            "required": [
                                "key",
                                "acl",
                                "success_action_status",
                                "policy",
                                "x-amz-algorithm",
                                "x-amz-credential",
                                "x-amz-date",
                                "x-amz-signature"
                            ]
                        }
                    },
                    "required": ["upload_url", "apiURL", "params"]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - invalid or missing filename",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "A Valid filename Is Needed!",
                        "Invalid file extension!"
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during credential generation",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from AWS credential generation or configuration issues"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "This endpoint generates secure, time-limited credentials for uploading files directly to Amazon S3. It provides a secure way to enable client-side file uploads without exposing AWS credentials, using AWS Signature Version 4 for authentication.\n\n**Security Features:**\n\n- **Temporary Credentials**: Generates time-limited upload credentials (1 minute expiration)\n- **Filename Sanitization**: Automatically sanitizes filenames to prevent security issues\n- **UUID Prefixing**: Adds unique UUID prefix to prevent filename conflicts\n- **Private ACL**: Sets uploaded files to private access by default\n- **Signature V4**: Uses AWS Signature Version 4 for secure authentication\n\n**Upload Process:**\n\n1. **Credential Generation**: Creates secure upload parameters with policy and signature\n2. **Policy Creation**: Defines upload constraints including expiration and file restrictions\n3. **Signature Calculation**: Generates HMAC-SHA256 signature for request authentication\n4. **Response Delivery**: Returns upload URL and all required form parameters\n\n**File Handling:**\n\n- **Filename Sanitization**: Removes potentially dangerous characters from filenames\n- **UUID Generation**: Prepends unique identifier to prevent naming conflicts\n- **Extension Validation**: Supports comprehensive list of allowed file extensions\n- **Size Limitations**: Configurable file size restrictions through policy\n\n**Supported File Types:**\n\nThe endpoint supports a wide range of file extensions including:\n- **Documents**: pdf, doc, docx, txt, rtf, tex\n- **Images**: jpg, jpeg, png, gif, svg, tiff, bmp, webp\n- **Data**: xml, json, csv, xlsx, xls\n- **Media**: mp4, mp3, avi, mov, wmv\n- **Archives**: zip\n- **Presentations**: ppt, pptx\n- **And many more...**\n\n**AWS Configuration:**\n\nThe endpoint uses version-specific AWS configuration:\n- **Bucket**: Configured per CMS version for resource storage\n- **Region**: AWS region for S3 operations\n- **Access Keys**: Secure credential management\n- **API URL**: Additional service endpoints for post-upload operations\n\n**Upload Parameters:**\n\nThe response includes all parameters needed for multipart form upload:\n- **key**: Unique S3 object identifier with UUID prefix\n- **acl**: Access control (private by default)\n- **policy**: Base64-encoded upload policy with constraints\n- **x-amz-algorithm**: AWS4-HMAC-SHA256 signature algorithm\n- **x-amz-credential**: Scoped credential string\n- **x-amz-date**: ISO 8601 date stamp\n- **x-amz-signature**: Calculated request signature\n\n**Client Implementation:**\n\nClients can use the returned credentials to upload files directly to S3:\n1. Create multipart form with provided parameters\n2. Add file data to the form\n3. POST to the upload_url\n4. Handle success_action_status response\n\n**Security Considerations:**\n\n- **Time-Limited**: Credentials expire after 1 minute for security\n- **Single Use**: Each credential set is for one specific upload\n- **Scoped Access**: Limited to specific bucket and object key\n- **No Server Storage**: Files go directly to S3, reducing server load\n\n**Use Cases:**\n\n- **Direct Upload**: Client-side file uploads without server intermediary\n- **Large Files**: Efficient handling of large file uploads\n- **Scalability**: Reduces server bandwidth and processing requirements\n- **Security**: Maintains secure upload process without credential exposure\n- **Performance**: Faster uploads with direct S3 connection\n\n**Integration Benefits:**\n\n- **Reduced Server Load**: Files upload directly to S3\n- **Improved Performance**: No server-side file handling bottleneck\n- **Enhanced Security**: No long-lived credentials in client code\n- **Scalability**: Handles high-volume upload scenarios\n- **Cost Efficiency**: Reduces bandwidth costs for the application server\n\nThis endpoint provides a secure, scalable solution for file uploads while maintaining strict security controls and supporting a wide range of file types commonly used in content management systems.",
    summary: "Generate secure S3 upload credentials for direct client-side file uploads",
    queryParams,
    response
};
