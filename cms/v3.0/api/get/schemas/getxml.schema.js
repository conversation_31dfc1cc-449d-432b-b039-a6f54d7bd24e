const queryParams = {
    type: 'object',
    properties: {
        doi: {
            type: 'string',
            description: 'Document Object Identifier for the article',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        xmltype: {
            type: 'string',
            description: 'Type of XML to retrieve',
            enum: ['raw', 'customer'],
            default: 'customer'
        },
        cleanedupxml: {
            type: 'boolean',
            description: 'Flag to request cleaned up XML version'
        },
        proofType: {
            type: 'string',
            description: 'Type of proof processing to apply',
            examples: ['equation']
        }
    },
    required: ['doi', 'project', 'customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved XML content",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML content of the article",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><article><front><article-meta><title-group><article-title>Sample Article</article-title></title-group></article-meta></front><body><p>Article content...</p></body></article>"
                }
            }
        },
        "headers": {
            "Content-disposition": {
                "schema": {
                    "type": "string",
                    "example": "attachment; filename=10.1000/sample.xml"
                }
            },
            "Content-Type": {
                "schema": {
                    "type": "string",
                    "example": "application/xml; charset=utf-8"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - failed to retrieve or process XML",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error message or error object as string"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during XML processing",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error details from processing or transformation"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Content Export',
    description: "This endpoint exports article content as XML files, supporting both raw XML retrieval from the database and processed customer-specific XML generation. It provides comprehensive XML export functionality with optional HTML-to-XML transformation for specialized content types.",
    summary: "Export article content as XML with support for raw and customer-specific formats",
    queryParams,
    response
};
