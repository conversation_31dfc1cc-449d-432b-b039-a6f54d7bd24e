const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier'
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier'
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved AWS Step Function execution status",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "description": "Step Function execution status with progress information",
                            "properties": {
                                "message": {
                                    "type": "string",
                                    "description": "Execution history showing state transitions",
                                    "example": "\nStateEntered: Biblioservice\nStateEntered: ProcessingReferences\nStateExited: ProcessingReferences"
                                },
                                "status": {
                                    "type": "string",
                                    "description": "Current execution status",
                                    "enum": ["in-progress", "SUCCEEDED", "FAILED", "TIMED_OUT"],
                                    "example": "in-progress"
                                },
                                "log": {
                                    "type": "string",
                                    "description": "Current processing step log information",
                                    "example": "Processing References using BiblioService"
                                },
                                "progress": {
                                    "type": "integer",
                                    "description": "Progress percentage for current step",
                                    "minimum": 0,
                                    "maximum": 100,
                                    "example": 8
                                }
                            },
                            "required": ["message", "status"]
                        },
                        {
                            "type": "string",
                            "description": "Simple status message when job hasn't started",
                            "example": "Job not yet started"
                        }
                    ]
                }
            }
        }
    },
    "204": {
        "description": "No content - job not found or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from AWS Step Functions or processing"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during Step Function status retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from AWS SDK or internal processing"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Monitoring',
    description: "This endpoint monitors AWS Step Function execution status for conversion workflows, providing real-time progress tracking and detailed execution history for bibliographic processing and reference conversion tasks.",
    summary: "Monitor AWS Step Function execution status for conversion workflows with real-time progress tracking",
    queryParams,
    response
};
