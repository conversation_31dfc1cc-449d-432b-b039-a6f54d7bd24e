const requestBody = {
    type: 'object',
    properties: {
        videoId: {
            type: 'string',
            description: 'Brightcove video ID to retrieve video sources'
        },
        referenceId: {
            type: 'string',
            description: 'Brightcove reference ID to retrieve video information'
        }
    },
    oneOf: [
        {
            required: ['videoId'],
            properties: {
                videoId: { type: 'string' }
            }
        },
        {
            required: ['referenceId'],
            properties: {
                referenceId: { type: 'string' }
            }
        }
    ],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved Brightcove video information",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "array",
                            "description": "Video sources array when using videoId",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "src": {
                                        "type": "string",
                                        "description": "Video source URL"
                                    },
                                    "type": {
                                        "type": "string",
                                        "description": "MIME type of the video source",
                                        "examples": ["video/mp4", "application/x-mpegURL"]
                                    },
                                    "container": {
                                        "type": "string",
                                        "description": "Video container format",
                                        "examples": ["MP4", "M2TS"]
                                    },
                                    "codec": {
                                        "type": "string",
                                        "description": "Video codec",
                                        "examples": ["H264"]
                                    },
                                    "height": {
                                        "type": "integer",
                                        "description": "Video height in pixels"
                                    },
                                    "width": {
                                        "type": "integer",
                                        "description": "Video width in pixels"
                                    },
                                    "avg_bitrate": {
                                        "type": "integer",
                                        "description": "Average bitrate in bps"
                                    },
                                    "size": {
                                        "type": "integer",
                                        "description": "File size in bytes"
                                    },
                                    "duration": {
                                        "type": "integer",
                                        "description": "Video duration in milliseconds"
                                    }
                                },
                                "additionalProperties": true
                            }
                        },
                        {
                            "type": "object",
                            "description": "Video information when using referenceId",
                            "properties": {
                                "id": {
                                    "type": "string",
                                    "description": "Brightcove video ID"
                                },
                                "name": {
                                    "type": "string",
                                    "description": "Video name"
                                },
                                "reference_id": {
                                    "type": "string",
                                    "description": "Video reference ID"
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Video description"
                                },
                                "duration": {
                                    "type": "integer",
                                    "description": "Video duration in milliseconds"
                                },
                                "created_at": {
                                    "type": "string",
                                    "format": "date-time",
                                    "description": "Video creation timestamp"
                                },
                                "updated_at": {
                                    "type": "string",
                                    "format": "date-time",
                                    "description": "Video last update timestamp"
                                },
                                "state": {
                                    "type": "string",
                                    "description": "Video processing state",
                                    "examples": ["ACTIVE", "INACTIVE", "PENDING"]
                                },
                                "tags": {
                                    "type": "array",
                                    "items": {
                                        "type": "string"
                                    },
                                    "description": "Video tags"
                                }
                            },
                            "additionalProperties": true
                        }
                    ]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or Brightcove API error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response from Brightcove API or authentication failure"
                }
            }
        }
    },
    "401": {
        "description": "Unauthorized - Brightcove authentication failed",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Authentication error message"
                        },
                        "error_description": {
                            "type": "string",
                            "description": "Detailed error description"
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Video not found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error_code": {
                            "type": "string",
                            "example": "RESOURCE_NOT_FOUND"
                        },
                        "message": {
                            "type": "string",
                            "example": "Video not found"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Media Management',
    description: "This endpoint integrates with Brightcove Video Cloud API to retrieve video information and sources. It handles OAuth authentication with Brightcove using client credentials, then fetches video data based on either a video ID or reference ID. When using videoId, it returns an array of video sources with different quality options. When using referenceId, it returns comprehensive video metadata including title, description, duration, and processing state. This endpoint is essential for embedding and managing video content from Brightcove in the CMS.",
    summary: "Get Brightcove video information and sources",
    requestBody,
    response
};
