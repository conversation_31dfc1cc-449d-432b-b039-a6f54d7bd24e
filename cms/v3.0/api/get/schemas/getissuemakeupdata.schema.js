const queryParams = {
    type: 'object',
    properties: {
        customerName: {
            type: 'string',
            description: 'Customer name identifier',
            required: true
        },
        projectName: {
            type: 'string',
            description: 'Project name identifier',
            required: true
        },
        includeFields: {
            type: 'string',
            description: 'Fields to include in the response',
            default: '*'
        },
        from: {
            type: 'integer',
            description: 'Starting offset for pagination',
            default: 0,
            minimum: 0
        },
        size: {
            type: 'integer',
            description: 'Number of records to return',
            default: 500,
            minimum: 1,
            maximum: 10000
        },
        stage: {
            type: 'string',
            description: 'Filter by specific stage name(s) - comma separated for multiple stages'
        },
        excludeStageArticles: {
            type: 'string',
            description: 'Exclude articles in specific stage(s) - comma separated for multiple stages'
        },
        workflowType: {
            type: 'string',
            enum: ['milestone'],
            description: 'Workflow type for binder process'
        },
        urlToPost: {
            type: 'string',
            description: 'Custom elastic endpoint configuration key'
        },
        user: {
            type: 'string',
            description: 'User identifier for milestone workflow'
        },
        reportCE: {
            type: 'string',
            description: 'Report CE flag for Assign to Exeter option'
        },
        userValue: {
            type: 'string',
            description: 'User value for reportCE option'
        },
        version: {
            type: 'string',
            description: 'Version filter for milestone workflow'
        },
        workflowStatus: {
            type: 'string',
            description: 'Workflow status filter for milestone workflow'
        }
    },
    required: ['customerName', 'projectName'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved issue makeup data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Elasticsearch response containing issue details and articles",
                    "properties": {
                        "took": {
                            "type": "integer",
                            "description": "Time taken for the search in milliseconds"
                        },
                        "timed_out": {
                            "type": "boolean",
                            "description": "Whether the search timed out"
                        },
                        "_shards": {
                            "type": "object",
                            "description": "Shard information for the search"
                        },
                        "hits": {
                            "type": "object",
                            "properties": {
                                "total": {
                                    "type": "object",
                                    "properties": {
                                        "value": {
                                            "type": "integer",
                                            "description": "Total number of matching documents"
                                        },
                                        "relation": {
                                            "type": "string",
                                            "description": "Relation type for total count"
                                        }
                                    }
                                },
                                "max_score": {
                                    "type": "number",
                                    "description": "Maximum relevance score"
                                },
                                "hits": {
                                    "type": "array",
                                    "description": "Array of issue documents",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "_index": {
                                                "type": "string",
                                                "description": "Elasticsearch index name"
                                            },
                                            "_type": {
                                                "type": "string",
                                                "description": "Document type"
                                            },
                                            "_id": {
                                                "type": "string",
                                                "description": "Document ID"
                                            },
                                            "_score": {
                                                "type": "number",
                                                "description": "Relevance score"
                                            },
                                            "_source": {
                                                "type": "object",
                                                "description": "Issue document source data",
                                                "properties": {
                                                    "customer": {
                                                        "type": "string",
                                                        "description": "Customer identifier"
                                                    },
                                                    "project": {
                                                        "type": "string",
                                                        "description": "Project identifier"
                                                    },
                                                    "issueNumber": {
                                                        "type": "string",
                                                        "description": "Issue number"
                                                    },
                                                    "volume": {
                                                        "type": "string",
                                                        "description": "Volume number"
                                                    },
                                                    "year": {
                                                        "type": "string",
                                                        "description": "Publication year"
                                                    },
                                                    "stageName": {
                                                        "type": "string",
                                                        "description": "Current stage name"
                                                    },
                                                    "articles": {
                                                        "type": "array",
                                                        "description": "Articles in the issue",
                                                        "items": {
                                                            "type": "object",
                                                            "properties": {
                                                                "doi": {
                                                                    "type": "string",
                                                                    "description": "Article DOI"
                                                                },
                                                                "title": {
                                                                    "type": "string",
                                                                    "description": "Article title"
                                                                },
                                                                "pageRange": {
                                                                    "type": "string",
                                                                    "description": "Page range for the article"
                                                                }
                                                            }
                                                        }
                                                    }
                                                },
                                                "additionalProperties": true
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or query error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "Customer is missing",
                        "Project is missing"
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error or Elasticsearch error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Did not get a response"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Issue Management',
    description: "This endpoint retrieves issue makeup data based on customer and project parameters. It provides comprehensive information about issues, their articles, and workflow status for issue compilation and management processes.\n\n**Key Features:**\n\n- **Issue Listing**: Retrieves issues for specific customer and project combinations\n- **Stage Filtering**: Supports filtering by stage inclusion or exclusion\n- **Pagination Support**: Configurable offset and size parameters for large datasets\n- **Field Selection**: Customizable field inclusion for optimized responses\n- **Milestone Workflow**: Special support for milestone-based binder processes\n\n**Workflow Types:**\n\n- **Standard Issues**: Default issue listing from the issues index\n- **Milestone Workflow**: Enhanced functionality for binder processes with additional filtering options\n\n**Filtering Options:**\n\n- **Stage Inclusion**: Filter to show only issues in specific stages\n- **Stage Exclusion**: Filter to exclude issues in specific stages\n- **User Assignment**: Filter by assigned user (milestone workflow)\n- **Workflow Status**: Filter by workflow completion status\n- **Version Control**: Filter by specific versions\n\n**Use Cases:**\n\n- Issue makeup and compilation workflows\n- Editorial dashboard displays\n- Production planning and scheduling\n- Workflow status monitoring\n- Binder and packaging processes\n- Quality assurance and review processes\n\nThe endpoint queries Elasticsearch to retrieve issue data and supports both standard issue workflows and specialized milestone-based binder processes. It provides flexible filtering and pagination options to handle large datasets efficiently.",
    summary: "Get issue makeup data with filtering and pagination for workflow management",
    queryParams,
    response
};
