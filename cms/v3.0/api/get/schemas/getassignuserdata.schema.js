const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string', 
            minLength: 1,
            description: 'Customer identifier or "customer" to get all customers for the user'
        },
        userRoles: { 
            type: 'string',
            description: 'Specific user roles to filter by, overrides default role-type from user session'
        },
        accessLevel: { 
            type: 'string',
            description: 'Specific access level to filter by, overrides default access-level from user session'
        }
    },
    required: ['customer'],
    additionalProperties: true
};

const headers = {
    type: 'object',
    properties: {
        kuser: {
            type: 'string',
            description: 'User object containing authentication and role information'
        }
    }
};

const response = {
    "200": {
        "description": "Successfully retrieved user assignment data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Full name of the user"
                            },
                            "email": {
                                "type": "string",
                                "format": "email",
                                "description": "Email address of the user"
                            },
                            "additionalDetails": {
                                "type": "object",
                                "properties": {
                                    "customer-name": {
                                        "type": "string",
                                        "description": "Customer name associated with the user"
                                    },
                                    "role-type": {
                                        "type": "string",
                                        "description": "Role type of the user (e.g., copyeditor, manager, admin)"
                                    },
                                    "access-level": {
                                        "type": "string",
                                        "description": "Access level of the user (e.g., manager, vendor, production, admin)"
                                    }
                                },
                                "description": "Additional role and access details for the user"
                            },
                            "roles": {
                                "type": "array",
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "customer-name": {
                                            "type": "string"
                                        },
                                        "role-type": {
                                            "type": "string"
                                        },
                                        "access-level": {
                                            "type": "string"
                                        }
                                    }
                                },
                                "description": "Array of roles for users (removed after processing additionalDetails)"
                            }
                        },
                        "required": ["name", "email", "additionalDetails"]
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required data or invalid user object",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "Unable to find userObject"
                }
            },
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "enum": [
                                "Missing customer",
                                "Missing role-type", 
                                "Missing access-level"
                            ]
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Not found - error from Elasticsearch or no users found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response from Elasticsearch service"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Users',
    description: "This endpoint retrieves user assignment data based on customer, role type, and access level. It queries Elasticsearch to find users that can be assigned to tasks or articles. The endpoint handles role-based access control, automatically expanding access levels for managers and admins, and includes the logged-in user in the response.",
    summary: "Get assignable users data",
    queryParams,
    headers,
    response
};
