const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier to retrieve condition configuration',
            required: true
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved condition configuration and master list",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "conditionConfig": {
                            "type": "object",
                            "description": "Customer-specific condition configuration containing rules and settings for conditional content display",
                            "additionalProperties": true,
                            "example": {
                                "conditions": [
                                    {
                                        "id": "condition1",
                                        "name": "Show for specific article type",
                                        "xpath": "//subj-group[@subj-group-type='display-channel']/subject[text()='research-article']",
                                        "action": "show",
                                        "target": "component1"
                                    }
                                ],
                                "rules": {
                                    "defaultAction": "hide",
                                    "evaluationOrder": "sequential"
                                }
                            }
                        },
                        "masterList": {
                            "type": "object",
                            "description": "Master list of available XPath conditions and their definitions",
                            "additionalProperties": true,
                            "example": {
                                "articleTypes": [
                                    {
                                        "name": "Research Article",
                                        "xpath": "//subj-group[@subj-group-type='display-channel']/subject[text()='research-article']"
                                    },
                                    {
                                        "name": "Review Article",
                                        "xpath": "//subj-group[@subj-group-type='display-channel']/subject[text()='review-article']"
                                    }
                                ],
                                "workflowStages": [
                                    {
                                        "name": "Author Review",
                                        "xpath": "//workflow/stage[name='author-review' and status='in-progress']"
                                    }
                                ],
                                "customMeta": [
                                    {
                                        "name": "License Type",
                                        "xpath": "//custom-meta[meta-name='license-type']/meta-value"
                                    }
                                ]
                            }
                        }
                    },
                    "required": ["conditionConfig", "masterList"],
                    "additionalProperties": false
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing customer parameter",
        "content": {
            "application/json": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "conditionConfig not found",
                        "conditions xpath masterList not found"
                    ]
                }
            }
        }
    },
    "404": {
        "description": "Configuration files not found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "conditionConfig not found",
                        "conditions xpath masterList not found"
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Configuration Management',
    description: "This endpoint retrieves condition configuration settings for a specific customer along with the master list of available XPath conditions. The condition configuration defines rules for conditional content display based on article metadata, workflow stages, and other XML-based criteria. It reads two JSON files:\n\n1. **Customer Condition Config**: Contains customer-specific conditional rules and settings that determine when certain UI components or content should be displayed or hidden based on XPath evaluations against the article XML.\n\n2. **Master Condition List**: Contains a comprehensive list of predefined XPath expressions and their descriptions that can be used to create conditional rules. This serves as a reference for available conditions.\n\nThis configuration is typically used by the UI to dynamically show/hide components, enable/disable features, or modify behavior based on article characteristics, user roles, workflow states, and other contextual factors.",
    summary: "Get condition configuration and master XPath list for customer",
    queryParams,
    response
};
