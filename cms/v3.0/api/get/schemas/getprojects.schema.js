const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier to filter projects. Use "all" for all customers or specific customer name',
            examples: ['customer1', 'all']
        },
        includeFields: {
            type: 'string',
            description: 'Comma-separated list of fields to include in response',
            default: 'projects.name,projects.fullName',
            examples: ['projects.name,projects.fullName', 'projects']
        },
        getProjectConfig: {
            type: 'string',
            description: 'Flag to include project configuration data in response'
        },
        getLogos: {
            type: 'string',
            description: 'Flag to include customer logo availability information'
        },
        modal: {
            type: 'string',
            description: 'Modal type for specific data filtering',
            enum: ['issueConfig']
        },
        journalName: {
            type: 'string',
            description: 'Specific journal name for targeted project data retrieval'
        },
        getSpecificProjectData: {
            type: 'string',
            description: 'Flag to retrieve data for a specific project identified by journalName'
        }
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved projects data (filtered subset of projects endpoint)",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "description": "Array containing project information with restricted fields (name and fullName only)",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "Project/journal short name identifier",
                                "example": "nature"
                            },
                            "fullName": {
                                "type": "string",
                                "description": "Full display name of the project/journal",
                                "example": "Nature Journal"
                            }
                        }
                    },
                    "example": [
                        {
                            "name": "nature",
                            "fullName": "Nature Journal"
                        },
                        {
                            "name": "science",
                            "fullName": "Science Magazine"
                        }
                    ]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - invalid parameters or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "Did not get a response",
                                        "Invalid customer parameter",
                                        "Access denied"
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during project retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from Elasticsearch or processing operations"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Project Management',
    description: "This endpoint is a restricted wrapper around the projects API that returns a filtered subset of project data. It provides access to basic project information (name and fullName only) while maintaining the same underlying functionality as the full projects endpoint.\n\n**Key Features:**\n\n- **Restricted Data**: Returns only essential project fields (name, fullName) for security and performance\n- **User-Based Access**: Respects user permissions and role-based access controls\n- **Customer Filtering**: Supports customer-specific project filtering\n- **Session Integration**: Works with both header-based and session-based authentication\n\n**Data Restriction:**\n\nUnlike the full projects endpoint, this wrapper:\n- Automatically sets `includeFields` to 'projects.name,projects.fullName'\n- Filters out sensitive project configuration data\n- Returns only the projects array from the source data\n- Provides a simplified response structure\n\n**Access Control:**\n\nThe endpoint respects user access controls:\n- **Header Authentication**: Uses `kuser` header for API access\n- **Session Authentication**: Falls back to session-based user data\n- **Role-Based Filtering**: Filters projects based on user roles and permissions\n- **Customer Scope**: Limits access to authorized customer projects\n\n**Use Cases:**\n\n- **UI Dropdowns**: Populating project selection lists in user interfaces\n- **Quick Reference**: Getting basic project information without sensitive data\n- **API Integration**: Lightweight project data for external integrations\n- **Mobile Apps**: Reduced payload for mobile application consumption\n- **Public APIs**: Safe project data exposure for public-facing services\n\n**Underlying Functionality:**\n\nThis endpoint leverages the full projects API but:\n- Restricts returned fields for security\n- Maintains all filtering and access control logic\n- Supports the same query parameters\n- Provides the same authentication mechanisms\n- Ensures consistent data formatting\n\n**Performance Benefits:**\n\n- **Reduced Payload**: Smaller response size improves network performance\n- **Faster Processing**: Less data to serialize and transfer\n- **Lower Bandwidth**: Ideal for mobile and low-bandwidth environments\n- **Simplified Parsing**: Easier client-side data handling\n\n**Security Considerations:**\n\n- **Data Minimization**: Only exposes necessary project information\n- **Access Control**: Maintains full user permission checking\n- **Audit Trail**: Preserves logging and monitoring capabilities\n- **Consistent Authorization**: Uses same security model as full projects API\n\nThis endpoint is ideal for scenarios where you need basic project information without the overhead and security concerns of the full project data structure.",
    summary: "Get restricted project data with only name and fullName fields",
    queryParams,
    response
};
