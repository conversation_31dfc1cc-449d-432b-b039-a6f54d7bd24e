const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        email: {
            type: 'string',
            description: 'Author email address for submission lookup'
        },
        orcid: {
            type: 'string',
            description: 'Author ORCID identifier for submission lookup'
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for specific submission'
        },
        fileName: {
            type: 'string',
            description: 'Submission file name for direct file retrieval'
        },
        key: {
            type: 'string',
            description: 'Deeplink key for processing XML to JSON conversion'
        },
        status: {
            type: 'string',
            description: 'Submission status filter',
            default: 'inprogress',
            examples: ['inprogress', 'submitted', '*']
        },
        stubDoi: {
            type: 'string',
            description: 'Stub DOI for stub submission flow'
        },
        verifyDetails: {
            type: 'boolean',
            description: 'Flag to verify submission details without file retrieval'
        }
    },
    required: ['customer', 'project'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved submission data",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "description": "Complete submission data with state information",
                            "properties": {
                                "status": {
                                    "type": "integer",
                                    "example": 200
                                },
                                "state": {
                                    "type": "object",
                                    "description": "Submission state data containing form fields and metadata",
                                    "properties": {
                                        "submissionfileName": {
                                            "type": "string",
                                            "description": "Generated submission file name"
                                        }
                                    },
                                    "additionalProperties": true
                                }
                            },
                            "required": ["status", "state"]
                        },
                        {
                            "type": "object",
                            "description": "Direct submission configuration data",
                            "properties": {
                                "submissionfileName": {
                                    "type": "string",
                                    "description": "Submission file identifier"
                                }
                            },
                            "additionalProperties": true
                        }
                    ]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - processing error or invalid parameters",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 400
                        },
                        "err": {
                            "type": "string",
                            "description": "Error details from processing"
                        },
                        "message": {
                            "type": "string",
                            "example": "failed to process deeplink"
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Not found - submission data not available",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 404
                        },
                        "message": {
                            "type": "string",
                            "example": "Data Not found"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error - missing required parameters or processing failure",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 500
                                },
                                "message": {
                                    "type": "string",
                                    "example": "One or more of required parameters (customer id, project id, email or orcid) is/are not provided"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Submission Management',
    description: "This endpoint manages manuscript submission data retrieval and processing, supporting multiple submission workflows including normal submissions, revisions, resubmissions, and stub submissions. It integrates with Elasticsearch for data storage and AWS S3 for file management.",
    summary: "Retrieve and process manuscript submission data with support for multiple submission workflows",
    queryParams,
    response
};
