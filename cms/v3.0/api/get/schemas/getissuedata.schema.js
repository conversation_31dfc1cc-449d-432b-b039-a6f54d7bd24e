const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        returnType: {
            type: 'string',
            enum: ['xml', 'json'],
            description: 'Response format type',
            default: 'json'
        },
        // Alternative parameter names for backward compatibility
        articleID: {
            type: 'string',
            description: 'Alternative parameter name for DOI'
        },
        client: {
            type: 'string',
            description: 'Alternative parameter name for customer'
        },
        journalID: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        journalName: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        projectID: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        projectName: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        journal: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        jrnlName: {
            type: 'string',
            description: 'Alternative parameter name for project'
        },
        customerName: {
            type: 'string',
            description: 'Alternative parameter name for customer'
        },
        clientID: {
            type: 'string',
            description: 'Alternative parameter name for customer'
        }
    },
    required: ['customer', 'doi', 'project'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved issue data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "JSON response when returnType is not specified or set to 'json'",
                    "properties": {
                        "list": {
                            "type": "object",
                            "description": "Converted XML data as JSON object containing issue information, article metadata, and configuration settings for proofing and package creation",
                            "additionalProperties": true,
                            "example": {
                                "issue": {
                                    "metadata": {
                                        "issueNumber": "12",
                                        "volume": "45",
                                        "year": "2024",
                                        "title": "Special Issue on AI"
                                    },
                                    "articles": [
                                        {
                                            "doi": "10.1234/example.doi",
                                            "title": "Article Title",
                                            "authors": ["Author 1", "Author 2"],
                                            "pages": "1-15"
                                        }
                                    ],
                                    "configuration": {
                                        "proofingSettings": {},
                                        "packageSettings": {}
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML response when returnType is set to 'xml'. Contains issue data in XML format for proofing and package creation, including article metadata, issue configuration, and formatting instructions.",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<issue>\n  <metadata>\n    <issueNumber>12</issueNumber>\n    <volume>45</volume>\n    <year>2024</year>\n    <title>Special Issue on AI</title>\n  </metadata>\n  <articles>\n    <article>\n      <doi>10.1234/example.doi</doi>\n      <title>Article Title</title>\n      <authors>\n        <author>Author 1</author>\n        <author>Author 2</author>\n      </authors>\n      <pages>1-15</pages>\n    </article>\n  </articles>\n  <configuration>\n    <proofingSettings/>\n    <packageSettings/>\n  </configuration>\n</issue>"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response when required parameters are missing or processing fails"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during issue data processing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the processing failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Issue Management',
    description: "This endpoint retrieves issue data in XML or JSON format for proofing and package creation workflows. It serves as a GET wrapper around the POST issuedata endpoint, providing flexible parameter handling and format conversion.\n\n**Key Features:**\n\n- **Flexible Parameter Handling**: Accepts multiple parameter name variations for backward compatibility\n- **Format Conversion**: Returns data in either XML or JSON format based on returnType parameter\n- **Issue Data Retrieval**: Provides comprehensive issue information including:\n  - Issue metadata (volume, number, year, title)\n  - Article listings and metadata\n  - Configuration settings for proofing\n  - Package creation parameters\n\n**Parameter Flexibility:**\n\nThe endpoint accepts various parameter names for the same values:\n- **Customer**: customer, client, customerName, clientID\n- **DOI**: doi, articleID\n- **Project**: project, journalID, journalName, projectID, projectName, journal, jrnlName\n\n**Response Formats:**\n\n- **JSON (default)**: XML data converted to JSON using xml-js library\n- **XML**: Raw XML data suitable for direct processing by XML-based systems\n\n**Use Cases:**\n\n- Generating issue packages for publication\n- Creating proofing materials for issue review\n- Extracting issue metadata for external systems\n- Supporting legacy integrations with flexible parameter naming\n- Providing issue data for typesetting and layout systems\n\nThe endpoint leverages the existing POST issuedata functionality while providing a more convenient GET interface with enhanced parameter flexibility and format options.",
    summary: "Get issue data for proofing and package creation with flexible format options",
    queryParams,
    response
};
