const queryParams = {
    type: 'object',
    properties: {
        url: {
            type: 'string',
            description: 'URL to fetch content from',
            format: 'uri',
            required: true
        },
        validate: {
            type: 'boolean',
            description: 'Flag to validate URL accessibility instead of returning content'
        }
    },
    required: ['url'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved URL content or validation result",
        "content": {
            "oneOf": [
                {
                    "text/xml": {
                        "schema": {
                            "type": "string",
                            "description": "XML content retrieved from the URL",
                            "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><workflow><stage name=\"review\"><actions><action name=\"approve\"/></actions></stage></workflow>"
                        }
                    }
                },
                {
                    "application/json": {
                        "schema": {
                            "type": "boolean",
                            "description": "URL validation result when validate=true",
                            "example": true
                        }
                    }
                },
                {
                    "text/plain": {
                        "schema": {
                            "type": "string",
                            "description": "Plain text content from the URL"
                        }
                    }
                }
            ]
        }
    },
    "400": {
        "description": "Bad request - URL error or inaccessible",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "boolean",
                            "description": "Validation failure result when validate=true",
                            "example": false
                        },
                        {
                            "type": "string",
                            "description": "Empty response for failed URL fetch"
                        }
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during URL processing",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "boolean",
                            "description": "Validation failure result when validate=true",
                            "example": false
                        },
                        {
                            "type": "string",
                            "description": "Empty response for processing errors"
                        }
                    ]
                }
            }
        }
    }
};

module.exports = {
    tags: 'Utility',
    description: "This endpoint fetches content from external URLs or validates URL accessibility, primarily used for retrieving workflow configurations and external resources. It supports both content retrieval and URL validation modes.",
    summary: "Fetch content from external URLs or validate URL accessibility",
    queryParams,
    response
};
