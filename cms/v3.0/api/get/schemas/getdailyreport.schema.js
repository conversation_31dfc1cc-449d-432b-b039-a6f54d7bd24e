const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        stageName: {
            type: 'string',
            description: 'Stage name for the report',
            default: 'Pre-editing',
            examples: ['Pre-editing', 'Support', 'Copy-editing', 'Production']
        },
        year: {
            type: 'string',
            description: 'Year for the report (YYYY format)',
            pattern: '^\\d{4}$',
            example: '2024'
        },
        month: {
            type: 'string',
            description: 'Month for the report (MM format)',
            pattern: '^(0[1-9]|1[0-2])$',
            example: '03'
        },
        date: {
            type: 'string',
            description: 'Date for the report (DD format)',
            pattern: '^(0[1-9]|[12][0-9]|3[01])$',
            example: '15'
        },
        endDate: {
            type: 'string',
            description: 'End date for date range reports (YYYY-MM-DD format)',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$',
            example: '2024-03-20'
        },
        index: {
            type: 'string',
            description: 'Elasticsearch index to search'
        },
        from: {
            type: 'string',
            description: 'Starting offset for pagination',
            default: '0'
        },
        size: {
            type: 'string',
            description: 'Number of records to return',
            default: '500'
        },
        output: {
            type: 'string',
            enum: ['raw'],
            description: 'Output format - raw returns JSON instead of HTML table'
        },
        tickets: {
            type: 'string',
            description: 'Include ticket information for Support stage reports'
        },
        detailed: {
            type: 'string',
            description: 'Include detailed breakdown for Pre-editing stage'
        },
        tableChanged: {
            type: 'string',
            description: 'Include table change information'
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully generated daily report",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML table containing daily report data with columns varying based on stage and options. Default format includes DOI, Article Type, Stage, Total Time, Number of Proofs, Support counts, Signed Off By, and Page Count. Support stage with tickets includes additional columns for issue tracking."
                }
            },
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Raw JSON data when output=raw parameter is used",
                    "properties": {
                        "hits": {
                            "type": "array",
                            "description": "Array of article records matching the report criteria",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "_source": {
                                        "type": "object",
                                        "properties": {
                                            "id": {
                                                "type": "string",
                                                "description": "Article DOI"
                                            },
                                            "articleType": {
                                                "type": "string",
                                                "description": "Type of article"
                                            },
                                            "page-count-round": {
                                                "type": "integer",
                                                "description": "Rounded page count"
                                            },
                                            "stage": {
                                                "type": "array",
                                                "description": "Array of workflow stages",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "name": {
                                                            "type": "string",
                                                            "description": "Stage name"
                                                        },
                                                        "end-date": {
                                                            "type": "string",
                                                            "format": "date",
                                                            "description": "Stage completion date"
                                                        },
                                                        "assigned": {
                                                            "type": "object",
                                                            "properties": {
                                                                "to": {
                                                                    "type": "string",
                                                                    "description": "Person assigned to stage"
                                                                }
                                                            }
                                                        },
                                                        "job-logs": {
                                                            "type": "object",
                                                            "description": "Time tracking logs for the stage"
                                                        },
                                                        "object": {
                                                            "type": "object",
                                                            "description": "Stage objects including proofs"
                                                        },
                                                        "comments": {
                                                            "type": "string",
                                                            "description": "Stage comments"
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing customer parameter",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": ""
                }
            }
        }
    },
    "500": {
        "description": "Internal server error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Reporting',
    description: "This endpoint generates daily reports for article processing stages, providing detailed analytics on workflow performance, time tracking, and stage completion metrics. The report can be customized based on stage type and includes various formatting options.\n\n**Key Features:**\n\n- **Stage-specific Reports**: Different column layouts for different stages (Pre-editing, Support, etc.)\n- **Time Tracking**: Calculates total time spent in stages based on job logs\n- **Proof Counting**: Tracks number of proofs generated during processing\n- **Support Analytics**: Special handling for Support stage with issue categorization\n- **Flexible Date Ranges**: Single day or date range reporting\n- **Multiple Output Formats**: HTML table (default) or raw JSON data\n\n**Report Types:**\n\n- **Standard Report**: DOI, Article Type, Stage, Total Time, Proof counts, Support metrics\n- **Support Tickets**: Additional columns for issue tracking (Cause, Category, Fix Type, etc.)\n- **Detailed Pre-editing**: Breakdown of specific editing tasks (styling, references, etc.)\n- **Table Changes**: Information about table modifications during processing\n\nThe endpoint leverages the article list API to fetch data and processes it to generate comprehensive workflow analytics.",
    summary: "Generate daily reports for article processing stages with time tracking and analytics",
    queryParams,
    response
};
