const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved file list XML as downloadable attachment",
        "headers": {
            "Content-disposition": {
                "schema": {
                    "type": "string",
                    "example": "attachment; filename=10.1234/example.doi_file_list.xml"
                },
                "description": "Content disposition header for file download"
            },
            "Content-Type": {
                "schema": {
                    "type": "string",
                    "example": "application/xml; charset=utf-8"
                },
                "description": "Content type header indicating XML format"
            }
        },
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML document containing the complete file list for the article, including all associated resources, figures, tables, supplementary materials, and other files. The XML structure typically includes file paths, names, types, sizes, and metadata for each file associated with the article.",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<file-list>\n  <file>\n    <name>manuscript.xml</name>\n    <path>/resources/manuscript.xml</path>\n    <type>xml</type>\n    <size>45632</size>\n    <modified>2024-03-15T10:30:00Z</modified>\n  </file>\n  <file>\n    <name>figure1.jpg</name>\n    <path>/resources/figures/figure1.jpg</path>\n    <type>image</type>\n    <size>256789</size>\n    <modified>2024-03-15T09:15:00Z</modified>\n  </file>\n  <file>\n    <name>table1.docx</name>\n    <path>/resources/tables/table1.docx</path>\n    <type>document</type>\n    <size>12456</size>\n    <modified>2024-03-15T08:45:00Z</modified>\n  </file>\n</file-list>"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or file access error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response when file list cannot be retrieved"
                }
            }
        }
    },
    "404": {
        "description": "File list not found for the specified article",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "example": "File list not found"
                        },
                        "message": {
                            "type": "string",
                            "example": "No file list available for the specified article"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "This endpoint retrieves the file list XML document for a specific article and returns it as a downloadable attachment. The file list XML contains comprehensive information about all files associated with an article, including:\n\n**File Information:**\n- File names and paths\n- File types and formats\n- File sizes and modification dates\n- Resource categorization (figures, tables, supplements, etc.)\n\n**Key Features:**\n\n- **Downloadable Format**: Returns XML as an attachment with proper headers\n- **Complete File Inventory**: Lists all resources associated with the article\n- **Metadata Included**: Provides detailed information about each file\n- **Structured XML**: Well-formed XML document for easy parsing\n\n**Use Cases:**\n\n- Generating file manifests for article packages\n- Auditing article resources and assets\n- Creating backup and archival records\n- Integrating with external publishing systems\n- Quality assurance and completeness checking\n\nThe endpoint fetches the file list from the resources service using authenticated requests and returns it with appropriate headers for file download. This is essential for managing article assets and ensuring all required files are present for publication workflows.",
    summary: "Download file list XML for an article as attachment",
    queryParams,
    response
};
