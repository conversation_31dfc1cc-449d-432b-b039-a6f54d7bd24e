const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        urlToPost: {
            type: 'string',
            description: 'Elasticsearch endpoint to use for fetching articles',
            enum: ['getArticlesFiltered', 'getArticlesList']
        },
        from: {
            type: 'integer',
            description: 'Starting index for pagination',
            minimum: 0,
            default: 0
        },
        size: {
            type: 'integer',
            description: 'Number of articles to retrieve',
            minimum: 1,
            maximum: 1000,
            default: 100
        },
        stage: {
            type: 'string',
            description: 'Filter articles by workflow stage'
        },
        articleType: {
            type: 'string',
            description: 'Filter articles by article type'
        },
        priority: {
            type: 'string',
            description: 'Filter articles by priority (on/off)'
        },
        query: {
            type: 'string',
            description: 'Search query for filtering articles'
        },
        sortBy: {
            type: 'string',
            description: 'Field to sort articles by'
        },
        sortOrder: {
            type: 'string',
            enum: ['asc', 'desc'],
            description: 'Sort order (ascending or descending)'
        },
        skipReviewerData: {
            type: 'string',
            description: 'Skip reviewer data in response (automatically set to true)'
        }
    },
    required: ['customer'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Successfully retrieved article metadata list",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "string",
                                "description": "Article DOI identifier"
                            },
                            "customer": {
                                "type": "string",
                                "description": "Customer identifier"
                            },
                            "project": {
                                "type": "string",
                                "description": "Project identifier"
                            },
                            "title": {
                                "type": "string",
                                "description": "Article title"
                            },
                            "doi": {
                                "type": "string",
                                "description": "Document Object Identifier"
                            },
                            "articleType": {
                                "type": "string",
                                "description": "Type of article"
                            },
                            "stageName": {
                                "type": "string",
                                "description": "Current workflow stage name"
                            },
                            "workflowStatus": {
                                "type": "string",
                                "description": "Current workflow status"
                            },
                            "priority": {
                                "type": "string",
                                "description": "Article priority"
                            },
                            "authors": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "surname": {"type": "string"},
                                                "given-names": {"type": "string"},
                                                "email": {"type": "string"},
                                                "corresponding-author": {"type": "string"}
                                            }
                                        }
                                    }
                                }
                            },
                            "volume": {"type": "string"},
                            "issue": {"type": "string"},
                            "word-count": {"type": "string"},
                            "acceptedDate": {"type": "string"},
                            "receivedDate": {"type": "string"},
                            "lastUpdated": {"type": "string"},
                            "article-version": {"type": "string"},
                            "labels": {"type": "array"},
                            "system-labels": {"type": "array"},
                            "notes": {
                                "type": "object",
                                "properties": {
                                    "count": {"type": "integer"},
                                    "replied": {"type": "integer"}
                                }
                            }
                        },
                        "additionalProperties": true,
                        "description": "Article metadata object"
                    },
                    "description": "Array of article metadata objects without reviewer data"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - error in processing article list",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response from getarticlelist processing"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "This endpoint is a wrapper around the getarticlelist API that retrieves article metadata without reviewer data. It automatically sets skipReviewerData to true and returns a cleaned array of article metadata objects. The endpoint supports various filtering and pagination options through the underlying getarticlelist functionality, making it ideal for dashboard views and article listing pages where reviewer information is not needed.",
    summary: "Get article metadata list without reviewer data",
    queryParams,
    response
};
