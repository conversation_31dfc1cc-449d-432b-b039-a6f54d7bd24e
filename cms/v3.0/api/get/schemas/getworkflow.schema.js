const queryParams = {
    type: 'object',
    properties: {
        path: {
            type: 'string',
            description: 'File path to the workflow configuration file',
            required: true,
            examples: ['db/kriyadocs/customers/example/config/workflow.xml']
        }
    },
    required: ['path'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved workflow configuration",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML workflow configuration content",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><workflow><stages><stage name=\"review\"><actions><action name=\"approve\"/><action name=\"reject\"/></actions></stage></stages></workflow>"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - file not found or invalid path",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "File not found"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during file retrieval",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "File not found"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "This endpoint retrieves workflow configuration files from the file system, providing access to XML-based workflow definitions that control editorial and production processes within the CMS.",
    summary: "Retrieve workflow configuration files from the file system",
    queryParams,
    response
};
