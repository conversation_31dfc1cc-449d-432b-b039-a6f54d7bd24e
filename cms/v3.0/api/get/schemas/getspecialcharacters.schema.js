const queryParams = {
    type: 'object',
    properties: {},
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved special characters template",
        "content": {
            "text/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML template containing special character definitions and symbol page configuration",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><symbols><category name=\"Mathematical\"><symbol code=\"&#8721;\" name=\"Sum\" description=\"Summation symbol\"/><symbol code=\"&#8730;\" name=\"Square Root\" description=\"Square root symbol\"/></category><category name=\"Greek Letters\"><symbol code=\"&#945;\" name=\"Alpha\" description=\"Greek letter alpha\"/><symbol code=\"&#946;\" name=\"Beta\" description=\"Greek letter beta\"/></category></symbols>"
                }
            }
        }
    },
    "404": {
        "description": "Template file not found or configuration missing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 404
                        },
                        "content": {
                            "type": "string",
                            "example": "There is No configuration present"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during template file reading",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from file system operations"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Content Tools',
    description: "This endpoint retrieves the special characters template file that provides a comprehensive collection of symbols, mathematical characters, and special typography elements for content editing and document preparation.",
    summary: "Retrieve special characters and symbols template for content editing tools",
    queryParams,
    response
};
