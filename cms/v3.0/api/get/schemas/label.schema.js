const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier to filter labels',
            required: true
        },
        fetchID: {
            type: 'string',
            description: 'Specific label ID to fetch'
        },
        from: {
            type: 'integer',
            description: 'Starting index for pagination',
            minimum: 0,
            default: 0
        },
        size: {
            type: 'integer',
            description: 'Number of results to return',
            minimum: 1,
            maximum: 1000,
            default: 500
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const bodyParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier to filter labels',
            required: true
        },
        fetchID: {
            type: 'string',
            description: 'Specific label ID to fetch'
        },
        from: {
            type: 'integer',
            description: 'Starting index for pagination',
            minimum: 0,
            default: 0
        },
        size: {
            type: 'integer',
            description: 'Number of results to return',
            minimum: 1,
            maximum: 1000,
            default: 500
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved custom labels",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "description": "Array of custom label objects",
                    "items": {
                        "type": "object",
                        "properties": {
                            "labelName": {
                                "type": "string",
                                "description": "Name of the label (derived from filterLabel if not present)"
                            },
                            "filterLabel": {
                                "type": "string",
                                "description": "Filter label identifier"
                            },
                            "values": {
                                "type": "string",
                                "description": "Comma-separated values (converted from pipe-separated if needed)"
                            },
                            "source": {
                                "type": "string",
                                "description": "Source path (derived from metaPath if present)"
                            },
                            "metaPath": {
                                "type": "string",
                                "description": "Metadata path for the label"
                            },
                            "customer": {
                                "type": "string",
                                "description": "Customer identifier"
                            }
                        },
                        "additionalProperties": true
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing customer or Elasticsearch error",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "properties": {
                                "error": {
                                    "type": "string",
                                    "example": "Missing customer"
                                }
                            }
                        },
                        {
                            "type": "string",
                            "example": "400"
                        }
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during label retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error details from processing"
                }
            }
        }
    }
};

module.exports = {
    public: true,
    tags: 'Label Management',
    description: "This endpoint retrieves custom labels from the Elasticsearch custom labels index, providing access to customer-specific label configurations used for content categorization, filtering, and metadata management. It supports both specific label retrieval and bulk label queries with pagination.",
    summary: "Retrieve custom labels from Elasticsearch with customer filtering and pagination support",
    queryParams,
    bodyParams,
    response
};
