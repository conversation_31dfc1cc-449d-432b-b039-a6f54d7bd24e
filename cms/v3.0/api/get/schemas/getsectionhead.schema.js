const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier for section head configuration',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier',
            required: true
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved section head data or configuration error",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "description": "Section head data with journal and section information",
                            "properties": {
                                "journal_list": {
                                    "type": "array",
                                    "description": "List of journals with their associated sections",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "id": {
                                                "type": "string",
                                                "description": "Unique journal identifier",
                                                "example": "journal_001"
                                            },
                                            "name": {
                                                "type": "string",
                                                "description": "Journal title/name",
                                                "example": "Nature Medicine"
                                            },
                                            "section_list": {
                                                "type": "array",
                                                "description": "List of sections within this journal",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "id": {
                                                            "type": "string",
                                                            "description": "Unique section identifier",
                                                            "example": "section_001"
                                                        },
                                                        "name": {
                                                            "type": "string",
                                                            "description": "Section title/name",
                                                            "example": "Research Articles"
                                                        }
                                                    },
                                                    "required": ["id", "name"]
                                                }
                                            }
                                        },
                                        "required": ["id", "name", "section_list"]
                                    }
                                }
                            },
                            "required": ["journal_list"]
                        },
                        {
                            "type": "object",
                            "description": "Error response for missing parameters or configuration",
                            "properties": {
                                "status": {
                                    "type": "object",
                                    "properties": {
                                        "code": {
                                            "type": "integer",
                                            "example": 200
                                        },
                                        "message": {
                                            "type": "string",
                                            "examples": [
                                                "One or more of required parameters (customer id, project id, xpath) is/are not provided.",
                                                "Section head customer config missing."
                                            ]
                                        }
                                    },
                                    "required": ["code", "message"]
                                }
                            },
                            "required": ["status"]
                        }
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during section head data retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from external API calls or processing"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Content Organization',
    description: "This endpoint retrieves section head information for journals, providing a hierarchical structure of journals and their associated sections. It integrates with external APIs to fetch journal and section data based on customer-specific configurations.",
    summary: "Retrieve hierarchical journal and section head information from external APIs",
    queryParams,
    response
};
