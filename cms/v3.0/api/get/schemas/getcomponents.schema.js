const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        articleState: {
            type: 'string',
            description: 'Current state of the article',
            enum: ['open', 'read-only'],
            default: 'open'
        },
        articlesRole: {
            type: 'string',
            description: 'User role for the article',
            examples: ['author', 'editor', 'reviewer', 'copyeditor', 'proofreader']
        },
        accessType: {
            type: 'string',
            description: 'Type of access to the article',
            examples: ['dashboard', 'direct']
        },
        offlineState: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Whether the article is in offline mode',
            default: 'false'
        },
        skiplambda: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Skip lambda-based XSLT transformation',
            default: 'false'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully generated UI components for the review content editor",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML content containing customized UI components for the review editor. This includes toolbars, menus, templates, and interactive elements configured based on customer settings, user role, article state, and workflow stage. The HTML is dynamically generated by transforming customer configuration XML through XSLT processing."
                }
            }
        }
    },
    "204": {
        "description": "No content - error in processing or missing data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "redirect": {
                            "type": "string",
                            "example": "error_page"
                        },
                        "page": {
                            "type": "string",
                            "example": "500"
                        },
                        "msg": {
                            "type": "string",
                            "examples": [
                                "Current Stage not found",
                                "XML processing error"
                            ]
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Server error - missing required parameters or processing failure",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 500
                                },
                                "message": {
                                    "type": "string",
                                    "example": "One or more of required parameters (customer id, project id, current stage) is/are not provided. Unexpected input"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'UI Components',
    description: "This endpoint generates customized UI components for the review content editor interface. It retrieves the article XML, determines the current workflow stage, and transforms customer-specific configuration templates into HTML components. The generated components include toolbars, menus, templates, and interactive elements that are customized based on:\n\n- Customer and project configuration\n- User role and permissions\n- Article state (open/read-only)\n- Current workflow stage\n- Article type and metadata\n- License type and copyright settings\n\nThe endpoint handles complex logic for component visibility, user authentication, offline mode, and role-based access control. It uses XSLT transformation (either local or Lambda-based) to process configuration files and generate the final HTML output. The components support features like welcome messages, author lists, PDF links, authentication flows, and stage-specific editing capabilities.",
    summary: "Generate customized UI components for review content editor",
    queryParams,
    response
};
