const queryParams = {
    type: 'object',
    properties: {customer: { type: 'string', minLength: 1 }},
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved customer attributes/section configuration",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "enum": ["Success", "No Config found"],
                            "description": "Status message indicating the result of the operation"
                        },
                        "data": {
                            "type": "array",
                            "description": "Array of section configuration objects from Elasticsearch",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "_index": {
                                        "type": "string",
                                        "description": "Elasticsearch index name"
                                    },
                                    "_type": {
                                        "type": "string",
                                        "description": "Elasticsearch document type"
                                    },
                                    "_id": {
                                        "type": "string",
                                        "description": "Document ID"
                                    },
                                    "_score": {
                                        "type": "number",
                                        "description": "Elasticsearch relevance score"
                                    },
                                    "_source": {
                                        "type": "object",
                                        "description": "The actual section configuration data",
                                        "additionalProperties": true
                                    }
                                }
                            }
                        }
                    },
                    "required": ["message", "data"]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - authentication failed or error fetching configuration",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "example": "Error fetching sections config"
                        },
                        "error": {
                            "oneOf": [
                                {
                                    "type": "string",
                                    "description": "Error message"
                                },
                                {
                                    "type": "object",
                                    "description": "Error object with details"
                                }
                            ]
                        }
                    }
                }
            }
        }
    },
    "401": {
        "description": "Unauthorized - authentication failed",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Authentication error message"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during configuration retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "example": "Error fetching sections config"
                        },
                        "error": {
                            "type": "string",
                            "description": "Internal error details"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    public: false,
    tags: 'Customer Configuration',
    description: "This endpoint retrieves customer-specific attributes and section configuration data from the Elasticsearch projects index. It provides access to project section configurations that define how content is structured and processed for specific customers.",
    summary: "Retrieve customer-specific attributes and section configuration from Elasticsearch",
    queryParams,
    response
};
