const queryParams = {
    type: 'object',
    properties: {},
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Service health check successful",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Empty object indicating service is healthy",
                    "example": {}
                }
            }
        }
    },
    "500": {
        "description": "Service health check failed",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "string",
                            "description": "Error message when service monitoring is unavailable",
                            "example": "Unable to get service details"
                        },
                        {
                            "type": "object",
                            "description": "Error details from service monitoring system"
                        }
                    ]
                }
            }
        }
    }
};

module.exports = {
    tags: 'System Monitoring',
    description: "This endpoint provides a health check for the CMS service, returning the operational status of the system. It can optionally integrate with external service monitoring systems to provide comprehensive health information.",
    summary: "Service health check endpoint for monitoring system availability",
    queryParams,
    response
};
