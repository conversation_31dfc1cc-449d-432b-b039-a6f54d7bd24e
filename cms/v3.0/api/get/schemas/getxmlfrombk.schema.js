const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for the article',
            required: true
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved XML content from backend",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML content retrieved from the backend database",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><article><front><article-meta><title-group><article-title>Sample Article</article-title></title-group></article-meta></front><body><p>Article content...</p></body></article>"
                }
            }
        },
        "headers": {
            "Content-Type": {
                "schema": {
                    "type": "string",
                    "example": "application/xml; charset=utf-8"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing parameters or backend error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 400
                        },
                        "message": {
                            "type": "string",
                            "examples": ["Missing parameters", "Backend service error"]
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during XML retrieval",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error details from processing or backend communication"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Content Export',
    description: "This endpoint retrieves XML content directly from the backend database (ExistDB) using a formatted URL request. It provides access to raw article XML data stored in the backend system for export and processing purposes.",
    summary: "Retrieve raw XML content directly from backend database (ExistDB)",
    queryParams,
    response
};
