const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string', 
            minLength: 1,
            description: 'Customer identifier to filter article user data'
        },
        doi: { 
            type: 'string',
            description: 'Digital Object Identifier to filter by specific article'
        },
        email: { 
            type: 'string',
            format: 'email',
            description: 'Email address to filter by specific user'
        },
        version: { 
            type: 'string',
            description: 'Version identifier to filter by article version'
        },
        fetchID: { 
            type: 'string',
            description: 'Specific document ID to fetch from Elasticsearch'
        },
        notMatch: { 
            type: 'string',
            description: 'Query string for records to exclude from results'
        },
        from: { 
            type: 'integer',
            minimum: 0,
            description: 'Starting index for pagination (default: 0)'
        },
        size: { 
            type: 'integer',
            minimum: 1,
            maximum: 1000,
            description: 'Number of records to return (default: 1000)'
        },
        format: { 
            type: 'string',
            enum: ['xml'],
            description: 'Response format - if set to "xml", returns XML instead of JSON'
        }
    },
    required: ['customer'],
    additionalProperties: true
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string', 
            minLength: 1,
            description: 'Customer identifier to filter article user data'
        },
        doi: { 
            type: 'string',
            description: 'Digital Object Identifier to filter by specific article'
        },
        email: { 
            type: 'string',
            format: 'email',
            description: 'Email address to filter by specific user'
        },
        version: { 
            type: 'string',
            description: 'Version identifier to filter by article version'
        },
        fetchID: { 
            type: 'string',
            description: 'Specific document ID to fetch from Elasticsearch'
        },
        notMatch: { 
            type: 'string',
            description: 'Query string for records to exclude from results'
        },
        from: { 
            type: 'integer',
            minimum: 0,
            description: 'Starting index for pagination (default: 0)'
        },
        size: { 
            type: 'integer',
            minimum: 1,
            maximum: 1000,
            description: 'Number of records to return (default: 1000)'
        },
        format: { 
            type: 'string',
            enum: ['xml'],
            description: 'Response format - if set to "xml", returns XML instead of JSON'
        }
    },
    required: ['customer'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Successfully retrieved article user data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "customer": {
                                "type": "string",
                                "description": "Customer identifier"
                            },
                            "doi": {
                                "type": "string",
                                "description": "Digital Object Identifier of the article"
                            },
                            "email": {
                                "type": "string",
                                "format": "email",
                                "description": "User email address"
                            },
                            "version": {
                                "type": "string",
                                "description": "Article version"
                            },
                            "totalCount": {
                                "type": "integer",
                                "description": "Total number of records available (included in first item only)"
                            }
                        },
                        "additionalProperties": true,
                        "description": "Article user data record from Elasticsearch"
                    }
                }
            },
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML representation of the article user data when format=xml is specified"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing customer or Elasticsearch error",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "properties": {
                                "error": {
                                    "type": "string",
                                    "example": "Missing customer"
                                }
                            }
                        },
                        {
                            "type": "string",
                            "example": "400"
                        }
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the server error"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Articles',
    description: "This endpoint retrieves article user data from Elasticsearch based on various filter criteria. It can search by customer, DOI, email, version, or specific document ID. The endpoint supports pagination and can return results in either JSON or XML format. It's commonly used to get user-article associations and related metadata.",
    summary: "Get article user data",
    queryParams,
    requestBody,
    response
};
