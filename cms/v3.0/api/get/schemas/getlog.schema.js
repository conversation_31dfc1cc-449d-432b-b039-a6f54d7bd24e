const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier for article-specific logs'
        },
        project: {
            type: 'string',
            description: 'Project identifier for article-specific logs'
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for article-specific logs'
        },
        file: {
            type: 'string',
            description: 'Specific log file name (without .log extension)',
            default: 'log'
        },
        year: {
            type: 'string',
            description: 'Year for date-based log retrieval (YYYY format)',
            pattern: '^[0-9]{4}$'
        },
        month: {
            type: 'string',
            description: 'Month for date-based log retrieval (0-11, where 0=January)',
            enum: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']
        },
        date: {
            type: 'string',
            description: 'Day of month for date-based log retrieval (1-31)',
            pattern: '^[1-9]|[12][0-9]|3[01]$'
        }
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved log file content",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "Log file content wrapped in HTML <pre> tags for formatted display. Contains timestamped log entries with various levels (info, error, debug, etc.) and detailed information about system operations, errors, and user activities.",
                    "example": "<pre>[2024-01-15 10:30:25] INFO: Article processing started for customer: example, project: journal1, doi: 10.1234/example\n[2024-01-15 10:30:26] DEBUG: Validating article metadata\n[2024-01-15 10:30:27] ERROR: Failed to process image: image1.jpg - File not found\n[2024-01-15 10:30:28] INFO: Article processing completed successfully</pre>"
                }
            }
        }
    },
    "404": {
        "description": "Log file not found",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "Error : File not found"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during log retrieval",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error message indicating system failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'System Monitoring',
    description: "This endpoint retrieves log files from the system's logging infrastructure. It supports both article-specific logs and date-based system logs, providing comprehensive access to system activity, error tracking, and operational monitoring.\n\n**Log Types:**\n\n- **Article-Specific Logs**: Logs related to specific articles using customer, project, and DOI parameters\n- **Date-Based System Logs**: General system logs organized by year, month, and date\n- **Custom File Logs**: Specific log files by name within date-based structure\n\n**Log File Organization:**\n\n- **Article Logs**: `_logs/{customer}/{project}/{doi}.log`\n- **System Logs**: `_logs/{year}/{month}/{date}/{file}.log`\n- **Default Structure**: Current date if no date parameters provided\n\n**Month Mapping:**\n\n- 0: January (01_Jan)\n- 1: February (02_Feb)\n- 2: March (03_Mar)\n- 3: April (04_Apr)\n- 4: May (05_May)\n- 5: June (06_Jun)\n- 6: July (07_Jul)\n- 7: August (08_Aug)\n- 8: September (09_Sep)\n- 9: October (10_Oct)\n- 10: November (11_Nov)\n- 11: December (12_Dec)\n\n**Use Cases:**\n\n- **Debugging**: Investigating specific article processing issues\n- **System Monitoring**: Reviewing daily system activity and performance\n- **Error Analysis**: Tracking and analyzing error patterns\n- **Audit Trails**: Reviewing user activities and system changes\n- **Performance Analysis**: Monitoring processing times and resource usage\n\n**Log Content:**\n\nLogs typically contain:\n- Timestamped entries with log levels (INFO, ERROR, DEBUG, WARN)\n- User activity tracking\n- System operation details\n- Error messages and stack traces\n- Performance metrics\n- API request/response information\n\n**Access Patterns:**\n\n1. **Article-Specific**: Use customer, project, and DOI for targeted article logs\n2. **Date-Based**: Use year, month, date for system-wide logs\n3. **Current Date**: Omit date parameters to access today's logs\n4. **Custom Files**: Specify file parameter for non-default log files\n\nThe endpoint returns log content formatted in HTML `<pre>` tags for proper display in web browsers while maintaining the original formatting and readability of the log entries.",
    summary: "Retrieve system and article-specific log files for monitoring and debugging",
    queryParams,
    response
};
