const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer/client identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project/journal identifier',
            required: true
        },
        apiKey: {
            type: 'string',
            description: 'API key required when calling from outside the system'
        }
    },
    required: ['customer', 'project'],
    additionalProperties: false
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer/client identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project/journal identifier',
            required: true
        },
        apiKey: {
            type: 'string',
            description: 'API key required when calling from outside the system'
        }
    },
    required: ['customer', 'project'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved basic configuration",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Basic configuration JSON object containing customer and project specific settings",
                    "additionalProperties": true,
                    "example": {
                        "reviewSettings": {
                            "enableComments": true,
                            "allowFileUpload": false
                        },
                        "displaySettings": {
                            "theme": "default",
                            "showLineNumbers": true
                        },
                        "workflowSettings": {
                            "autoAssign": false,
                            "reminderDays": 7
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Configuration not found or missing parameters",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "examples": [404, 500]
                        },
                        "message": {
                            "type": "string",
                            "examples": [
                                "Missing project or client parameters"
                            ]
                        },
                        "content": {
                            "type": "string",
                            "examples": [
                                "There is No configuration present"
                            ]
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Configuration Management',
    description: "This endpoint retrieves basic configuration settings for a specific customer and project combination. It looks for configuration files in a hierarchical manner - first checking for project-specific configuration, then falling back to default customer configuration. The configuration contains various settings like review preferences, display options, and workflow parameters that customize the behavior of the system for different customers and projects.",
    summary: "Get basic configuration for customer and project",
    queryParams,
    requestBody,
    response
};
