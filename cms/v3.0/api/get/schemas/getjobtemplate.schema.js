const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        forceLoad: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Force reload existing job template'
        },
        specificPath: {
            type: 'string',
            description: 'Specific path for PVRH files'
        },
        updatemeta: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Update metadata during resubmission'
        },
        ftpFileName: {
            type: 'string',
            description: 'FTP file name for workflow'
        },
        uploadtype: {
            type: 'string',
            description: 'Upload type for workflow'
        },
        articleTitle: {
            type: 'string',
            description: 'Article title'
        },
        articleShortTitle: {
            type: 'string',
            description: 'Short title for the article'
        },
        articleType: {
            type: 'string',
            description: 'Type of article'
        },
        subject: {
            type: 'string',
            description: 'Subject category'
        },
        subjectObj: {
            type: 'string',
            description: 'JSON string of multiple subject categories'
        },
        authorName: {
            type: 'string',
            description: 'Author name (simple format)'
        },
        authorEmail: {
            type: 'string',
            description: 'Author email (simple format)'
        },
        authorObject: {
            type: 'string',
            description: 'JSON string containing detailed author information'
        },
        affiliationObj: {
            type: 'string',
            description: 'JSON string containing affiliation information'
        },
        affSeparator: {
            type: 'string',
            description: 'Separator for affiliation display',
            default: ', '
        },
        authorEqualContribution: {
            type: 'string',
            description: 'JSON string for equal contribution authors'
        },
        institutionalMember: {
            type: 'string',
            description: 'Institutional member information'
        },
        submittingAuthorInfo: {
            type: 'string',
            description: 'JSON string for submitting author information'
        },
        submittingAuthorAff: {
            type: 'string',
            description: 'JSON string for submitting author affiliation'
        },
        fundingObj: {
            type: 'string',
            description: 'JSON string containing funding information'
        },
        groupAuthorObj: {
            type: 'string',
            description: 'JSON string for group author information'
        },
        suggestedReviewersObj: {
            type: 'string',
            description: 'JSON string for suggested reviewers'
        },
        suggestedReviewerAffiliation: {
            type: 'string',
            description: 'JSON string for suggested reviewer affiliations'
        },
        opposedReviewersObj: {
            type: 'string',
            description: 'JSON string for opposed reviewers'
        },
        opposedReviewerAffiliation: {
            type: 'string',
            description: 'JSON string for opposed reviewer affiliations'
        },
        keywordObject: {
            type: 'string',
            description: 'JSON string containing keywords'
        },
        customMeta: {
            type: 'string',
            description: 'JSON string for custom metadata'
        },
        perspectives: {
            type: 'string',
            description: 'JSON string for perspective questions and answers'
        },
        questionMeta: {
            type: 'string',
            description: 'JSON string for question and answer metadata'
        },
        journalPreferences: {
            type: 'string',
            description: 'JSON string for journal preferences'
        },
        sectionPreferences: {
            type: 'string',
            description: 'JSON string for section preferences'
        },
        files: {
            type: 'string',
            description: 'JSON string for file information'
        },
        relatedArticle: {
            type: 'string',
            description: 'JSON string for related articles'
        },
        dataAvailabilityStatement: {
            type: 'string',
            description: 'JSON string for data availability statement'
        },
        fastTrack: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Enable fast track processing'
        },
        userEmail: {
            type: 'string',
            description: 'User email for fast track requests'
        },
        fnObj: {
            type: 'string',
            description: 'JSON string for footnote objects'
        },
        receivedDate: {
            type: 'string',
            description: 'Received date for the article'
        },
        resubmission: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Flag indicating if this is a resubmission'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully generated job template XML",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "Complete XML job template with all metadata, author information, workflow configuration, and article structure. The template includes comprehensive article metadata, author details with affiliations, funding information, reviewer suggestions, keywords, custom metadata, and workflow stages.",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<article>\n  <front>\n    <journal-meta>...</journal-meta>\n    <article-meta>\n      <article-id pub-id-type=\"doi\">10.1234/example.doi</article-id>\n      <title-group>\n        <article-title>Example Article Title</article-title>\n      </title-group>\n      <contrib-group>\n        <contrib contrib-type=\"author\">\n          <name>\n            <surname>Smith</surname>\n            <given-names>John</given-names>\n          </name>\n          <email><EMAIL></email>\n        </contrib>\n      </contrib-group>\n      <kwd-group>\n        <kwd>keyword1</kwd>\n        <kwd>keyword2</kwd>\n      </kwd-group>\n    </article-meta>\n  </front>\n  <body></body>\n  <back></back>\n  <workflow>\n    <stage>\n      <name>Add Job</name>\n      <status>in-progress</status>\n    </stage>\n  </workflow>\n</article>"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - validation error or processing failure",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "Job already exists",
                                        "Project not found in project list",
                                        "JobTemplate not found in project list",
                                        "JobTemplate path not found",
                                        "Job already exists and is in Production stage"
                                    ]
                                },
                                "logData": {
                                    "type": "boolean",
                                    "description": "Whether to log this error"
                                }
                            }
                        },
                        "step": {
                            "type": "string",
                            "example": "add job"
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Template or configuration not found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Could not get article from database"
                                }
                            }
                        },
                        "step": {
                            "type": "string",
                            "example": "add job"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Job Management',
    description: "This endpoint generates comprehensive job templates for article processing workflows. It creates detailed XML templates with complete metadata, author information, workflow configuration, and article structure based on customer-specific templates and submission data.\n\n**Key Features:**\n\n- **Template Generation**: Creates XML job templates from customer-specific base templates\n- **Metadata Integration**: Incorporates comprehensive article metadata including titles, authors, affiliations, keywords\n- **Author Management**: Handles complex author structures with affiliations, contributions, ORCID IDs, and correspondence\n- **Reviewer Management**: Processes suggested and opposed reviewers with affiliations and keywords\n- **Funding Information**: Integrates funding sources, grant IDs, and acknowledgments\n- **Workflow Integration**: Sets up initial workflow stages and assignments\n- **Resubmission Handling**: Manages article resubmissions and updates\n\n**Template Components:**\n\n- **Article Metadata**: DOI, title, article type, subject categories\n- **Author Information**: Names, affiliations, emails, ORCID IDs, contributions, equal contributions\n- **Reviewer Data**: Suggested/opposed reviewers with reasons and keywords\n- **Funding Details**: Grant information, funders, acknowledgments\n- **Keywords**: Multiple keyword groups and categories\n- **Custom Metadata**: Flexible custom fields and configurations\n- **Workflow Setup**: Initial stage configuration and assignments\n\n**Advanced Features:**\n\n- **Force Reload**: Ability to reload existing jobs with updated metadata\n- **Fast Track Processing**: Priority handling for urgent submissions\n- **Group Authors**: Support for collaborative authorship\n- **Data Availability**: Integration of data availability statements\n- **Related Articles**: Cross-referencing with related publications\n- **Journal Preferences**: Multi-journal submission preferences\n\n**Validation and Error Handling:**\n\n- Validates existing job status before creation\n- Checks project and template availability\n- Handles PVRH file dependencies\n- Manages stage-specific restrictions\n\nThe endpoint serves as the foundation for article processing workflows, creating structured XML documents that drive the entire editorial and production process.",
    summary: "Generate comprehensive job template XML with metadata, authors, and workflow configuration",
    queryParams,
    response
};
