const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        workflowType: {
            type: 'string',
            enum: ['milestone', 'stub'],
            description: 'Type of workflow to process (milestone or stub)'
        },
        xpath: {
            type: 'string',
            description: 'XPath expression for milestone workflow type'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        workflowType: {
            type: 'string',
            enum: ['milestone', 'stub'],
            description: 'Type of workflow to process (milestone or stub)'
        },
        xpath: {
            type: 'string',
            description: 'XPath expression for milestone workflow type'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved article metadata",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "description": "Article DOI identifier"
                        },
                        "customer": {
                            "type": "string",
                            "description": "Customer identifier"
                        },
                        "project": {
                            "type": "string",
                            "description": "Project identifier"
                        },
                        "title": {
                            "type": "string",
                            "description": "Article title"
                        },
                        "doi": {
                            "type": "string",
                            "description": "Document Object Identifier"
                        },
                        "articleType": {
                            "type": "string",
                            "description": "Type of article (research, review, etc.)"
                        },
                        "stageName": {
                            "type": "string",
                            "description": "Current workflow stage name"
                        },
                        "workflowStatus": {
                            "type": "string",
                            "description": "Current workflow status"
                        },
                        "priority": {
                            "type": "string",
                            "description": "Article priority (on/off)"
                        },
                        "authors": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "surname": {"type": "string"},
                                            "given-names": {"type": "string"},
                                            "email": {"type": "string"},
                                            "affiliations": {"type": "array", "items": {"type": "string"}},
                                            "orcid": {"type": "string"},
                                            "corresponding-author": {"type": "string"},
                                            "submitting-author": {"type": "string"},
                                            "deceased": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "affiliations": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "id": {"type": "string"},
                                    "institution": {"type": "string"},
                                    "department": {"type": "string"},
                                    "city": {"type": "string"},
                                    "state": {"type": "string"},
                                    "country": {"type": "string"},
                                    "postal_code": {"type": "string"},
                                    "ror": {"type": "string"},
                                    "ringgold_id": {"type": "string"}
                                }
                            }
                        },
                        "reviewerData": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "object",
                                                "properties": {
                                                    "surname": {"type": "string"},
                                                    "given-names": {"type": "string"}
                                                }
                                            },
                                            "email": {"type": "string"},
                                            "author-type": {"type": "string"},
                                            "article-key": {"type": "string"},
                                            "duedate": {"type": "string"},
                                            "assigned-date": {"type": "string"},
                                            "decision-date": {"type": "string"},
                                            "completed-date": {"type": "string"},
                                            "workflowStatus": {"type": "string"},
                                            "id": {"type": "string"},
                                            "message": {"type": "string"},
                                            "version": {"type": "string"},
                                            "lastUpdated": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        },
                        "authorSuggestedReviewers": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "prefix": {"type": "string"},
                                    "surname": {"type": "string"},
                                    "givenname": {"type": "string"},
                                    "middlename": {"type": "string"},
                                    "suffix": {"type": "string"},
                                    "email": {"type": "string"},
                                    "orcid": {"type": "string"},
                                    "reason": {"type": "string"},
                                    "affiliation": {"type": "array"}
                                }
                            }
                        },
                        "reviewerSuggestedReviewers": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "prefix": {"type": "string"},
                                    "surname": {"type": "string"},
                                    "givenname": {"type": "string"},
                                    "middlename": {"type": "string"},
                                    "suffix": {"type": "string"},
                                    "email": {"type": "string"},
                                    "orcid": {"type": "string"},
                                    "reason": {"type": "string"},
                                    "affiliation": {"type": "array"}
                                }
                            }
                        },
                        "volume": {"type": "string"},
                        "issue": {"type": "string"},
                        "word-count": {"type": "string"},
                        "fig-count": {"type": "string"},
                        "table-count": {"type": "string"},
                        "ref-count": {"type": "string"},
                        "acceptedDate": {"type": "string"},
                        "receivedDate": {"type": "string"},
                        "epubDate": {"type": "string"},
                        "keywords": {"type": "array", "items": {"type": "string"}},
                        "labels": {"type": "array"},
                        "system-labels": {"type": "array"},
                        "notes": {
                            "type": "object",
                            "properties": {
                                "count": {"type": "integer"},
                                "replied": {"type": "integer"}
                            }
                        },
                        "stage": {"type": "array"},
                        "lastUpdated": {"type": "string"},
                        "article-version": {"type": "string"},
                        "review-status": {"type": "string"}
                    },
                    "additionalProperties": true,
                    "description": "Comprehensive article metadata extracted from XML"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or invalid XML",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 400
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "One or more of required parameters (customer id, project id, xpath) is/are not provided.",
                                        "input XML is missing."
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "This endpoint retrieves comprehensive article metadata by extracting information from XML documents stored in the system. It processes article data including authors, affiliations, reviewers, workflow status, bibliographic information, and various metadata fields. The endpoint supports different workflow types (standard articles, milestones, and stubs) and uses configurable XPath expressions to extract specific data elements. The response includes detailed information about the article's current state, review process, and publication metadata.",
    summary: "Get comprehensive article metadata from XML",
    queryParams,
    requestBody,
    response
};
