const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier for filtering stage templates'
        },
        articleType: {
            type: 'string',
            description: 'Article type for filtering stage templates'
        },
        customerType: {
            type: 'string',
            description: 'Type of customer configuration',
            default: 'journal',
            examples: ['journal', 'book', 'conference']
        },
        pageName: {
            type: 'string',
            description: 'Page name for configuration context',
            default: 'dashboard'
        }
    },
    required: ['customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved customer configuration",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "stage": {
                            "type": "object",
                            "description": "Stage configuration including templates and milestone information",
                            "properties": {
                                "stagetemplate": {
                                    "type": "array",
                                    "description": "Array of workflow stages with their configuration",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "description": "Stage name"
                                            },
                                            "type": {
                                                "type": "string",
                                                "description": "Stage type",
                                                "examples": ["editorial", "production", "review"]
                                            },
                                            "kanbanDisplayName": {
                                                "type": "string",
                                                "description": "Display name for Kanban board"
                                            }
                                        }
                                    }
                                },
                                "productionStageNames": {
                                    "type": "array",
                                    "description": "Array of production stage names",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "description": "Production stage name"
                                            }
                                        }
                                    }
                                },
                                "milestonetemplate": {
                                    "type": "array",
                                    "description": "Array of milestone stages",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "description": "Milestone stage name"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        "accessconfig": {
                            "type": "object",
                            "description": "Access configuration settings merged from default and customer-specific configurations",
                            "additionalProperties": true,
                            "example": {
                                "dashboard": {
                                    "filters": ["status", "stage", "priority"],
                                    "columns": ["title", "doi", "stage", "assignee"],
                                    "actions": ["edit", "view", "assign"]
                                },
                                "permissions": {
                                    "canEdit": true,
                                    "canDelete": false,
                                    "canAssign": true
                                }
                            }
                        },
                        "customconfig": {
                            "type": "object",
                            "description": "Custom label configuration for UI customization",
                            "additionalProperties": true,
                            "example": {
                                "labels": {
                                    "articleTitle": "Manuscript Title",
                                    "submitButton": "Submit Article",
                                    "statusLabel": "Current Status"
                                },
                                "messages": {
                                    "welcomeText": "Welcome to the submission system",
                                    "helpText": "Contact support for assistance"
                                }
                            }
                        }
                    },
                    "additionalProperties": false
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing customer parameter or configuration error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "Customer is missing",
                        "Configuration file not found"
                    ]
                }
            }
        }
    },
    "404": {
        "description": "Customer configuration not found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response when configuration files are missing"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Configuration Management',
    description: "This endpoint retrieves comprehensive customer configuration including workflow stages, access permissions, and custom UI settings. It aggregates configuration from multiple sources:\n\n**Stage Configuration:**\n- Stage templates defining workflow stages with types and display names\n- Production stage names for production workflow management\n- Milestone templates for tracking article progress\n\n**Access Configuration:**\n- Default configuration merged with customer-specific overrides\n- Permission settings for different user roles and actions\n- UI component visibility and behavior settings\n- Custom configuration modifications applied per customer\n\n**Custom Configuration:**\n- Custom label configurations for UI text customization\n- Branding and messaging customization\n\nThe endpoint supports filtering stage templates by project and article type, and applies customer-specific customizations on top of default configurations. This is essential for providing a tailored experience for each customer's workflow and branding requirements.",
    summary: "Get comprehensive customer configuration including stages, access, and custom settings",
    queryParams,
    response
};
