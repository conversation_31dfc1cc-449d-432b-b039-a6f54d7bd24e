const queryParams = {
    type: 'object',
    properties: {
        project: {
            type: 'string',
            description: 'Project identifier to retrieve project-specific page template',
            required: true
        },
        pageName: {
            type: 'string',
            description: 'Name of the page for which to retrieve the template',
            required: true
        },
        role: {
            type: 'string',
            description: 'User role for role-based template customization'
        }
    },
    required: ['project', 'pageName'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved page template configuration",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML structure containing page template components including main template, header/toolbar, and component definitions",
                    "example": "<page><template><!DOCTYPE html><html><head><title>Page Template</title></head><body><div id=\"main-content\">...</div></body></html></template><header><div class=\"toolbar\"><button>Save</button><button>Cancel</button></div></header><components><component name=\"editor\" type=\"wysiwyg\">...</component></components></page>"
                }
            }
        }
    },
    "204": {
        "description": "No content - template configuration not found or file missing",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "File not found",
                        "Configuration not found"
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during template retrieval",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error details from file system or XML parsing operations"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Template Management',
    description: "This endpoint retrieves page template configurations for specific pages and projects. It assembles complete page templates by combining multiple template components including the main page template, toolbar/header templates, and component definitions.\n\n**Template Assembly Process:**\n\n1. **Configuration Lookup**: Searches for page configuration in `pageConfig.xml` based on customer, project, and page name\n2. **Template Retrieval**: Loads the main page template file specified in the configuration\n3. **Header Integration**: Includes toolbar/header template if specified\n4. **Component Assembly**: Adds component definitions and configurations\n5. **XML Compilation**: Combines all components into a unified XML structure\n\n**Template Structure:**\n\n- **Main Template**: Core HTML/XML structure for the page layout\n- **Header/Toolbar**: Navigation and action buttons specific to the page\n- **Components**: Reusable UI components and their configurations\n\n**Configuration Hierarchy:**\n\nThe system follows a hierarchical configuration approach:\n1. **Project-Specific**: Templates configured for specific project names\n2. **Default Fallback**: Uses 'default' project configuration if project-specific not found\n3. **Customer Context**: Templates are scoped to customer context from session\n\n**Template Attributes:**\n\n- `page-template`: Path to main page template file\n- `toolbar-template`: Path to header/toolbar template file\n- `component-template`: Path to component definitions file\n\n**File Path Resolution:**\n\nTemplate paths are dynamically resolved based on:\n- CMS version (v2.0, v3.0, etc.)\n- Customer context\n- Project specifications\n- Page type requirements\n\n**Use Cases:**\n\n- **Page Rendering**: Loading complete page templates for UI rendering\n- **Dynamic UI**: Building context-aware user interfaces\n- **Template Management**: Managing and updating page layouts\n- **Component Integration**: Assembling reusable UI components\n- **Multi-tenant Support**: Customer and project-specific customizations\n\n**Template Types:**\n\n- **Editor Pages**: Rich text editing interfaces with specialized toolbars\n- **Dashboard Pages**: Overview and navigation interfaces\n- **Review Pages**: Content review and approval workflows\n- **Configuration Pages**: System and project settings interfaces\n\n**Version Management:**\n\nSupports multiple CMS versions with automatic path resolution:\n- Templates are version-specific to maintain compatibility\n- Fallback mechanisms ensure system stability\n- Version-specific feature sets and UI components\n\nThe endpoint provides a complete template assembly service that enables dynamic, context-aware page rendering while maintaining separation of concerns between layout, functionality, and content.",
    summary: "Retrieve assembled page templates with headers, components, and configurations",
    queryParams,
    response
};
