const queryParams = {
    type: 'object',
    properties: {
        type: {
            type: 'string',
            description: 'Type of reference component to retrieve',
            enum: ['stylus', 'getCitation', 'modal'],
            required: true
        },
        customer: {
            type: 'string',
            description: 'Customer identifier for accessing customer-specific templates',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier for accessing project-specific style templates',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for article-specific data (required for getCitation type)'
        }
    },
    required: ['type', 'customer', 'project'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved reference component data",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML content for reference components. Content varies based on type parameter.",
                    "examples": {
                        "stylus": "<ref-modal><select class=\"btn btn-hover referenceTypes\"><option value=\"journal\">journal</option></select><div data-ref-type=\"journal\"><span class=\"btn btn-hover\">Title</span><span class=\"btn btn-hover\">Author</span></div></ref-modal>",
                        "modal": "<div data-ref-type=\"journal\"><div class=\"row input-field\"><div class=\"col s2\"><label>Title</label></div><div class=\"col s10\"><p contenteditable=\"true\" class=\"text-line\"></p></div></div></div>",
                        "getCitation": "Citation format configuration based on project style template and article metadata"
                    }
                }
            },
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "JSON object containing citation configuration (for getCitation type)",
                    "properties": {
                        "citation": {
                            "type": "object",
                            "properties": {
                                "supplementOrder": {
                                    "type": "string",
                                    "description": "Supplement ordering configuration"
                                },
                                "jrnlSupplRef": {
                                    "type": "string",
                                    "description": "Journal supplement reference format"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - invalid method, missing parameters, or configuration errors",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "Invalid Method",
                        "Project not found",
                        "Citations not found"
                    ]
                }
            },
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "Project not found",
                                        "Style template not found"
                                    ]
                                }
                            }
                        },
                        "step": {
                            "type": "string",
                            "example": "add stage"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during component generation",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from template processing or file operations"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Reference Management',
    description: "This endpoint provides reference component generation and citation management functionality. It supports multiple types of reference-related operations including style palette generation, citation formatting, and reference editing modals.\n\n**Component Types:**\n\n**1. Stylus (`type=stylus`)**\n- Generates interactive style palette for reference formatting\n- Creates dropdown menus for author/editor and other reference elements\n- Supports CSL (Citation Style Language) mapping integration\n- Provides clickable buttons for applying reference formatting\n- Organizes reference elements by categories (Authors/Editors, Others, Main elements)\n\n**2. Citation (`type=getCitation`)**\n- Retrieves citation configuration based on project style templates\n- Processes article metadata to determine citation format\n- Supports language-specific citation formats\n- Handles conditional citation elements based on article content\n- Returns JSON configuration for citation rendering\n\n**3. Modal (`type=modal`)**\n- Generates reference editing modal interfaces\n- Creates form fields for different reference types\n- Supports dynamic field generation based on reference identifiers\n- Provides validation and required field handling\n- Enables customer-specific template customizations\n\n**Style Template Integration:**\n\nThe endpoint integrates with customer and project-specific style templates:\n- Loads templates from `{customer}/config/{styleTemplatePath}`\n- Processes XML-based reference type definitions\n- Supports conditional element display based on XPath conditions\n- Handles reference type identification and classification\n\n**CSL Mapping Support:**\n\nFor stylus type, the system supports CSL integration:\n- Maps Kriya reference types to CSL types\n- Uses `cslMapping.json` for type conversions\n- Provides standardized citation element mapping\n- Enables compatibility with external citation systems\n\n**Reference Type Processing:**\n\nEach reference type is processed with:\n- **Identifiers**: XPath-based element identification\n- **Display Configuration**: Field labels, types, and validation\n- **Template Integration**: Custom HTML templates for specific customers\n- **Conditional Logic**: Show/hide elements based on content\n\n**Language Support:**\n\nCitation generation supports multiple languages:\n- Detects article language from metadata\n- Selects appropriate citation format\n- Falls back to default format if language-specific not available\n- Handles multilingual reference formatting\n\n**UI Component Generation:**\n\nThe endpoint generates various UI components:\n- **Dropdown Menus**: For author/editor selection\n- **Button Palettes**: For applying reference formatting\n- **Form Fields**: For reference data entry\n- **Validation Elements**: For required field checking\n\n**Use Cases:**\n\n- **Reference Editing**: Interactive reference formatting in editors\n- **Citation Management**: Automated citation format generation\n- **Style Application**: Applying journal-specific reference styles\n- **Template Customization**: Customer-specific reference workflows\n- **Multi-language Publishing**: Language-aware citation formatting\n\n**Integration Points:**\n\n- **Projects API**: Retrieves project configuration and style templates\n- **Article Data**: Accesses article metadata for citation processing\n- **Style Templates**: Loads customer/project-specific formatting rules\n- **CSL Systems**: Integrates with Citation Style Language standards\n\nThe endpoint provides a comprehensive solution for reference management, supporting both interactive editing interfaces and automated citation processing while maintaining flexibility for customer-specific requirements.",
    summary: "Generate reference components including style palettes, citation formats, and editing modals",
    queryParams,
    response
};
