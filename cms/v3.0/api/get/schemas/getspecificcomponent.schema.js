const queryParams = {
    type: 'object',
    properties: {
        componentTemplateName: {
            type: 'string',
            description: 'Name of the component template to retrieve components from',
            required: true
        },
        componentsList: {
            type: 'array',
            description: 'Array of component IDs to retrieve from the template',
            items: {
                type: 'string'
            },
            required: true
        },
        getConfig: {
            type: 'boolean',
            description: 'Flag to retrieve custom configuration instead of standard components'
        },
        customConfig: {
            type: 'string',
            description: 'Custom configuration name when getConfig is true'
        },
        customer: {
            type: 'string',
            description: 'Customer identifier for customer-specific configurations'
        }
    },
    required: ['componentTemplateName', 'componentsList'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved specific components",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "componentWrapper": {
                            "type": "string",
                            "description": "HTML string containing the requested components wrapped in a componentWrapper div",
                            "example": "<div class=\"componentWrapper\"><component id=\"header\"><title>Article Title</title></component><component id=\"abstract\"><content>Article abstract content</content></component></div>"
                        }
                    },
                    "required": ["componentWrapper"]
                }
            }
        }
    },
    "403": {
        "description": "Forbidden - component template not found or access denied",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "Unable to find the component template"
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during component processing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from component processing or file operations"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Component Management',
    description: "This endpoint retrieves specific components from component templates, allowing selective extraction of individual components or custom configurations. It supports both standard component templates and customer-specific custom configurations.",
    summary: "Retrieve specific components from component templates with support for custom configurations",
    queryParams,
    response
};
