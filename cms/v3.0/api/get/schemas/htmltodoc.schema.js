const queryParams = {
    type: 'object',
    properties: {
        doi: {
            type: 'string',
            description: 'Document Object Identifier for the article',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        page: {
            type: 'string',
            description: 'Page type for content rendering',
            enum: ['peer_review'],
            examples: ['peer_review']
        },
        exportword: {
            type: 'boolean',
            description: 'Flag to export as HTML for PHPWord processing'
        },
        exportPHPWord: {
            type: 'string',
            description: 'Legacy export flag for old PHPWord processing',
            enum: ['true-old']
        }
    },
    required: ['doi', 'project', 'customer'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully converted HTML to DOC format",
        "content": {
            "application/msword": {
                "schema": {
                    "type": "string",
                    "format": "binary",
                    "description": "Microsoft Word document content"
                }
            },
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML content when exportword=true"
                }
            }
        },
        "headers": {
            "Content-disposition": {
                "schema": {
                    "type": "string",
                    "example": "attachment; filename=10.1000/sample.doc"
                }
            },
            "Content-Type": {
                "schema": {
                    "type": "string",
                    "examples": ["application/msword", "text/html"]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing parameters or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "examples": ["Missing DOI", "Missing project", "Missing customer", "Can't get page template."]
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during HTML to DOC conversion",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from processing or conversion service"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Content Export',
    description: "This endpoint converts HTML content to Microsoft Word DOC format, providing document export functionality for articles and review content. It supports multiple rendering modes and integrates with external conversion services for high-quality document generation.",
    summary: "Convert HTML content to Microsoft Word DOC format with multiple rendering modes",
    queryParams,
    response
};
