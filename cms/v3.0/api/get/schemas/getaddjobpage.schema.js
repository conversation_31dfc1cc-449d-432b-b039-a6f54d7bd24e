const queryParams = {
    type: 'object',
    properties: {
        emailID: { 
            type: 'string',
            format: 'email',
            description: 'Email address of the author',
            required: true
        },
        authorName: { 
            type: 'string',
            description: 'Name of the author',
            required: true
        },
        customer: { 
            type: 'string',
            description: 'Customer identifier. If not provided, uses the first customer from user roles'
        }
    },
    required: ['emailID', 'authorName'],
    additionalProperties: false
};

const headers = {
    type: 'object',
    properties: {
        kuser: {
            type: 'string',
            description: 'User object containing authentication and role information',
            required: true
        }
    },
    required: ['kuser']
};

const response = {
    "200": {
        "description": "Success response with error message if validation fails",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 200
                                },
                                "message": {
                                    "type": "string",
                                    "enum": [
                                        "ERROR: Email ID is not provided.",
                                        "ERROR: Author Name is not provided."
                                    ]
                                }
                            }
                        }
                    },
                    "description": "Error response when required parameters are missing"
                }
            }
        }
    },
    "302": {
        "description": "Redirect to add job page with encrypted parameters",
        "headers": {
            "Location": {
                "schema": {
                    "type": "string",
                    "example": "/add_Job?key=encrypted_parameters"
                },
                "description": "Redirect URL with encrypted query parameters containing email, dateTime, name, role, and customer"
            }
        }
    }
};

module.exports = {
    tags: 'Job Management',
    description: "This endpoint validates required parameters (emailID and authorName) and redirects users to the add job page with encrypted parameters. It extracts customer information from user roles and creates an encrypted key containing email, timestamp, author name, role (publisher), and customer information. The endpoint requires user authentication through the kuser header.",
    summary: "Get add job page with encrypted parameters",
    queryParams,
    headers,
    response
};
