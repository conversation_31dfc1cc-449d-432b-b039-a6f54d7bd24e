const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Specific customer identifier to retrieve. Use "all" to get all customers regardless of user permissions. If not provided, returns customers based on user accessibility'
        }
    },
    additionalProperties: true
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Specific customer identifier to retrieve. Use "all" to get all customers regardless of user permissions. If not provided, returns customers based on user accessibility'
        }
    },
    additionalProperties: true
};

const headers = {
    type: 'object',
    properties: {
        kuser: {
            type: 'string',
            description: 'User object containing authentication and role information to determine customer accessibility'
        }
    }
};

const response = {
    "200": {
        "description": "Successfully retrieved customer data from Elasticsearch",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "hits": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "_source": {
                                        "type": "object",
                                        "properties": {
                                            "customer": {
                                                "type": "string",
                                                "description": "Customer identifier"
                                            },
                                            "name": {
                                                "type": "string",
                                                "description": "Customer display name"
                                            },
                                            "status": {
                                                "type": "string",
                                                "description": "Customer status (active, inactive, etc.)"
                                            },
                                            "type": {
                                                "type": "string",
                                                "description": "Customer type or category"
                                            }
                                        },
                                        "additionalProperties": true,
                                        "description": "Customer data from Elasticsearch source"
                                    },
                                    "_id": {
                                        "type": "string",
                                        "description": "Elasticsearch document ID"
                                    },
                                    "_index": {
                                        "type": "string",
                                        "description": "Elasticsearch index name"
                                    },
                                    "_score": {
                                        "type": "number",
                                        "description": "Elasticsearch relevance score"
                                    }
                                }
                            }
                        },
                        "total": {
                            "type": "object",
                            "properties": {
                                "value": {
                                    "type": "integer",
                                    "description": "Total number of customers found"
                                },
                                "relation": {
                                    "type": "string",
                                    "description": "Relation type (eq, gte)"
                                }
                            }
                        },
                        "max_score": {
                            "type": "number",
                            "description": "Maximum relevance score"
                        }
                    },
                    "description": "Elasticsearch response containing customer data"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - no response received from Elasticsearch",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Did not get a response"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "403": {
        "description": "Forbidden - access denied or authentication error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response from Elasticsearch or authentication failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Customers',
    description: "This endpoint retrieves customer data from Elasticsearch. It supports two modes: 1) If a specific customer parameter is provided, it returns that particular customer's data. 2) If no customer is specified, it returns all customers based on the user's accessibility permissions from their role configuration. The endpoint uses user authentication to determine which customers the user has access to.",
    summary: "Get customers data",
    queryParams,
    requestBody,
    headers,
    response
};
