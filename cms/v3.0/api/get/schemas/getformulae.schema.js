const queryParams = {
    type: 'object',
    properties: {
        
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved equation editor configuration XML",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML configuration file for the equation editor containing mathematical formulae definitions, symbols, templates, and editor settings. This XML defines the available mathematical symbols, equation templates, and configuration options for the equation editor interface.",
                    "example": "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n<equation-editor>\n  <symbols>\n    <category name=\"Basic\">\n      <symbol latex=\"\\alpha\" unicode=\"α\" description=\"Alpha\"/>\n      <symbol latex=\"\\beta\" unicode=\"β\" description=\"Beta\"/>\n      <symbol latex=\"\\gamma\" unicode=\"γ\" description=\"Gamma\"/>\n    </category>\n    <category name=\"Operators\">\n      <symbol latex=\"\\sum\" unicode=\"∑\" description=\"Summation\"/>\n      <symbol latex=\"\\int\" unicode=\"∫\" description=\"Integral\"/>\n      <symbol latex=\"\\prod\" unicode=\"∏\" description=\"Product\"/>\n    </category>\n  </symbols>\n  <templates>\n    <template name=\"fraction\" latex=\"\\frac{numerator}{denominator}\"/>\n    <template name=\"square_root\" latex=\"\\sqrt{expression}\"/>\n    <template name=\"superscript\" latex=\"base^{exponent}\"/>\n    <template name=\"subscript\" latex=\"base_{subscript}\"/>\n  </templates>\n  <settings>\n    <font-size>12</font-size>\n    <default-color>#000000</default-color>\n    <background-color>#ffffff</background-color>\n  </settings>\n</equation-editor>"
                }
            }
        }
    },
    "400": {
        "description": "Equation configuration file not found",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "ERROR: The equations was not found in the server."
                }
            }
        }
    },
    "500": {
        "description": "Internal server error reading configuration file",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the file system error"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Editor Configuration',
    description: "This endpoint retrieves the equation editor configuration XML file that defines mathematical symbols, templates, and settings for the equation editor interface. The configuration includes:\n\n**Symbol Definitions:**\n- Mathematical symbols with LaTeX notation and Unicode representations\n- Categorized symbol groups (Greek letters, operators, relations, etc.)\n- Symbol descriptions and metadata\n\n**Equation Templates:**\n- Pre-defined equation structures (fractions, roots, integrals, etc.)\n- LaTeX template definitions for common mathematical expressions\n- Placeholder definitions for user input\n\n**Editor Settings:**\n- Font and display configuration\n- Color schemes and styling options\n- Editor behavior and interaction settings\n\n**Key Features:**\n\n- **Version-specific Configuration**: Supports different CMS versions with separate config files\n- **Comprehensive Symbol Library**: Extensive collection of mathematical symbols and operators\n- **Template System**: Pre-built templates for common mathematical structures\n- **Customizable Settings**: Configurable appearance and behavior options\n\n**Use Cases:**\n\n- Initializing equation editor interfaces in article editing systems\n- Providing symbol palettes and template libraries\n- Configuring mathematical notation rendering\n- Supporting LaTeX-based equation input and display\n\nThe endpoint reads the configuration from version-specific XML files stored in the database configuration directory, ensuring that the equation editor has access to all necessary symbols and templates for mathematical content creation.",
    summary: "Get equation editor configuration XML with symbols and templates",
    queryParams,
    response
};
