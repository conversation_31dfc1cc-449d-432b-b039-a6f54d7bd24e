const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier',
            required: true
        },
        process: {
            type: 'string',
            description: 'Process type for different UI rendering modes',
            enum: ['new'],
            examples: ['new']
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved S3 bucket listing as HTML directory structure",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML unordered list structure representing the S3 bucket directory hierarchy with files and folders",
                    "example": "<ul prefixKey=\"customer/project/doi\" class=\"data\">\n<li id=\"123456789\" class=\"folder\"><span class=\"icon\"><i class=\"material-icons\">folder</i></span><span class=\"name\">versions</span>\n<ul>\n<li id=\"123456790\" class=\"file\" data-url=\"versions/article.xml\"><span class=\"icon\"><i class=\"material-icons\">insert_drive_file</i></span><span class=\"name\"><a target=\"_blank\" href=\"resources/customer/project/doi/versions/article.xml\">article.xml</a></span></li>\n</ul>\n</li>\n</ul>"
                }
            },
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Empty string when no files found",
                    "example": ""
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or S3 access error",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error message or empty response"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during S3 bucket listing",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error details from S3 operations"
                }
            }
        }
    }
};

module.exports = {
    public: true,
    tags: 'File Management',
    description: "This endpoint retrieves and displays the contents of an S3 bucket as an HTML directory structure, providing a hierarchical view of files and folders for a specific customer, project, and DOI. It supports different rendering modes and integrates with AWS S3 for file management operations.",
    summary: "List S3 bucket contents as HTML directory structure with file metadata and download links",
    queryParams,
    response
};
