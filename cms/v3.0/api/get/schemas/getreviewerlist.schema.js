const bodyParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for article-specific reviewer data',
            required: true
        },
        from: {
            type: 'integer',
            description: 'Starting index for pagination',
            default: 0,
            minimum: 0
        },
        format: {
            type: 'string',
            description: 'Response format specification',
            enum: ['xml']
        },
        requiredData: {
            type: 'string',
            description: 'Comma-separated list of required data types',
            default: 'reviewerlist,reviewerhistory,artreviewerhistory,artreviewerlist',
            examples: [
                'reviewerlist,reviewerhistory',
                'artreviewerlist',
                'reviewerlist,reviewerhistory,artreviewerhistory,artreviewerlist'
            ]
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier for article-specific reviewer data',
            required: true
        },
        from: {
            type: 'integer',
            description: 'Starting index for pagination',
            default: 0,
            minimum: 0
        },
        format: {
            type: 'string',
            description: 'Response format specification',
            enum: ['xml']
        },
        requiredData: {
            type: 'string',
            description: 'Comma-separated list of required data types',
            default: 'reviewerlist,reviewerhistory,artreviewerhistory,artreviewerlist'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved reviewer data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Object containing different types of reviewer data based on requiredData parameter",
                    "properties": {
                        "reviewerlist": {
                            "type": "array",
                            "description": "List of available reviewers with production access level",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "userId": {
                                        "type": "string",
                                        "description": "Unique user identifier"
                                    },
                                    "userName": {
                                        "type": "string",
                                        "description": "User display name"
                                    },
                                    "email": {
                                        "type": "string",
                                        "description": "User email address"
                                    },
                                    "role": {
                                        "type": "string",
                                        "description": "User role in the system"
                                    },
                                    "accessLevel": {
                                        "type": "string",
                                        "example": "reviewer"
                                    }
                                }
                            }
                        },
                        "reviewerhistory": {
                            "type": "array",
                            "description": "Historical reviewer data across all articles",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "articleId": {
                                        "type": "string",
                                        "description": "Article identifier"
                                    },
                                    "reviewerId": {
                                        "type": "string",
                                        "description": "Reviewer user ID"
                                    },
                                    "reviewDate": {
                                        "type": "string",
                                        "description": "Date of review activity"
                                    },
                                    "status": {
                                        "type": "string",
                                        "description": "Review status"
                                    }
                                }
                            }
                        },
                        "artreviewerhistory": {
                            "type": "array",
                            "description": "Reviewer history specific to the requested article",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "reviewerId": {
                                        "type": "string",
                                        "description": "Reviewer user ID"
                                    },
                                    "reviewDate": {
                                        "type": "string",
                                        "description": "Date of review activity"
                                    },
                                    "reviewType": {
                                        "type": "string",
                                        "description": "Type of review performed"
                                    },
                                    "comments": {
                                        "type": "string",
                                        "description": "Review comments"
                                    }
                                }
                            }
                        },
                        "artreviewerlist": {
                            "type": "string",
                            "description": "XML data containing reviewer contributor group information from article metadata",
                            "example": "<contrib-group data-group=\"reviewer\"><contrib><name><surname>Smith</surname><given-names>John</given-names></name></contrib></contrib-group>"
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "examples": [
                                "Missing customer",
                                "Failed to get reviewerlist",
                                "Failed to get reviewerhistory"
                            ]
                        }
                    }
                }
            }
        }
    },
    "204": {
        "description": "No content - request processed but no data returned",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "message": {
                            "type": "string",
                            "description": "Details about why no content was returned"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during reviewer data retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from internal API calls or processing"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Reviewer Management',
    description: "This endpoint provides comprehensive reviewer data aggregation by making multiple internal API calls to gather different types of reviewer information. It serves as a centralized interface for accessing reviewer lists, historical data, and article-specific reviewer information.\n\n**Data Types Available:**\n\n**1. Reviewer List (`reviewerlist`)**\n- Retrieves users with reviewer access level and production role type\n- Includes user details like ID, name, email, and role information\n- Supports pagination with configurable starting index and size\n- Filters by customer, project, and DOI context\n\n**2. Reviewer History (`reviewerhistory`)**\n- Provides historical reviewer activity across all articles\n- Shows reviewer assignments, review dates, and status information\n- Useful for tracking reviewer workload and performance\n- Supports large result sets with pagination\n\n**3. Article Reviewer History (`artreviewerhistory`)**\n- Focuses on reviewer activity for a specific article (DOI)\n- Includes detailed review information, comments, and timestamps\n- Provides article-specific reviewer workflow tracking\n- Returns XML format data for detailed review information\n\n**4. Article Reviewer List (`artreviewerlist`)**\n- Extracts reviewer information directly from article metadata\n- Uses XPath to query contributor groups marked as reviewers\n- Returns XML data from the article's contrib-group elements\n- Provides structured reviewer attribution data\n\n**API Integration:**\n\nThe endpoint orchestrates multiple internal API calls:\n- **getuserdata**: For reviewer list with role and access filtering\n- **getarticleuserdata**: For reviewer history data (both general and article-specific)\n- **getdata**: For article metadata extraction using XPath queries\n\n**Retry Mechanism:**\n\nImplements robust error handling with:\n- **Automatic Retries**: Up to 3 attempts for failed API calls\n- **Exponential Backoff**: 5-second delay between retry attempts\n- **Comprehensive Logging**: Detailed error tracking for troubleshooting\n- **Graceful Degradation**: Continues processing even if some data types fail\n\n**Pagination Support:**\n\n- **Configurable Starting Point**: `from` parameter for result offset\n- **Large Result Sets**: Default size of 1000 records per request\n- **Efficient Data Retrieval**: Optimized for handling large reviewer datasets\n\n**Format Options:**\n\n- **JSON Response**: Default structured data format\n- **XML Integration**: Optional XML format for specific data types\n- **Mixed Content**: Combines JSON structure with XML data where appropriate\n\n**Use Cases:**\n\n- **Reviewer Assignment**: Finding available reviewers for article assignment\n- **Workload Management**: Tracking reviewer activity and availability\n- **Historical Analysis**: Analyzing reviewer performance and patterns\n- **Article Tracking**: Understanding review workflow for specific articles\n- **Reporting**: Generating reviewer activity and performance reports\n\n**Security Features:**\n\n- **API Key Authentication**: Uses internal API key for secure service-to-service communication\n- **Role-Based Access**: Filters data based on user roles and access levels\n- **Customer Isolation**: Ensures data access is scoped to appropriate customers\n- **Audit Trail**: Comprehensive logging for security and compliance\n\n**Performance Considerations:**\n\n- **Parallel Processing**: Concurrent API calls for improved response times\n- **Caching Opportunities**: Data structure supports caching strategies\n- **Scalable Architecture**: Designed to handle high-volume reviewer data\n- **Resource Optimization**: Efficient memory usage for large datasets\n\nThis endpoint provides a comprehensive solution for reviewer management, combining multiple data sources into a unified interface while maintaining performance, security, and reliability standards.",
    summary: "Aggregate reviewer data including lists, history, and article-specific reviewer information",
    bodyParams,
    queryParams,
    response
};
