const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier to filter projects. Use "all" for all customers or specific customer ID',
            examples: ['customer1', 'all']
        },
        includeFields: {
            type: 'string',
            description: 'Comma-separated list of fields to include in response',
            default: 'projects',
            examples: ['projects', 'projects,type', 'projects,config']
        },
        getProjectConfig: {
            type: 'boolean',
            description: 'Flag to include project configuration data'
        },
        getLogos: {
            type: 'boolean',
            description: 'Flag to include customer logo availability information'
        },
        modal: {
            type: 'string',
            description: 'Modal type for specific data filtering',
            enum: ['issueConfig'],
            examples: ['issueConfig']
        },
        getSpecificProjectData: {
            type: 'boolean',
            description: 'Flag to get data for a specific project'
        },
        journalName: {
            type: 'string',
            description: 'Journal name when getting specific project data',
            examples: ['Nature', 'Science']
        }
    },
    additionalProperties: false
};

const bodyParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier to filter projects',
            examples: ['customer1', 'all']
        },
        includeFields: {
            type: 'string',
            description: 'Comma-separated list of fields to include in response',
            examples: ['projects', 'projects,type']
        }
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved projects data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "_id": {
                                "type": "string",
                                "description": "Customer ID"
                            },
                            "_source": {
                                "type": "object",
                                "properties": {
                                    "projects": {
                                        "oneOf": [
                                            {
                                                "type": "array",
                                                "items": {
                                                    "type": "object",
                                                    "properties": {
                                                        "name": {
                                                            "type": "string",
                                                            "description": "Project name"
                                                        },
                                                        "type": {
                                                            "type": "string",
                                                            "description": "Project type"
                                                        },
                                                        "workflowTemplate": {
                                                            "type": "string",
                                                            "description": "Workflow template path"
                                                        },
                                                        "stageTemplate": {
                                                            "type": "string",
                                                            "description": "Stage template path"
                                                        }
                                                    }
                                                }
                                            },
                                            {
                                                "type": "object",
                                                "description": "Single project object when getSpecificProjectData=true"
                                            }
                                        ]
                                    },
                                    "type": {
                                        "type": "string",
                                        "description": "Customer type (journal, book, etc.)"
                                    },
                                    "config": {
                                        "type": "object",
                                        "description": "Project configuration data (when getProjectConfig=true)"
                                    },
                                    "logoPath": {
                                        "type": "string",
                                        "description": "Logo availability indicator (when getLogos=true)",
                                        "enum": ["true"]
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - failed to retrieve projects",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Did not get a response"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during projects retrieval",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Error details from processing"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Project Management',
    description: "This endpoint retrieves project information from the Elasticsearch database, providing comprehensive access to customer projects with flexible filtering, configuration data, and user-based access control. It supports multiple query modes and data enrichment options.",
    summary: "Retrieve customer projects with flexible filtering and user-based access control",
    queryParams,
    bodyParams,
    response
};
