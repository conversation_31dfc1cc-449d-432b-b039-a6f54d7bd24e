const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        xpath: {
            type: 'string',
            description: 'XPath expression to extract specific data from XML',
            default: '//article'
        },
        attribute: {
            type: 'string',
            description: 'Specific attribute to extract from the XML node'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: { 
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: { 
            type: 'string',
            description: 'Document Object Identifier (DOI) of the article',
            required: true
        },
        xpath: {
            type: 'string',
            description: 'XPath expression to extract specific data from XML',
            default: '//article'
        },
        attribute: {
            type: 'string',
            description: 'Specific attribute to extract from the XML node'
        },
        skipNotFound: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Skip file not found errors and return 404 status instead',
            default: 'false'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved XML data",
        "content": {
            "application/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML content extracted using the specified XPath expression"
                }
            },
            "text/plain": {
                "schema": {
                    "type": "string",
                    "description": "Attribute value when attribute parameter is specified"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or XML retrieval error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 400
                                },
                                "message": {
                                    "type": "string",
                                    "examples": [
                                        "One or more of required parameters (customer id, project id, xpath) is/are not provided.",
                                        "Did not get a response"
                                    ]
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "File not found (when skipNotFound is true)",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 404
                        }
                    }
                }
            }
        }
    },
    "502": {
        "description": "Bad Gateway - XML database service unavailable (automatically retried once)",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error response from XML database service"
                }
            }
        }
    }
};

module.exports = {
    tags: 'XML Data Access',
    description: "This endpoint retrieves XML data from the XML database using XPath expressions. It constructs a URL to fetch the XML file for a specific article and allows extraction of specific parts of the XML using XPath queries. If an attribute parameter is provided, it extracts the value of that specific attribute from the XML node. The endpoint includes automatic retry logic for 502 errors and supports skipping file not found errors. This is a core utility endpoint used by other APIs to access raw XML data.",
    summary: "Get XML data using XPath expressions",
    queryParams,
    requestBody,
    response
};
