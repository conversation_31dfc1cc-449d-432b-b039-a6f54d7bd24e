const bodyParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Source project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Source DOI to repurpose',
            required: true
        },
        repproject: {
            type: 'string',
            description: 'Target repurpose project identifier',
            required: true
        },
        repdoi: {
            type: 'string',
            description: 'Target repurpose DOI',
            required: true
        }
    },
    required: ['customer', 'project', 'doi', 'repproject', 'repdoi'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully repurposed article to new project",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 200
                        },
                        "content": {
                            "type": "string",
                            "example": "job loaded successfully"
                        }
                    }
                }
            }
        }
    },
    "204": {
        "description": "No content - repurpose operation failed",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "string",
                                    "example": "400"
                                },
                                "message": {
                                    "type": "string",
                                    "example": "Project not found in project list"
                                }
                            }
                        },
                        "step": {
                            "type": "string",
                            "description": "Step where the error occurred",
                            "examples": ["add job", "add stage"]
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer",
                                    "example": 400
                                },
                                "message": {
                                    "type": "string",
                                    "example": "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage cannot be done. Unexpected input"
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during repurpose operation",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error details from processing steps"
                }
            }
        }
    }
};

module.exports = {
    public: true,
    tags: 'Content Management',
    description: "This endpoint repurposes an existing article from one project to another, creating a new article entry with updated metadata, journal information, and workflow configuration. It performs comprehensive content transformation, resource migration, and workflow initialization for the repurposed content.",
    summary: "Repurpose existing article content to new project with metadata transformation and workflow initialization",
    bodyParams,
    response
};
