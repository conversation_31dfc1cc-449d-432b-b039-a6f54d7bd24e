const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier',
            required: true
        },
        project: {
            type: 'string',
            description: 'Project identifier',
            required: true
        },
        doi: {
            type: 'string',
            description: 'Document Object Identifier',
            required: true
        },
        currStage: {
            type: 'string',
            description: 'Current workflow stage for signoff options',
            required: true
        },
        flowName: {
            type: 'string',
            description: 'Workflow flow name',
            default: 'signoff',
            examples: ['signoff', 'author-chaser']
        },
        xpath: {
            type: 'string',
            description: 'XPath expression for article data extraction'
        }
    },
    required: ['customer', 'project', 'doi', 'currStage'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved signoff options with workflow configuration",
        "content": {
            "text/xml": {
                "schema": {
                    "type": "string",
                    "description": "XML response containing signoff options and workflow triggers",
                    "example": "<response><options><p><input type=\"radio\" name=\"signoff\" value=\"approve\" data-reviewer=\"editor\"/>Approve</p><p><input type=\"radio\" name=\"signoff\" value=\"reject\" data-reviewer=\"author\"/>Reject</p></options><trigger action=\"sendMail\" name=\"editor\" status=\"200\" next-stage=\"production\"><to><EMAIL></to><subject>Article Approved</subject><body><div class=\"email-body\">Your article has been approved.</div></body></trigger></response>"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing parameters or processing error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from workflow processing or data retrieval"
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "Not found - workflow configuration or user data not available",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details when workflow or user data cannot be found"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during signoff options processing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from internal processing or API calls"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "This endpoint generates signoff options for workflow stages, providing dynamic workflow configuration based on article content, user permissions, and conditional logic. It integrates multiple systems to create contextual signoff interfaces with email triggers and user assignments.",
    summary: "Generate dynamic signoff options with conditional logic, user assignments, and email triggers",
    queryParams,
    response
};
