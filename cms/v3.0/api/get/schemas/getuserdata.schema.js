const queryParams = {
    type: 'object',
    properties: {
        customer: {
            type: 'string',
            description: 'Customer identifier for user filtering'
        },
        searchUserMail: {
            type: 'string',
            description: 'Email address to search for specific user'
        },
        userEmail: {
            type: 'string',
            description: 'User email for direct user lookup'
        },
        orcid: {
            type: 'string',
            description: 'ORCID identifier for user lookup'
        },
        accessLevel: {
            type: 'string',
            description: 'Filter users by access level',
            examples: ['admin', 'editor', 'reviewer']
        },
        roleType: {
            type: 'string',
            description: 'Filter users by role type',
            examples: ['internal', 'external', 'freelance']
        },
        ignoreUserAccessForEdit: {
            type: 'string',
            description: 'Exclude users with specific access level from results'
        },
        journals: {
            oneOf: [
                {
                    type: 'string',
                    description: 'Journal filter - single journal or "all"'
                },
                {
                    type: 'array',
                    description: 'Array of journal identifiers',
                    items: {
                        type: 'string'
                    }
                }
            ]
        },
        allusers: {
            type: 'boolean',
            description: 'Flag to retrieve all users instead of filtered subset'
        },
        from: {
            type: 'integer',
            description: 'Pagination offset',
            minimum: 0,
            default: 0
        },
        size: {
            type: 'integer',
            description: 'Number of results to return',
            minimum: 1,
            maximum: 1000,
            default: 1000
        },
        sortKey: {
            type: 'string',
            description: 'Field to sort by',
            examples: ['first', 'last', 'email']
        },
        sortOrder: {
            type: 'string',
            description: 'Sort order',
            enum: ['asc', 'desc'],
            default: 'asc'
        },
        format: {
            type: 'string',
            description: 'Response format',
            enum: ['json', 'xml'],
            default: 'json'
        },
        scope: {
            type: 'string',
            description: 'Scope for customer data expansion',
            examples: ['customers']
        }
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved user data",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "array",
                            "description": "Array of user objects",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "object",
                                        "properties": {
                                            "first": {
                                                "type": "string",
                                                "example": "John"
                                            },
                                            "last": {
                                                "type": "string",
                                                "example": "Doe"
                                            }
                                        }
                                    },
                                    "email": {
                                        "type": "string",
                                        "example": "<EMAIL>"
                                    },
                                    "status": {
                                        "type": "string",
                                        "example": "active"
                                    },
                                    "roles": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "customer-name": {
                                                    "oneOf": [
                                                        {
                                                            "type": "string"
                                                        },
                                                        {
                                                            "type": "array",
                                                            "items": {
                                                                "type": "string"
                                                            }
                                                        }
                                                    ]
                                                },
                                                "access-level": {
                                                    "type": "string"
                                                },
                                                "role-type": {
                                                    "type": "string"
                                                }
                                            }
                                        }
                                    },
                                    "affiliation": {
                                        "type": "string",
                                        "example": "University of Example"
                                    },
                                    "expertise": {
                                        "type": "array",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "orcid": {
                                        "type": "string",
                                        "example": "0000-0000-0000-0000"
                                    },
                                    "totalCount": {
                                        "type": "integer",
                                        "description": "Total number of users matching the query (included in first result)"
                                    }
                                },
                                "additionalProperties": true
                            }
                        },
                        {
                            "type": "string",
                            "description": "XML formatted user data when format=xml"
                        }
                    ]
                }
            }
        }
    },
    "400": {
        "description": "Bad request - invalid parameters or query error",
        "content": {
            "application/json": {
                "schema": {
                    "oneOf": [
                        {
                            "type": "object",
                            "properties": {
                                "error": {
                                    "type": "string",
                                    "example": "Missing customer"
                                }
                            }
                        },
                        {
                            "type": "string",
                            "example": "400"
                        }
                    ]
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during user data retrieval",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "error": {
                            "type": "string",
                            "description": "Error details from processing or Elasticsearch"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "This endpoint retrieves user data from Elasticsearch with comprehensive filtering, sorting, and formatting options. It supports both individual user lookup and bulk user queries with role-based access control and customer-specific filtering.",
    summary: "Retrieve user data with advanced filtering, role-based access control, and flexible formatting",
    queryParams,
    response
};
