const queryParams = {
    type: 'object',
    properties: {
        fileData: {
            type: 'array',
            description: 'Array of file objects to be included in the ZIP archive',
            items: {
                type: 'object',
                properties: {
                    file_name: {
                        type: 'string',
                        description: 'Name of the file to be saved in the ZIP'
                    },
                    file_href: {
                        type: 'string',
                        description: 'Path/URL to the file resource'
                    }
                },
                required: ['file_name', 'file_href']
            },
            minItems: 1,
            required: true
        }
    },
    required: ['fileData'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully created and returned ZIP file containing requested files",
        "headers": {
            "Content-Type": {
                "schema": {
                    "type": "string",
                    "example": "application/zip"
                },
                "description": "Content type header indicating ZIP format"
            }
        },
        "content": {
            "application/zip": {
                "schema": {
                    "type": "string",
                    "format": "binary",
                    "description": "ZIP archive containing all requested files. The ZIP file is streamed directly to the client and automatically cleaned up after a short delay. Files are downloaded from the resources service, packaged into a ZIP archive, and streamed back to the client."
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing or invalid file data",
        "content": {
            "application/json": {
                "schema": {
                    "type": "string",
                    "examples": [
                        "Filelist is missing",
                        "Invalid file data format",
                        "Download failed for all files"
                    ]
                }
            }
        }
    },
    "404": {
        "description": "File not found or read error",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "ERROR : file read error in AWS"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error during ZIP creation or file processing",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the failure during ZIP creation or file download"
                }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "This endpoint creates and returns a ZIP archive containing multiple files specified in the request. It downloads files from the resources service, packages them into a ZIP archive, and streams the result back to the client.\n\n**Key Features:**\n\n- **Bulk File Download**: Downloads multiple files and packages them into a single ZIP archive\n- **Streaming Response**: Streams the ZIP file directly to the client for efficient memory usage\n- **Automatic Cleanup**: Temporary files and ZIP archives are automatically cleaned up after delivery\n- **Error Handling**: Handles partial failures gracefully, creating ZIP with successfully downloaded files\n- **Retry Logic**: Implements retry mechanism for failed downloads (502/404 errors)\n\n**Process Flow:**\n\n1. **File List Processing**: Parses the provided file data array\n2. **Parallel Downloads**: Downloads all specified files concurrently from the resources service\n3. **ZIP Creation**: Uses 7zip to create a compressed archive containing all downloaded files\n4. **Streaming Delivery**: Streams the ZIP file directly to the client\n5. **Cleanup**: Automatically removes temporary files and ZIP archive after delivery\n\n**Use Cases:**\n\n- Bulk download of article assets (figures, tables, supplements)\n- Creating article packages for external systems\n- Archiving and backup operations\n- Providing complete file sets to authors or reviewers\n- Integration with publishing workflows requiring file bundles\n\nThe endpoint uses authenticated requests to the resources service and handles various file types and formats. It's optimized for handling large file sets efficiently while maintaining system performance.",
    summary: "Create and download ZIP archive containing multiple files",
    queryParams,
    response
};
