const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string', 
            minLength: 1,
            description: 'Customer identifier (e.g., bmj, bir, elife, mbs, frontiers)'
        },
        project: { 
            type: 'string', 
            minLength: 1,
            description: 'Project name or journal abbreviation (e.g., elife, jgv, ijsem, heartjrnl)'
        },
        doi: { 
            type: 'string', 
            minLength: 1,
            description: 'Digital Object Identifier of the article'
        },
        articleState: { 
            type: 'string',
            enum: ['open', 'read-only'],
            description: 'State of the article, determines if content is editable'
        },
        articlesRole: { 
            type: 'string',
            description: 'Role of the user accessing the article (e.g., preeditor, typesetter, publisher, editor, copyeditor)'
        },
        customerType: { 
            type: 'string',
            enum: ['journal', 'book'],
            description: 'Type of customer, affects reference handling and PDF paths'
        },
        skiplambda: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Skip lambda-based XSLT transformation'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Successfully retrieved and transformed article data",
        "content": {
            "text/html": {
                "schema": {
                    "type": "string",
                    "description": "HTML content of the transformed article with embedded navigation, queries, footnotes, and other components"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters or invalid XML",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "redirect": {
                            "type": "string",
                            "example": "error_page"
                        },
                        "page": {
                            "type": "string",
                            "example": "500"
                        },
                        "msg": {
                            "type": "string",
                            "example": "Unable to fetch requested data. Invalid XML"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Server error - missing required parameters or processing failure",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "number",
                                    "example": 500
                                },
                                "message": {
                                    "type": "string",
                                    "example": "One or more of required parameters (customer id, project id, current stage) is/are not provided. Unexpected input"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Articles',
    description: "This endpoint retrieves article XML data, transforms it to HTML using XSLT, and returns the processed content with embedded components like navigation, queries, footnotes, and other editorial elements. The endpoint handles both manuscript and reference data, applies customer-specific transformations, and supports various article states and user roles.",
    summary: "Get transformed article data as HTML",
    queryParams,
    response
};
