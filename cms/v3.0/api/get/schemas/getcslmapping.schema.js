const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier (optional for CSL mapping)'
        },
        project: { 
            type: 'string',
            description: 'Project identifier (optional for CSL mapping)'
        },
        apiKey: {
            type: 'string',
            description: 'API key required when calling from outside the system'
        }
    },
    additionalProperties: false
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier (optional for CSL mapping)'
        },
        project: { 
            type: 'string',
            description: 'Project identifier (optional for CSL mapping)'
        },
        apiKey: {
            type: 'string',
            description: 'API key required when calling from outside the system'
        }
    },
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved CSL mapping configuration",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "CSL (Citation Style Language) mapping configuration containing field mappings and citation formatting rules",
                    "additionalProperties": true,
                    "example": {
                        "fieldMappings": {
                            "title": {
                                "xpath": "//element-citation//article-title",
                                "cslField": "title",
                                "required": true
                            },
                            "author": {
                                "xpath": "//element-citation//person-group[@person-group-type='author']//name",
                                "cslField": "author",
                                "type": "array"
                            },
                            "journal": {
                                "xpath": "//element-citation//source",
                                "cslField": "container-title"
                            },
                            "volume": {
                                "xpath": "//element-citation//volume",
                                "cslField": "volume"
                            },
                            "issue": {
                                "xpath": "//element-citation//issue",
                                "cslField": "issue"
                            },
                            "pages": {
                                "xpath": "//element-citation//fpage",
                                "cslField": "page"
                            },
                            "year": {
                                "xpath": "//element-citation//year",
                                "cslField": "issued"
                            },
                            "doi": {
                                "xpath": "//element-citation//pub-id[@pub-id-type='doi']",
                                "cslField": "DOI"
                            }
                        },
                        "citationTypes": {
                            "journal": {
                                "name": "Journal Article",
                                "cslType": "article-journal",
                                "requiredFields": ["title", "author", "journal", "year"]
                            },
                            "book": {
                                "name": "Book",
                                "cslType": "book",
                                "requiredFields": ["title", "author", "publisher", "year"]
                            },
                            "chapter": {
                                "name": "Book Chapter",
                                "cslType": "chapter",
                                "requiredFields": ["title", "author", "container-title", "year"]
                            }
                        },
                        "formatOptions": {
                            "dateFormat": "YYYY-MM-DD",
                            "nameFormat": "family-given",
                            "titleCase": true
                        }
                    }
                }
            }
        }
    },
    "404": {
        "description": "CSL mapping configuration not found",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "status": {
                            "type": "integer",
                            "example": 404
                        },
                        "content": {
                            "type": "string",
                            "example": "There is No configuration present"
                        }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Internal server error",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "description": "Error object with details about the failure"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Citation Management',
    description: "This endpoint retrieves the CSL (Citation Style Language) mapping configuration used for citation formatting and reference management. The CSL mapping defines how XML citation elements are mapped to CSL JSON fields, enabling proper citation formatting according to various academic styles.\n\nThe configuration includes:\n\n- **Field Mappings**: XPath expressions that map XML citation elements to CSL fields (title, author, journal, etc.)\n- **Citation Types**: Definitions for different reference types (journal articles, books, chapters, etc.) with their required fields\n- **Format Options**: Settings for date formats, name formats, and other formatting preferences\n\nThis configuration is essential for the citation editor and reference formatting functionality, ensuring that citations are properly structured and can be formatted according to different citation styles (APA, MLA, Chicago, etc.).",
    summary: "Get CSL mapping configuration for citation formatting",
    queryParams,
    requestBody,
    response
};
