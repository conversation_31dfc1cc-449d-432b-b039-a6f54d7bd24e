const queryParams = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier to retrieve service details for',
            required: true
        },
        service: { 
            type: 'string',
            description: 'Service identifier to retrieve details for',
            required: true
        }
    },
    required: ['customer', 'service'],
    additionalProperties: false
};

const requestBody = {
    type: 'object',
    properties: {
        customer: { 
            type: 'string',
            description: 'Customer identifier to retrieve service details for',
            required: true
        },
        service: { 
            type: 'string',
            description: 'Service identifier to retrieve details for',
            required: true
        }
    },
    required: ['customer', 'service'],
    additionalProperties: false
};

const response = {
    "200": {
        "description": "Successfully retrieved customer service details",
        "content": {
            "application/json": {
                "schema": {
                    "type": "object",
                    "properties": {
                        "id": {
                            "type": "string",
                            "description": "Elasticsearch document ID"
                        },
                        "customer": {
                            "type": "string",
                            "description": "Customer identifier"
                        },
                        "service": {
                            "type": "string",
                            "description": "Service identifier"
                        },
                        "service-details": {
                            "type": "object",
                            "description": "Decrypted service configuration details",
                            "additionalProperties": true
                        },
                        "created_date": {
                            "type": "string",
                            "format": "date-time",
                            "description": "Service creation timestamp"
                        },
                        "updated_date": {
                            "type": "string",
                            "format": "date-time",
                            "description": "Service last update timestamp"
                        },
                        "status": {
                            "type": "string",
                            "description": "Service status (active, inactive, etc.)"
                        }
                    },
                    "additionalProperties": true,
                    "description": "Customer service configuration data"
                }
            }
        }
    },
    "400": {
        "description": "Bad request - missing required parameters",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "Parameters missing!"
                }
            }
        }
    },
    "500": {
        "description": "Internal server error - unable to fetch service details",
        "content": {
            "text/plain": {
                "schema": {
                    "type": "string",
                    "example": "Unable to fetch service details"
                }
            }
        }
    }
};

module.exports = {
    tags: 'Customer Services',
    description: "This endpoint retrieves specific service configuration details for a given customer and service combination. The service details are stored encrypted in Elasticsearch and are automatically decrypted before being returned. This is used to get service-specific configurations and settings for customer implementations.",
    summary: "Get customer service configuration details",
    queryParams,
    requestBody,
    response
};
