const cheerio = require('cheerio');
const libxmljs = require('libxmljs');
const moment = require('moment-business-days');
const xmlJS = require('xml-js');
const fs = require('fs');
const strformat = require('strformat');
const path = require('path');
const getReviewerDueDateByCustomer = require('@helpers/reviewerduedate.config.js');
const axios = require('axios');

function getProjects(req, res){
	return new Promise(function(resolve, reject) {
		var projectsApi = require('../../api/get/projects.js');
		projectsApi['processapi'](req, res)
			.then(function(response){
				if (response.length == 1 && response[0]._source){
					resolve(response[0]._source);
				}else{
					resolve(response)
				}
			})
			.catch(function(err){
				reject(err)
			})	
	});
}

function allConditionsMatch(conditions, reqData) {
    // Check 'except' conditions first
    if (conditions['except']) {
        for (const [exceptKey, exceptValue] of Object.entries(conditions['except'])) {
            if (typeof exceptValue === 'string' && exceptValue.includes('*')) {
                const regex = new RegExp(`^${exceptValue.replace(/\*/g, '.*')}$`);
                if (regex.test(reqData[exceptKey] || '')) {
                    return false;
                }
            } else if (reqData[exceptKey] == exceptValue) {
                return false;
            }
        }
    }

    // Now check main conditions
    for (const [key, value] of Object.entries(conditions)) {
        if (key === "except") continue;

        if (typeof value === 'string' && value.includes('*')) {
            const regex = new RegExp(`^${value.replace(/\*/g, '.*')}$`);
            if (!regex.test(reqData[key] || '')) {
                return false;
            }
        } else if (reqData[key] != value) {
            return false;
        }
    }

    return true;
}

// To get Peer Review Config
function getPeerReviewConfig(cmsVersion='v3.0', customer){
	if(!cmsVersion || !customer){
		return {}
	}
	var customerJson = path.resolve('./cms/' + cmsVersion + '/js/submission/config/' + customer + '/peerreview_config.json');
	if(!fs.existsSync(customerJson)){
		customerJson = path.resolve('./cms/' + cmsVersion + '/js/submission/config/default/peerreview_config.json');
	}
	var jsonConfig = require(customerJson);
	return jsonConfig
}

function getArticle(req, res){
	return new Promise(function(resolve, reject) {
		if ((req.body && req.body.workflowType && req.body.workflowType == 'milestone') || (req.query && req.query.workflowType && req.query.workflowType == 'milestone')) {
			var issuedata = require("../post/issuedata.js")
			issuedata['processapi'](req, res)
				.then(function (response) {
					resolve(response);
				})
				.catch(function (err) {
					resolve(false)
				})
		} else {
		var getData = require('../../api/get/getdata.js');
		getData['processapi'](req, res)
			.then(function(response){
				resolve(response);
			})
			.catch(function(err){
				resolve(false)
			})	
		}
	});
}


var postsFunction = {
	processRequest: function(req, res){
		return new Promise(function(resolve, reject) {
			var currStage = req.query.currStage;
			var o = req.body;
			// check if we have the bare minimum to handle the requested
			// this means we need the customer id, project id and the current stage
			if (typeof(o.customer) === 'undefined' || o.customer === '' || typeof(o.project) === 'undefined' || o.project === '' || typeof(currStage) === 'undefined' || currStage === ''){
				logger.error(`One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage (${currStage}) cannot be done. Unexpected input`, { reqData: extractReqData(req), fileName: "workflow" });
				reject({
					status: {code: 500, message: "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage (" + currStage + ") cannot be done. Unexpected input"},
					step: "perform trigger action based on workflow template"
				});
				return false;
			}
			
			//to log details in customer/project folder
			req.query.project = o.project;
			req.query.customer = o.customer;
			req.query.doi = o.doi;

			module.exports.getWorkflowXML(req, res)
				.then(function(wfXML){
					let $ = cheerio.load(wfXML, {recognizeSelfClosing: true});
					req.body.stage = $('flow').attr('display') + ' (' + $('flow').attr('name') + ')';;
					//get direct trigger in flow added by vijayakumar 11-12-2018 
					var triggers = $('flow').find('> trigger[status=' + req.body.status.code + ']');
					//to get more detailed with types and sub-types inside a flow
					if (req.body.type && req.body.status.code){
						var triggers = $('flow').find('> trigger[name="' + req.body.type + '"][status=' + req.body.status.code + ']');
					}else if (req.body.type){
						var triggers = $('flow').find('> trigger[name="' + req.body.type + '"]');
					}
					// if we are not able to fetch the trigger(s) based on the 'status' data then return without any further processing
					if (triggers.length == 0){
						logger.error('unable to fetch trigger information from workflow template. Unexpected input', {reqData: extractReqData(req, ["type", "body.status.code"]), fileName: 'workflow'});
						reject({
							status: {code: 500, message: "unable to fetch trigger information from workflow template. Unexpected input"},
							step: "get triggers from workflow template"
						});
					}
					// loop through the triggers element
					triggers.each(function(i, elem) {
						var useParam = $(elem).attr('use-param');
						if (useParam){
							var parameter = $('parameter[name=' + useParam + ']');
							$('parameter[name=' + useParam + '] variable').each(function(i, childElement){
								var elementName = $(childElement).attr('name');
								var elemChildren = $(elem).find(elementName);
								if (elemChildren.length){
									$(elemChildren[0]).html($(childElement).attr('value'));
								}
								else{
									$(elem).append('<' + elementName + '>' + $(childElement).attr('value') + '</' + elementName + '>');
								}
							})
						}
						var currAction = $(elem).attr('action');
						var action = $(elem).attr('action').toLocaleLowerCase();
						try {
							var performAction = require('./' + action + '.js');
							performAction['processapi'](req, res, $.html(elem))
								.then(function(info){
									// to process triggers inside a trigger on its success to avoid xml manipulation at same time - JAI -07-09-2018
									// childsynctrigger will not be handled in this callback, those triggers will be handled in processParentSyncTrigger
									$(elem).find('trigger:not([childsynctrigger="true"])').each(function(i, elem) {
										let syncTrigger = false;
										if($(elem).attr('parentsynctrigger')) {
											syncTrigger = true;
										}
										// If any trigger has to process in sync manner,the below function will be called based on this attr parentsynctrigger
										if(syncTrigger) {
											module.exports.processParentSyncTrigger(elem,req,res);
										}
										else {
										var useParam = $(elem).attr('use-param');
										if (useParam){
											var parameter = $('parameter[name=' + useParam + ']');
											$('parameter[name=' + useParam + '] variable').each(function(i, childElement){
												var elementName = $(childElement).attr('name');
												var elemChildren = $(elem).find(elementName);
												if (elemChildren.length){
													$(elemChildren[0]).html($(childElement).attr('value'));
												}
												else{
													$(elem).append('<' + elementName + '>' + $(childElement).attr('value') + '</' + elementName + '>');
												}
											})
										}
										var action = $(elem).attr('action').toLocaleLowerCase();
										var performAction = require('./' + action + '.js');
										performAction['processapi'](req, res, $.html(elem))
										.then(function(r){
											//success
										})
										.catch(function(e){
											//To do: Need to check if the current trigger is also a flow and trigger this event
											// logger.error(`Workflow process failed at ${action}, Current Stage: ${req.body.currStage}`, { reqData: extractReqData(req, ["currStage"]), err: e, fileName: "workflow", action: action });
										})
										}
									})
									// on success, post the result back to /workflow so that it can handle the next sequence of events
									var cmsVersion = req.session.site.version;
									
									
									var urlToPost = (req.secure ? 'https://' : 'http://') + req.headers.host + cms.config[cmsVersion].wfm.wfURL + '?currStage=' + currAction
									var paData = req.body;
									paData.info = info;
									paData.status = {code: 200, message: "data received"};
									axios.post(urlToPost, paData).then(()=>{}).catch((error) => {
										logger.error('Error in workflow update request', { error, url: urlToPost });
									});
									resolve(info);
									return true;
								})
								.catch(function (e) {
									logger.error(`Workflow process failed at ${action}, Current Stage: ${req.body.currStage}`, { reqData: extractReqData(req, ["currStage"]), err: e, fileName: "workflow", action: action });									
									var errMsg = e;
									if (e && e.status && e.status.message){
										errMsg = e.status.message
									}else if(e && e.message){
										errMsg = e.message
									}

									// on failure, post the result back to /workflow so that it can handle the next sequence of events
									var cmsVersion = req.session.site.version;
									
									var urlToPost = (req.secure ? 'https://' : 'http://') +  req.headers.host + cms.config[cmsVersion].wfm.wfURL + '?currStage=' + currAction
									var paData = req.body;
									paData.status = {code: 500, message: "data received"};

									axios.post(urlToPost, paData).then(()=>{}).catch((error) => {
										logger.error('Error in workflow update request', { error, url: urlToPost });
									});
									//If the error response has resolve true then resolve else reject - Jagan	
									if(!e || !e.status || !e.status.resolve || e.status.resolve != true){
										reject({
											status: {code: 500, message: errMsg},
											step: e.step
										});
									}else{
										resolve(e);
									}
									return false;
								});
						}
						catch (e) {
							logger.error(`requested action (${action}) is not available. Unexpected input`, { reqData: extractReqData(req), err: e, fileName: "workflow" });
							reject({
								status: {code: 500, message: "requested action (" + action + ") is not available. Unexpected input"},
								step: "perform trigger action based on workflow template",
								error: e
							});
							return false;
						}
					});
				})
				.catch(function (e) {
					var updatelog = true;
					if (e && e.status && e.status.message){
						errorStatement = e.status.message;
						if (e.status.updatelog && e.status.updatelog == 'false'){
							updatelog = false;
						}
					}
					reject({
						status: {code: 500, message: errorStatement},
						step: "perform trigger action based on workflow template",
						error: e
					});
					if (updatelog){
						logger.error("Unable to fetch workflowTemplate", { reqData: extractReqData(req), err: e, fileName: "workflow" });
					}
					return false;
				});
		});
	},
	// Recursive function to process triggers synchoronously
	processParentSyncTrigger: async function (triggerElem, req, res) {
		let action;
		try {
			const $ = cheerio.load(triggerElem, {recognizeSelfClosing: true});
			action = $(triggerElem).attr('action').toLowerCase();
			const useParam = $(triggerElem).attr('use-param');
			const performAction = require('./' + action + '.js');

			if (useParam) {
				$('parameter[name=' + useParam + '] variable').each(function (i, childElement) {
					const elementName = $(childElement).attr('name');
					const elemChildren = $(triggerElem).find(elementName);
					if (elemChildren.length) {
						$(elemChildren[0]).html($(childElement).attr('value'));
					} else {
						$(triggerElem).append(
							'<' + elementName + '>' + $(childElement).attr('value') + '</' + elementName + '>'
						);
					}
				});
			}

			await performAction['processapi'](req, res, $.html(triggerElem));

		} catch (e) {
			logger.error(`Workflow process failed at ${action}, Current Stage: ${req.body.currStage}`, { reqData: extractReqData(req, ["currStage"]), err: e, fileName: "workflow", action: action });
		}
		finally {
             // Process children triggers
			const $ = cheerio.load(triggerElem, {recognizeSelfClosing: true});
			const childTriggers = $(triggerElem).find('trigger');
			for (let i = 0; i < childTriggers.length; i++) {
				try {
					module.exports.processParentSyncTrigger(childTriggers[i], req, res);
				} catch (error) {
					// Errors
				}
			}
		}
	},
	/*
	* get the workflow xml from exist-db using XQL. resolve with xml if success, reject with message on failure
	* @param {string} customer - string to identify customer. e.g., bmj, bir, elife, mbs, frontiers, ...
	* @param {string} project - project name. If journals then it would be abbreviations like elife, jgv, ijsem, heartjrnl, ...
	* @param {string} currStage - current stage where the article is in. parseEmail, addJob, preEdit, ...
	*/
	getWorkflowXML: function (req, res) {
		return new Promise(function(resolve, reject) {
			getProjects(req)
				.then(function(response){
					var options = {
						ignoreComment: true,
						alwaysChildren: true,
						compact: true
					};
					
					var updatedIssueXML = xmlJS.json2xml(response, options);
					updatedIssueXML = updatedIssueXML.replace(/[\r\n]+[ \t]*/g, '');

					getArticle(req, res)
					.then(async function(xmlData){
							var params = req.query.project ? req.query : req.body;
							updatedIssueXML = '<projects>' + updatedIssueXML + '</projects>';
							var pl = libxmljs.parseXml(updatedIssueXML);
							var projectNode = pl.find('//projects[./name[.="' + params.project + '"]]');
							if (projectNode.length == 0) {
								reject({
									status: {
										code: '400',
										message: 'Project not found in project list'
									},
									step: "Get workflow data"
								});
								return false;
							}
							projectNode = projectNode[0];
							var wfTemplatePath = projectNode.find('.//workflowTemplate');
							if (wfTemplatePath.length > 0){
								wfTemplatePath = wfTemplatePath[0].text();
							}else{
								wfTemplatePath = "";
							}
							if ((req.body && req.body.workflowType && req.body.workflowType == 'milestone') || (req.query && req.query.workflowType && req.query.workflowType == 'milestone')) {
								wfTemplatePath = '/default/milestoneWorkflowTemplate.xml';
							}
							if (wfTemplatePath == ""){
								reject({
									status: {
										code: '400',
										message: 'workflowTemplate not found in project list '
									},
									step: "Get workflow data"
								});
								return false;
							}
							let cmsVersion = 'v3.0'
							if(req && req.session && req.session.site && req.session.site.version){
								cmsVersion = req.session.site.version;
							}
							var jsonConfig = getPeerReviewConfig(cmsVersion,params.customer)
							wfTemplatePath = path.resolve(process.env.CONFIG_PATH + '/db/kriyadocs/customers/' + params.customer + '/config' + wfTemplatePath);
							if (! fs.existsSync(wfTemplatePath)){
								reject({
									status: {
										code: '400',
										message: 'JobTemplate path not found:'+ wfTemplatePath
									},
									step: "Get workflow data"
								});
								return false;
							}
							if (req && req.headers && req.headers.kuser && req.headers.kuser.kuemail){
								req.body.currentUserEmail = req.headers.kuser.kuemail;
								req.body.currentUserFirstName = req.headers.kuser.kuname.first;
								req.body.currentUserLastName = req.headers.kuser.kuname.last;
							}
							//added by Anuraja to replace variables in workflowTemplate							
							var regex = /\{(.*?)\}/ig;
							if (xmlData){
								var xmlDoc = libxmljs.parseXml(xmlData);
								var pawdoi = xmlDoc.find('//article//custom-meta[.//meta-name[.="Paw-doi"]]');
								var articleType = xmlDoc.find('//article-categories/subj-group[@subj-group-type="display-channel"]/subject');
								if (articleType.length > 0){
									req.body.articleType  = articleType[0].text();
								}
								var fastTrack = xmlDoc.find('//article/workflow[priority="on"]');
								if (fastTrack.length > 0){
									req.body.fasttrack  = true;
								}
								var resubmission = xmlDoc.find(`//custom-meta[@data-type="resubmission"][@data-version="${req.body.currentVersion}"]`);
								if (resubmission.length > 0){
									req.body.resubmission  = true;
								}

								var authorEmailFirstName = xmlDoc.find('//contrib-group/contrib[@data-author-email="'+req.body.currentUserEmail+'"]/name/given-names')
								if(authorEmailFirstName.length > 0){
									req.body.currentUserFirstName = authorEmailFirstName[0].text()
								}
								var authorEmailLastName = xmlDoc.find('//contrib-group/contrib[@data-author-email="'+req.body.currentUserEmail+'"]/name/surname')
								if(authorEmailLastName.length > 0){
									req.body.currentUserLastName = authorEmailLastName[0].text()
								}
								//added by Anuraja to get version number
								var originalVersion = xmlDoc.find('//article-meta//article-version[contains(@article-version-type, "Original")]');
								var otherVersion = xmlDoc.find('//article-meta//article-version[not(contains(@article-version-type, "Original"))]');
								var articleVersion = '';
								var articleversionDisplay = '';
								if(!originalVersion || originalVersion.length == 0){
									articleVersion = "Original";
									articleversionDisplay = "Original";
								}else if(otherVersion){
									articleVersion = "R" + (otherVersion.length +1);
									articleversionDisplay = "Revision" + (otherVersion.length +1)
								}
								req.body.currentVersion = articleVersion;
								req.body.articleversionDisplay = articleversionDisplay;
								if(pawdoi.length > 0){
									req.body.pawdoi = pawdoi[0].attr('data-doi').value();
									req.body.pawproject = pawdoi[0].attr('data-project').value()
								}
							}
							var bodyData = req.body;
							var templateData = fs.readFileSync(wfTemplatePath, "utf8");
							templateData = templateData.replace(/[\r\n]+[ \t]*/g, '');
							templateData = templateData.replace(/\{project\}/g, params.project);
							var result;
							while (result = regex.exec(templateData)) {
								var varName = result[1];
								var replaceReg = new RegExp('{' + varName + '}', 'g');
								if (bodyData[varName]) {
									templateData = templateData.replace(replaceReg, bodyData[varName].replace(/\& /, '&amp; '));
								} else if (bodyData.mailInfo && bodyData.mailInfo[varName]) {
									templateData = templateData.replace(replaceReg, bodyData.mailInfo[varName]);
								} else if (bodyData.status && bodyData.status[varName]) {
									templateData = templateData.replace(replaceReg, bodyData.status[varName]);
								}
							}

							var wfDoc = libxmljs.parseXml(templateData);
							// Remove double blind elements when given in peerreview_config
							if(jsonConfig && (jsonConfig['doubleblind']==="true"|| (jsonConfig.journal_doubleblind && jsonConfig.journal_doubleblind[req.query.project] ==="true"))){
								var doubleBlindNodes = wfDoc.find('//p[@data-check-blind="doubleblind"]');
								for (var m = 0, ml = doubleBlindNodes.length; m < ml; m++){
									doubleBlindNodes[m].remove();
								}
							}
							var workflows = wfDoc.find('//variable[@populate-from="workflow"]');
							for (var n = 0, nl = workflows.length; n < nl; n++){
								var node = workflows[n];
								var r = node.attr('xpath').value();
								var flownodes = wfDoc.find(r);
								if(flownodes){
									for (var m = 0, ml = flownodes.length; m < ml; m++){
										var flownode = libxmljs.parseXmlString(flownodes[m].toString()).root();
										node.addPrevSibling(flownode);
									}
								}
								node.remove();
							}

							//Add the date in workflow email template
							//This date is including saturday and sunday also and increase/decrease count is configurable
							//Default date is MMMM DD, YYYY
							//Date format also configurable - Jagan
							var addDatesNodes = wfDoc.find('//span[@data-add-date="true"]');
							req.body.project = req.body.project ? req.body.project :req.query.project;
							for(var a=0;a<addDatesNodes.length;a++){
								var dataNode = addDatesNodes[a];
								var count = dataNode.attr('data-count').value();
								let dynamicSla = dataNode.attr('data-condition-sla');
								if(count && dynamicSla  && dynamicSla.value() =="true"){
									let overDueConfig = await getReviewerDueDateByCustomer({customer: params.customer, project: params.project, doi: params.doi, type: 'ACCEPT', articleData: req.body})
									count = overDueConfig.totalDays ? overDueConfig.totalDays : count
									count = parseInt(count);
									dataNode.attr('data-count').value(count);
								}
								var dateFormat = dataNode.attr('data-date-format').value();
								dateFormat = (!dateFormat)?"MMMM DD, YYYY":dateFormat;
								//var dateStr = moment().add(count, 'd').format(dateFormat);
								// changed to calculate business days - Jai
								if(dataNode.attr('consecutive-days') && dataNode.attr('consecutive-days').value() == 'true'){//Added by Anuraja to update consecutive days based on attrib
									var day= moment();
									var dateStr = day.add(count, 'd').utc().format(dateFormat);	
								}else{
								var dateStr = module.exports.addBusinessDay(count, 'd', moment().utc().format('YYYY-MM-DD HH:mm:ss'), false).format(dateFormat);
								}
								if(req.body.duedate && req.body.duedate != ''){
									let timestamp = parseInt(req.body.duedate);
									if(!isNaN(timestamp) && moment(timestamp).isValid()){
										let formattedDate = moment(timestamp).format(dateFormat);
										dateStr = formattedDate;
									}
								}
								dataNode.text(dateStr);

							}


							var dates = wfDoc.find('//variable[@populate-data="date"]');
							for (var n = 0, nl = dates.length; n < nl; n++){
								var time = dates[n].attr('count').value();
								time = moment().utc().businessAdd(time, 'd').toDate();
								time = moment(time).utc().format('YYYY-MM-DD');
								var businessDay = moment(time).isBusinessDay();
								if (!businessDay) {
									let nextBusinessDay = moment(time).nextBusinessDay();
									time = nextBusinessDay;
								}
								var replaceNode = dates[n].node('span');
								replaceNode.text(moment(time).format('DD-MM-YYYY'));
								replaceNode.attr('class', dates[n].name())
								dates[n].addPrevSibling(replaceNode)
								dates[n].remove();
							}
							var flow = wfDoc.find('//flow[@name="' + req.query.currStage + '"]');
							if (flow.length == 0){
								reject({
									status: {
										code: '400',
										message: req.query.currStage + ' not found in workflow',
										'updatelog': 'false'
									},
									step: "Get workflow data"
								});
								return false;
							}
							if (xmlData){
								/*var xmlDoc = libxmljs.parseXml(xmlData);
								var pawdoi = xmlDoc.find('//article//custom-meta[.//meta-name[.="Paw-doi"]]');
								if(pawdoi.length > 0){
									req.body.pawdoi = pawdoi[0].attr('data-doi').value();
									req.body.pawproject = pawdoi[0].attr('data-project').value()
								}*/
								//Anuraja	to handle same trigger with different values in a loop (example trigger same email template for all reviewers separately)			
								//Updated by Anuraja to loop second level variables first and then firts level variable	to send all reviewers comments to all reviewers
								var forLoopArray = ['.//variable[not(.//variable[@data-type="for-loop"])][@data-type="for-loop"]', './/variable[.//variable][@data-type="for-loop"]'];
								for (var forLoopXpath of forLoopArray){
									var loopNodes = flow[0].find(forLoopXpath);
									for (var n = 0, nl = loopNodes.length; n < nl; n++){
										var loopElement = './trigger';
										if(loopNodes[n].attr('data-loop-element')){
											loopElement = loopNodes[n].attr('data-loop-element').value();
										}
										if(loopNodes[n].find(loopElement).length > 0 ){
											var variableNode = loopNodes[n];										
											var xpath = variableNode.attr('xpath').value();
											var xNode = xmlDoc.find(xpath);																			
											for (var x = 0, xl = xNode.length; x < xl; x++){
												var node = loopNodes[n].find(loopElement)[0].clone().toString();											
												var tempParam = {};
												var varNodes = variableNode.find('.//variable[.//variable][@populate-from="element"][@data-type="tempvariable"]|.//variable[not(.//variable)][@populate-from="element"][@data-type="tempvariable"]');											
												tempParam['elementIndex'] = (x+1).toString();
												if (varNodes.length > 0){
													for (var s = 0, sl = varNodes.length; s < sl; s++){
														var varNode = varNodes[s];
														var childXpath = varNode.attr('xpath').value();
														if (varNode.attr('name') != null){
															var varName = varNode.attr('name').value();
															var childNodes = xNode[x].find(childXpath);
															var nodeContent = '';
															for (var c = 0, cl = childNodes.length; c < cl; c++){
																nodeContent += childNodes[c].text();
															}
															tempParam[varName] = nodeContent;														
														}													
													}	
												}
												while (result = regex.exec(node)) {
													var curVarName = result[1];
													var replaceReg = new RegExp('{' + curVarName + '}', 'g');
													if (tempParam[curVarName]) {
														node = node.replace(replaceReg, tempParam[curVarName].replace(/\& /, '&amp; '));
													}
												}
												var node = libxmljs.parseXml(node).root();
												if (x < xl-1 && loopNodes[n].attr('inter-punc') != null){
													var spanNodeTemp = new libxmljs.Element(xmlDoc,'span')
													if(spanNodeTemp){
														spanNodeTemp.text(loopNodes[n].attr('inter-punc').value())
														node.addChild(spanNodeTemp);
														spanNodeTemp.remove()
													}
												}
												var varNodes = node.find('.//variable[.//variable][@populate-from="element"]|.//variable[not(.//variable)][@populate-from="element"]');
												for (var v = 0, vl = varNodes.length; v < vl; v++){
													var varNode = varNodes[v];
													var childXpath = varNode.attr('xpath').value();
													var childNodes = xNode[x].find(childXpath);
													if (req.query.currProcess && req.query.currProcess == 'signoff' && varNode.attr('data-display-label')){
														var replaceNode = flow[0].node('span');
														var nodeContent = "";
														replaceNode.text(varNode.attr('data-display-label').value());
														replaceNode.attr('class', xNode[x].name());
														replaceNode.attr('data-type', 'data-display-label');
														varNode.addPrevSibling(replaceNode);
														varNode.remove();
													}else{
														for (var c = 0, cl = childNodes.length; c < cl; c++){
															var replaceNodeName = 'span'
															var replaceNode = flow[0].node(replaceNodeName);
															if (varNode.attr('data-display-label')){
																replaceNode.attr('data-type', 'data-display-label');
															}
															var nodeContent = "";
															var subNodes = varNode.find('.//variable');
															if (subNodes.length > 0){
																for (var s = 0, sl = subNodes.length; s < sl; s++){
																	var subNode = subNodes[s];
																	if (subNode.attr('start-punc') != null){
																		nodeContent += subNode.attr('start-punc').value();
																	}
																	var childXpath = subNode.attr('xpath').value();
																	var subChildNodes = childNodes[c].find(childXpath);
																	for (var sc = 0, scl = subChildNodes.length; sc < scl; sc++){
																		nodeContent += subChildNodes[sc].text();
																	}
																	if (subNode.attr('end-punc') != null){
																		nodeContent += subNode.attr('end-punc').value();
																	}
																}	
															}else if(childNodes[c] && childNodes[c].type && childNodes[c].type() == "attribute"){
																nodeContent += childNodes[c].value();
															}else{
																nodeContent += childNodes[c].text();
															}
															if (node.attr('end-punc') != null){
																nodeContent += node.attr('end-punc').value();
															}
															replaceNode.text(nodeContent)
															replaceNode.attr('class', childNodes[c].name())
															varNode.addPrevSibling(replaceNode)
														}
														varNode.remove();
													}
												}	
												variableNode.addPrevSibling(node);
											}									
											variableNode.remove();
										}
									}								
								}
								//Anuraja - End loop process
								var nodes = flow[0].find('.//*[@xpath]');
								for (var n = 0, nl = nodes.length; n < nl; n++){
									var node = nodes[n];
									r = node.attr('xpath').value();
								}
								var nodes = flow[0].find('.//*[@condition-xpath]');
								for (var n = 0, nl = nodes.length; n < nl; n++){
									var node = nodes[n];
									var xpaths = node.attr('condition-xpath').value();
									xpaths = xpaths.replace(/] or /g, ']|');
									xpaths = xpaths.split(/ and (?![a-zA-Z])/);
									var validXpath = true;
									for (var x = 0, xl = xpaths.length; x < xl; x++){
										var xNode = xmlDoc.find(xpaths[x]);
										if (xNode.length == 0){
											validXpath = false;
										}
									}
									if (validXpath){
										if (node.attr('condition-count') != null && node.attr('condition-count').value() == "false"){
											node.remove();
										}
									}else{
										if (!(node.attr('condition-count') != null && node.attr('condition-count').value() == "false")){
											node.remove();
										}
									}
								}
								
								var correctionPDFs = xmlDoc.find("//note[.//content[.//p[@data-reason='correction-pdf']]][last()]//span[@class='comment-file']//a[@href]");
								if(correctionPDFs.length >0){
									req.body.correctionPDF = correctionPDFs[0].attr('href').value();
								}

								// add custom function and use that in the workflow - Jai 24-Aug-2021
								// things that cant be done directly using the xpath can be added as fucntion
								// added getPrevStage function 
								var nodes = flow[0].find('.//*[@condition-function]');
								for (var n = 0, nl = nodes.length; n < nl; n++){
									var node = nodes[n];
									var functionValues = node.attr('condition-function').value();
									functionValues = functionValues.split(/,\s/);
									var functionName = functionValues[0];
									var functionParams = false;
									if (functionValues.length > 1){
										functionParams = functionValues[1];
									}
									var validXpath = true;
									if (functionName && typeof module.exports[functionName] === 'function'){
										validXpath = await module.exports[functionName](xmlDoc,...functionValues.slice(1), req, node);
									}

									if (validXpath){
										if (node.attr('condition-count') != null && node.attr('condition-count').value() == "false"){
											node.remove();
										}
										if (node.attr('set-as-node-text') != null && node.attr('set-as-node-text').value() == "true"){
											node.text(validXpath);
										}
										if(node.attr('set-as-node-html') != null && node.attr('set-as-node-html').value() == "true"){
											let tempNode = libxmljs.parseXml(validXpath).get(`/*`)
											node.addNextSibling(tempNode);
											node.remove();
										}
									}else{
										if (!(node.attr('condition-count') != null && node.attr('condition-count').value() == "false")){
											node.remove();
										}
									}
								}

								//Get comments from prev email content 
								var commentNodes = flow[0].find('.//*[@data-comment-xpath][.=""]');
								for (var n = 0, nl = commentNodes.length; n < nl; n++){
									var commentPath = commentNodes[n].attr('data-comment-xpath').value();
									var commentXNodes = xmlDoc.find(commentPath);
									for (var x = 0, xl = commentXNodes.length; x < xl; x++){
										var replaceNode = commentNodes[n].node('p');
										replaceNode.text('Comments:');
										replaceNode.attr('style','margin: 0px;font-size: 14px;font-weight: 600;');
										commentNodes[n].addPrevSibling(replaceNode)
										var flownode = libxmljs.parseXmlString(commentXNodes[x].toString()).root();
										commentNodes[n].addPrevSibling(flownode)
									}
									commentNodes[n].remove()
								}

								var nodes = flow[0].find('.//variable[.//variable]|.//variable[not(.//variable)]');
								for (var n = 0, nl = nodes.length; n < nl; n++){
									var node = nodes[n];
									if(!(node.attr('xpath'))){
										continue;
									}
									var xpath = node.attr('xpath').value();
									if (node.attr('populate-from') != null && node.attr('populate-from').value() == 'config'){
										var xNode = projectNode.find(xpath)
									}else{
										var xNode = xmlDoc.find(xpath);
									}
									if(node.attr('data-display') != null && node.attr('data-display').value() == 'count' && xNode.length > 0){//updated by Anuraja to display count instead of value 
										var replaceNodeName = 'span'
										if (node.attr('wrap-with') != null){
											replaceNodeName = node.attr('wrap-with').value();
										}					
										var replaceNode = flow[0].node(replaceNodeName);					
										var nodeContent = "";
										if (x > 0 && node.attr('inter-punc') != null){
											nodeContent += node.attr('inter-punc').value();
										}
										if (node.attr('start-punc') != null){
											nodeContent += node.attr('start-punc').value();
										}
										nodeContent += xNode.length;
										if (node.attr('end-punc') != null){
											nodeContent += node.attr('end-punc').value();
										}
										replaceNode.text(nodeContent)
										replaceNode.attr('class', xNode[0].name())
										node.addPrevSibling(replaceNode)
									}else{
									for (var x = 0, xl = xNode.length; x < xl; x++){
										
										//Remove the deleted contents from xNode - Jagan
										if(xNode[x] && xNode[x].type && xNode[x].type() != "attribute"){
											//Updated by Anuraja to remove child deleted nodes also
											var removeNodes = xNode[x].find('.//named-content[contains(@content-type, "del") or contains(@data-track, "del")]|.//*[@hidden-track="track"]');
											for(var rm = 0; rm < removeNodes.length; rm++){
												removeNodes[rm].remove();
											}
										}
                    //Added by anuraja to wrap text into tag for comments
										var replaceNodeName = 'span'
										if (node.attr('wrap-with') != null){
											replaceNodeName = node.attr('wrap-with').value();
										}					
										var replaceNode = flow[0].node(replaceNodeName);					
										var nodeContent = "";
										if (x > 0 && node.attr('inter-punc') != null){
											nodeContent += node.attr('inter-punc').value();
										}
										if (node.attr('start-punc') != null){
											nodeContent += node.attr('start-punc').value();
										}
										var subNodes = node.find('.//variable');
										if (subNodes.length > 0){
											for (var s = 0, sl = subNodes.length; s < sl; s++){
												var subNode = subNodes[s];
												if (subNode.attr('start-punc') != null){
													nodeContent += subNode.attr('start-punc').value();
												}
												var childXpath = subNode.attr('xpath').value();
												var childNodes = xNode[x].find(childXpath);
												for (var c = 0, cl = childNodes.length; c < cl; c++){
													nodeContent += childNodes[c].text();
												}
												if (subNode.attr('end-punc') != null){
													nodeContent += subNode.attr('end-punc').value();
												}
											}	
										}else if(xNode[x] && xNode[x].type && xNode[x].type() == "attribute"){
											nodeContent += xNode[x].value();
										}else if(node.attr('set-as-html') != null && node.attr('set-as-html').value() == "true"){
											nodeContent += xNode[x].toString();
										}else{
											nodeContent += xNode[x].text();
										}
										if (node.attr('end-punc') != null){
											nodeContent += node.attr('end-punc').value();
										}
										// to set variable data as html using set-as-html attribute from workflow
										if(node.attr('set-as-html') != null && node.attr('set-as-html').value() == "true"){
											let newNode = libxmljs.parseXmlString(nodeContent).root();
											replaceNode.addChild(newNode);
										}else{
											replaceNode.text(nodeContent)
										}
										replaceNode.attr('class', xNode[x].name())
										node.addPrevSibling(replaceNode)
										}
									}
									node.remove();
								}
							}
							var paramNodes = flow[0].find('.//trigger[@use-param]');
							var paramArray = [];
							var paramData = '';
							for (var r = 0, rl = paramNodes.length; r < rl; r++){
								var paramName = paramNodes[r].attr('use-param').value();
								if (paramArray.indexOf(paramName) < 0){
									paramArray.push(paramName);
									var parameters = wfDoc.find('//parameter[@name="' + paramName + '"]');
									for (var p = 0, pnl = parameters.length; p < pnl; p++){
										paramData += parameters[p].toString();
									}
								}
							}							
							var response = '<response><workflowData><workflows>' + flow[0].toString() + paramData + '</workflows></workflowData><projectConfig>' + projectNode.toString() + '</projectConfig></response>';
							resolve(response);
						})
						.catch(function(err){
							if (typeof(err) == 'object') err = JSON.stringify(err);
							reject({
								status: {
									code: '400',
									message: err
								},
								step: "Get workflow data"
							})
						})
				})
				.catch(function(err){
					reject({
						status: {
							code: '400',
							message: 'Unable to get project list'
						},
						step: "perform trigger action based on workflow template"
					});
				})
		});
	},
	// get previous stage from where the article was signed off - Jai 24-Aug-2021
	// ignore stages like add job, file download, convert files, support, hold
	// used for handling resubmission on peer review
	// when article was sent from Manging editor stage or Handling editor stage, it needs to be sent back to the respective stages
	getPrevStage: function(xmlDoc, functionParams){
		var workflowNode = xmlDoc.find("//workflow");
		if (workflowNode.length == 0) return false;
		var lastStage = workflowNode[0].find('./stage[last()]');
		if (lastStage.length == 0) return false;
		var currentstageName = lastStage[0].find('./name')[0].text();
		var prevStage = lastStage[0].find('./preceding-sibling::stage');
		var lastStageNameMatches = false;
		for (var p = prevStage.length - 1; p >= 0; p--){
			var stageName = prevStage[p].find('./name')[0].text();
			var stagesRegex = '^(add\\s?Job|File\\s?download|Convert\\s?Files|Support|hold|Structure\\s?content|Author\\s?resubmission|Author\\s?revision|Author\\s?review|'+currentstageName+')$';
			stagesRegex = new RegExp(stagesRegex, 'i')
			if (stagesRegex.test(stageName)) {
				continue;
			}
			if (stageName == functionParams){
				lastStageNameMatches = true;
				break;
			}else{
				break;
			}
		}
		return lastStageNameMatches;
	},
	// get Current stage from where the article - Vijayakumar 03-11-2023
	getCurrentStage: function(xmlDoc, functionParams){
		var workflowNode = xmlDoc.find("//workflow");
		if (workflowNode.length == 0) return false;
		var lastStage = workflowNode[0].find('./stage[last()]');
		if (lastStage.length == 0) return false;
		var currentstageName = lastStage[0].find('./name')[0].text();
		var stagesRegex = '^(add\\s?Job|File\\s?download|Convert\\s?Files|Support|hold|Structure\\s?content)$';
		stagesRegex = new RegExp(stagesRegex, 'i')
		var lastStageNameMatches = false;
		if (stagesRegex.test(currentstageName)) {
			var prevStage = lastStage[0].find('./preceding-sibling::stage');
			for (var p = prevStage.length - 1; p >= 0; p--){
				var stageName = prevStage[p].find('./name')[0].text();
				if (stagesRegex.test(stageName)) {
					continue;
				}
				if (stageName == functionParams){
					lastStageNameMatches = true;
					break;
				}else{
					break;
				}
			}
		}else{
			if (currentstageName == functionParams){
				lastStageNameMatches = true;
			}
		}
		return lastStageNameMatches;
	},
	// To check the review status and trigger the action added by vijayakumar murugan 31-10-2023
	getReviewerStatus: function(xmlDoc, count, req, node){
		var reviewerStatus = false;
		var conditionPath = ""
		if(count == 'all'){
			conditionPath = "//contrib-group[@data-group='reviewer'][count(.//contrib[@data-version='{currentVersion}']) = count(.//contrib[@data-version='{currentVersion}'][@data-reviewer-status='completed' or @data-reviewer-status='terminated' or @data-reviewer-status='rejected'  or @data-reviewer-status='standby'])]"
		}else{
			count = count.trim()
			conditionPath = "//contrib-group[@data-group='reviewer'][count(.//contrib[@data-version='{currentVersion}'])>"+count+"][count(.//contrib[@data-version='{currentVersion}'][@data-reviewer-status='completed'])="+count+"][count(.//contrib[@data-version='{currentVersion}']) != count(.//contrib[@data-version='{currentVersion}'][@data-reviewer-status='completed' or @data-reviewer-status='terminated' or @data-reviewer-status='rejected'  or @data-reviewer-status='standby'])]"
		}
		conditionPath = strformat(conditionPath, req.body);
		var workflowNode = xmlDoc.find(conditionPath);
		if(typeof(workflowNode) != 'undefined' && workflowNode.length >0){
			reviewerStatus = true;
		}
		return reviewerStatus;
	},
	getReviewerStausAndMoveToDescisionStage : function(xmlDoc, req, node){
    var reviewerStatus = false;
    var currentVersion = req.body.currentVersion;

    var reviewerInvolvedCount = `//contrib-group[@data-group='reviewer']//contrib[@data-version='${currentVersion}']`;
    var reviewerStatusRejectCount = `.//contrib[@contrib-type='reviewer'][@data-version='${currentVersion}'][@data-reviewer-status='terminated' or @data-reviewer-status='rejected'  or @data-reviewer-status='standby']`;
    var reviewerStatusCompletedCount = `.//contrib[@contrib-type='reviewer'][@data-version='${currentVersion}'][@data-reviewer-status='completed']`;
   
    // Find nodes matching the conditionPath
    var involvedNodes = xmlDoc.find(reviewerInvolvedCount);
    var completedNodes = xmlDoc.find(reviewerStatusCompletedCount);
    var rejectNodes = xmlDoc.find(reviewerStatusRejectCount);

    // Check if condition involved reviewe count should match with decision maken reviewer and atleat one should completed 
    if (involvedNodes.length == (completedNodes.length + rejectNodes.length) && completedNodes.length > 0) {
        reviewerStatus = true;
    }
    return reviewerStatus;
	},

	//To get editor xpath bsed on role
	getEditorDetails: function(xmlDoc, editorRole, Xpath, req, node){
		editorRole = editorRole?.split('|');
		let editorStatus = false;
		for(const role of editorRole) {
			let editorNode = xmlDoc.find('//contrib-group[@data-contrib-name="'+role+'"]//contrib[not(@data-reviewer-status="terminated")][last()]')
			if(editorNode.length > 0){
				let editorPath = editorNode[0].path();
				node.attr('xpath', editorPath+Xpath);
				editorStatus = true;
				break;
			}
		}
		if(!editorStatus) {
			node.remove();
		}
		return editorStatus;
	},
	addBusinessDay: function(number, period, time, excludeDay) {
			var day = moment(time);
			var splittedExcludeDay = (excludeDay == false && typeof(excludeDay) != "string")?[]:excludeDay.split(',');
			if (!day.isValid()) {
				return moment(day);
			}
			if (number < 0) {
				number = Math.round(-1 * number) * -1;
			} else {
				number = Math.round(number);
			}
			var signal = number < 0 ? -1 : 1;
			period = typeof period !== 'undefined' ? period : 'days';
			var remaining = Math.abs(number);
			while (remaining > 0) {
				day.add(signal, period);
				//If excludeDay is false then check isBusinessDay
				//If excludeDay is none then don't skip any days
				//Skip the days which is in splittedExcludeDay
				if (((excludeDay == false && day.isBusinessDay()) || (excludeDay == 'none')) || (splittedExcludeDay.length > 0 && splittedExcludeDay.indexOf(day.day().toString()) < 0)) {
					remaining--;
				}
			}
			return day;
	},
	getSupportAssigneeEmail: function(xmlDoc){
		var lastStageEmail = xmlDoc.find("//workflow/stage[last()]/assigned/to");
		if (lastStageEmail.length == 0) return false;
		var stageEmail = lastStageEmail[0].text();
		let email = stageEmail.replace(/(\s|[\,\;]$)/g, '').split(/\s?[\,\;]\s?/)
		return email[1];
	},
	getSupportAssigneeName: function(xmlDoc){
		var lastStageDetail = xmlDoc.find("//workflow/stage[last()]/assigned/to");
		if (lastStageDetail.length == 0) return {name: "" };;
		var stageDetail = lastStageDetail[0].text();
		let assignedUser = stageDetail.replace(/[,;]+$/, '').split(/[;,]+/)
		return assignedUser[0];
	},
	//To get current stage assingnee mailid and avoid support stages
	getCurrentAssigneeEmail: function(xmlDoc){
		var lastStageEmail = xmlDoc.find("//workflow/stage[not(./name[.='Support'])][last()]/assigned/to");
		if (lastStageEmail.length == 0) return false;
		var stageEmail = lastStageEmail[0].text();
		let email = stageEmail.replace(/(\s|[\,\;]$)/g, '').split(/\s?[\,\;]\s?/)
		return email[1];
	},
	//To get current stage assingnee name and avoid support stages
	getCurrentAssigneeName: function(xmlDoc){
		var lastStageDetail = xmlDoc.find("//workflow/stage[not(./name[.='Support'])][last()]/assigned/to");
		if (lastStageDetail.length == 0) return {name: "" };;
		var stageDetail = lastStageDetail[0].text();
		let assignedUser = stageDetail.replace(/[,;]+$/, '').split(/[;,]+/)
		return assignedUser[0];
	},	
	//To get desired stage assingnee name
	getStageAssigneeName: function(xmlDoc, stageName){
		let getPreviousStage = xmlDoc.find(`//workflow/stage[./name[.='${stageName}']][last()]/assigned/to`);
		if (getPreviousStage.length == 0) return {name: "" };
		let prevStageAssignee = getPreviousStage[0].text();
		let assigneeName = prevStageAssignee.replace(/[,;]+$/, '').split(/[;,]+/);
		return assigneeName[0];
	},
	getStageAssigneeEmail: function(xmlDoc, stageName){
		let getPreviousStage = xmlDoc.find(`//workflow/stage[./name[.='${stageName}']][last()]/assigned/to`);
		if (getPreviousStage.length == 0) return {name: "" };
		let prevStageAssignee = getPreviousStage[0].text();
		let assigneeEmail = prevStageAssignee.replace(/(\s|[\,\;]$)/g, '').split(/\s?[\,\;]\s?/);
		return assigneeEmail[1];
	},
	getAssigneeNameByDepth: function(xmlDoc, functionParams){
		let getPreviousStage = xmlDoc.find(`//workflow/stage[last()-${functionParams}]/assigned/to`);
		if (getPreviousStage.length == 0) return {name: "" };
		let prevStageAssigne = getPreviousStage[0].text();
		let assigneName = prevStageAssigne.replace(/[,;]+$/, '').split(/[;,]+/);
		return assigneName[0];
	},
	getAssigneeEmailByDepth: function(xmlDoc, functionParams){
		let getPreviousStage = xmlDoc.find(`//workflow/stage[last()-${functionParams}]/assigned/to`);
		if (getPreviousStage.length == 0) return false;
		let prevStageAssigne = getPreviousStage[0].text();
		let assigneEmail = prevStageAssigne.replace(/(\s|[\,\;]$)/g, '').split(/\s?[\,\;]\s?/)
		return assigneEmail[1];
	},
	validateTransfer: async function(xmlDoc, req) {
		try {
			const xmlData = await getArticle(req, {});
			if (xmlData) {
				const xmlDoc = libxmljs.parseXml(xmlData);
				let transferToList = {};
				const transferedJrnls = xmlDoc.find(
					"//custom-meta-group//custom-meta[@journal-prefrences-type='multiple'][./meta-name[.='journal-preferences']]/meta-value/named-content[@data-journal-abbr][@data-transferred='true']"
				);
				if (req && req.session && req.session.site && req.session.site.version && req.query && req.query.project && req.query.customer) {
					const jsonConfig = getPeerReviewConfig(req.session.site.version, req.query.customer);
					transferToList = jsonConfig['jrnlTransferMatrix'][req.query.project];
					for (const journal of transferedJrnls) {
						if (journal.attr("data-journal-abbr") && journal.attr("data-journal-abbr").value() && transferToList.includes(journal.attr("data-journal-abbr").value())) {
							const index = transferToList.indexOf(journal.attr("data-journal-abbr").value());
							if (index > -1) {
								transferToList.splice(index, 1);
							}
						}
					}
				}
				if (transferToList && Object.keys(transferToList).length > 0) {
					return true;
				}
			}
			return false;
		} catch (e) {
			return false;
		}
	},

    getSubmittingAuthorDetails: function(xmlDoc, type, subType) {
		const primaryXpath = "//front//contrib-group[@data-group='submitting-author']/contrib[@contrib-type='submitting-author']";
        const fallbackXpath = "//front//contrib-group/contrib[@contrib-type='author'][@corresp='yes']";

        function getText(xpath) {
            let node = xmlDoc.find(xpath);
            return node.length > 0 ? node.map(n => n.text()).join(", ") : false;
        }
    
        function getFullName(xpath, subType) {
            let node = xmlDoc.find(xpath);
            if (node.length > 0) {
				let nodeTexts = []
				for (let i = 0; i < node.length; i++) {
					if (typeof subType === 'string' && subType.trim() !== '' ){
						let fullName = node[i].get(subType)?.text()
						nodeTexts.push(fullName)
					} else{
						let given = node[i].get("given-names")?.text();
						let surname = node[i].get("surname")?.text();
						let fullName = `${given ? given : ''} ${surname? surname : ""}`;
						nodeTexts.push(fullName);
					}
				}
				return nodeTexts.length > 0 ? nodeTexts.join(", ") : false;
            }
            return false;
        }
		let submittingAuthorNode = xmlDoc.find(primaryXpath)
        const selectedNode = submittingAuthorNode.length > 0 ? primaryXpath : fallbackXpath;
		switch (type) {
			case '/email':
				return getText(selectedNode + type);

			case '/name':
				return getFullName(selectedNode + type, subType);
			default:
				return false;
		}
    },
	getFormattedDate: function(xmlDoc, date, format, targetXpath, req){
		let formattedDate;
		let studDateDetail = targetXpath? xmlDoc.get(targetXpath) :  xmlDoc.get(`//article/contrib-group/contrib[@data-author-email='${req.body.currentUserEmail}']/stub-content/p[@${date}]`);
		if (studDateDetail){
			var dateValue;
			if(studDateDetail.type() == 'attribute'){
				dateValue = Number(studDateDetail.value());
			}else{
				dateValue =targetXpath? Number(studDateDetail.text()) : Number(studDateDetail.attr(date).value());
			}
			if (dateValue) {
				if(format =='none'){
					formattedDate = moment(dateValue).format('MMMM DD, YYYY')
				} else {
					formattedDate = moment(dateValue).format(format)
				}
			}
		}
		return formattedDate;
	},
	getSuggestedTitle: function (xmlDoc, req){
		let suggestedTopic;
		let userMailID = req.body.userMailID || req.query.userMailID
		if (userMailID ){
			let authorSubTopic = xmlDoc.get(`//contrib[@data-author-email='${userMailID}']/stub-content/p[@data-subtopic]`);
			if (authorSubTopic){
				suggestedTopic = authorSubTopic.attr('data-subtopic').value()
			}
		}
		return suggestedTopic;
	},
	getReviewerDueDate: function(xmlDoc, dateFormat, req){
		if (!(req && req.query && req.query.version)){
			req.query.version = req.body.articleversionDisplay
		}
		if (!(req && req.query && req.query.revieweremail)){
			req.query.revieweremail = req.body.currentUserEmail
		}
		let reviewerAcceptNode = xmlDoc.get(`//contrib-group[@data-group="reviewer"]/contrib[@data-version="${req.query.version}"][./email[.="${req.query.revieweremail}"]]/fn/p[.="accepted"][@data-planned-end-date]`);
		if(!reviewerAcceptNode){
			if(dateFormat == 'none') return true
			return false
		}
		var plannedDueDate = Number(reviewerAcceptNode.attr('data-planned-end-date').value());
		if(isNaN(plannedDueDate)){
			if(dateFormat == 'none') return true
			return false
		}
		let formattedDate = dateFormat ? dateFormat.replace(/\|\|/g, ',') : "MMMM DD, YYYY";
		plannedDueDate = moment(plannedDueDate).utcOffset(330)
		if(!plannedDueDate._isValid){
			if(dateFormat == 'none') return true
			return false
		}
		if(dateFormat && (["after","before","none"].includes(dateFormat))){
			let now = moment();
			if(now.isAfter(plannedDueDate,'day') && dateFormat == "before"){
				return true
			}
			else if((now.isBefore(plannedDueDate,'day')  || now.isSame(plannedDueDate,'day')) && dateFormat == "after"){
				return true
			}
			return false
		}
		else{
			return plannedDueDate.format(formattedDate);
		}
	},
	getReviewerExtendedDueDate: function(xmlDoc,text,dateFormat, req) {
		if (req.body.duedate != undefined ) {
			return `${text} ${moment(parseInt(req.body.duedate)).format(dateFormat ? dateFormat.replace(/\|\|/g, ',') : "MMMM DD, YYYY")}`
		} else {
			return false
		}
	},
	getEditorialQuestions: function(xmlDoc, req){
		try{
			var customerXmlPath = path.resolve('./cms/' + req.session.site.version + '/js/peerreview_editor/config/' + req.query.customer + '/peerreview_config.xml');
			if(!fs.existsSync(customerXmlPath)){
				return ''
			}
			var customXml = cheerio.load(fs.readFileSync(customerXmlPath).toString(), {ignoreWhitespace: true, xmlMode: true});
			var peereRevXml = customXml("peer-review-config")
			var workflowNode = xmlDoc.find("//workflow");
			if (workflowNode.length == 0) return false;
			var lastStage = workflowNode[0].find('./stage[last()]');
			if (lastStage.length == 0) return false;
			var currentstageName = lastStage[0].find('./name')[0].text().replace(/\s+/g, '').toLocaleLowerCase();
			var questionsPanel = peereRevXml.find(`#editorQuestionsContentDiv div#questionsPanel div.card[data-editable-stage*="${currentstageName}"] div.ques-container`);
			var constructedHtml = '<div class="editorialQuestions">';
			// var questionCounter = 1;
			for (let i = 0; i < questionsPanel.length; i++){
				var quesName = questionsPanel[i].attribs['data-ques-name'];
				// check the child nodes of the question panel which has data-name="question"
				var question = peereRevXml.find(`#editorQuestionsContentDiv div[data-ques-name="${quesName}"] div[data-name="question"]`)
				var answer = xmlDoc.get(`//custom-meta-group//custom-meta[@specific-use="question"][meta-name="${quesName}"]/meta-value`)
				if(answer){
					if(question){
						// if(question.find('span.displayNumber').length > 0){
						// 	question.find('span.displayNumber').text(questionCounter + '.');
						// 	questionCounter++;
						// }
						question = question.text().trim();
						constructedHtml = constructedHtml + '<p><b>'+question+'</b></p>';
					}
					answer = answer.text().trim();
					constructedHtml = constructedHtml + '<p>'+answer+'</p>';
				}
			}
			return constructedHtml+'</div>';
		}
		catch(e){
			return ''
		}
	},	
	reviewerSlaDueDate: function(xmlDoc, req){
		var cmsVersion  = req.session.site.version;
		var customerJson = path.resolve('./cms/' + cmsVersion + '/js/submission/config/' + req.query.customer + '/peerreview_config.json');
		if(!fs.existsSync(customerJson)){
			customerJson = path.resolve('./cms/' + cmsVersion + '/js/submission/config/default/peerreview_config.json');
		}
		var jsonConfig = JSON.parse(fs.readFileSync(customerJson).toString());
		if (jsonConfig &&jsonConfig.reviewer_sla && jsonConfig.reviewer_sla.default_due_days && jsonConfig.reviewer_sla.sla_conditions.length>0){
	
			let bestMatchDueDays = jsonConfig.reviewer_sla.default_due_days;
			let highestConditionCount = 0;
			for (const config of jsonConfig.reviewer_sla.sla_conditions) {
				const { conditions, days } = config;
				if (allConditionsMatch(conditions, req.body)) {
					let exceptConditionCount = conditions.except ? Object.keys(conditions.except).length : 0;
					const conditionCount = Object.keys(conditions).filter(key => key !== 'except').length + exceptConditionCount;
					if (conditionCount > highestConditionCount) {
						bestMatchDueDays = days;
						highestConditionCount = conditionCount;
					}
				}
			}
			return bestMatchDueDays;
		}
		return false
	}
}

module.exports = postsFunction;