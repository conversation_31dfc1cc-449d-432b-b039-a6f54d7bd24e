// Required modules
const axios = require('axios');
const libxmljs = require("libxmljs");            // For XML and XSD parsing/validation
const fs = require("fs");                         // For reading XSD file from the filesystem
const updatechangesmapping = require("../post/updatechangesmapping");
const loggerReport = require("../post/loggerreport");

/**
 * Added by <PERSON><PERSON><PERSON><PERSON> to validate the raw XML against the XSD.
 *
 * Sample Input:
 * {
 *   "apiKey": "",
 *   "customer": "",
 *   "project": "",
 *   "doi": ""
 * }
 *
 * Output:
 *   Returns a JSON array containing schema validation errors, if any.
 */

// Function to fetch XML content via GET request
async function getXML(url, req) {
    try {
        const response = await axios.get(url, { headers: { 'cookie': req.headers.cookie } });
        return response;
    } catch (err) {
        throw err;
    }
}

module.exports = {
    // Exported function to handle XML validation
    validatexml: async function (req, res) {
        try{
            let { customer, project, doi, apiKey, xmlContent,triggeredFrom } = req.body;

            // Validate presence of required parameters
            if (!customer || !project || !doi) {
                return res.status(400).json({                   // Respond with 400 if any required field is missing
                    status: {
                        code: 400,
                        message: 'Missing required parameters: customer, project, or doi.'
                    }
                });
            }
    
            // Construct base URL depending on whether request is secure (https) or not (http)
            const baseUrl = (req.secure ? 'https://' : 'http://') + req.headers.host;
    
            // Construct the API URL to fetch raw XML
            let url = `${baseUrl}/api/getxml?doi=${doi}&project=${project}&customer=${customer}&xmltype=raw`;
    
            // Append apiKey to URL if present in query or body
            if (req.query?.apiKey) {
                url += `&apiKey=${req.query.apiKey}`;           // Append from query string
            } else if (apiKey) {
                url += `&apiKey=${apiKey}`;                     // Append from request body
            }
            
            if(!xmlContent){
                xmlContent = await getXML(url, req);
                xmlContent = xmlContent.data;
            }
    
            xmlContent = xmlContent.replace(/></g, '>\n<');
    
            const xsdContent = fs.readFileSync('cms/v3.0/xslt/kriyaSchema/schema.xsd', 'utf8');
    
            // Parse XSD and XML content using libxmljs
            const xsdDoc = libxmljs.parseXml(xsdContent, { baseUrl: "cms/v3.0/xslt/kriyaSchema/" });   // Parse XSD
            const xmlDoc = libxmljs.parseXml(xmlContent);   // Parse XML
            const isValid = xmlDoc.validate(xsdDoc); 
            if(isValid){
                return res.status(200).json({
                    status: "success",
                    message: "XML is valid",
                    errors: []
                });
            }

            const xmlLines = xmlContent.split(/\r?\n/);
            const errorInfos = [];                      // Initialize array to store errors
    
            // If validation fails, collect and return all errors
            for (let error of xmlDoc.validationErrors) {
                const lineText = xmlLines[error.line - 1] || "";  // Extract line where error occurred
                errorInfos.push({
                    ErrorMsg: error.message,            // Error message
                    code: error.code,                   // Error code
                    column: error.column,               // Column number
                    domain: error.domain,               // Domain of the error
                    level: error.level,                 // Error severity level
                    line: error.line,                   // Line number
                    Element: lineText                   // Full line content for context
                });
            }

            loggerAnalysis(errorInfos, triggeredFrom, 'SchemaValidationLogs', req,res);
    
            res.status(200).json({
                status: "failed",
                message: "Validation Failed",
                errors: errorInfos
            });
        }catch(error){
            logger.error('xmlValidation', {reqData: extractReqData(req), err: error, fileName: "validatexml" });
            res.json({"error": error.message});
            res.end();
        }
    }
};

async function loggerAnalysis(schemaError, triggeredFrom, folderPath, req,res) {
    try {
        const { customer, project, doi } = req.body;

        const fileName = triggeredFrom
            ? `${customer}-${project}-${doi}-${triggeredFrom}-XMLValidationErrors-${Date.now()}.txt`
            : `${customer}-${project}-${doi}-XMLValidationErrors-${Date.now()}.txt`;

        req.body.fileName = fileName;
        req.body.folderPath = folderPath;
        req.body.data = schemaError;

        let updatechangesmappingResponse = await updatechangesmapping.processapi(req,res);
        let errorCount = schemaError.length;
        let logger = {
            customer,
            project,
            doi,
            errors: updatechangesmappingResponse.url,
            autoResolved: 'no',
            totalErrors: errorCount,
            totalAutoresolved: 0,
            timeStamp: Date.now().toString()
        };

        req.body.data = logger;
        req.body.table = "schemalogs";

        let loggerReportResponse = await loggerReport.processapi(req,res);
        return loggerReportResponse;
    } catch (err) {
        console.error("LoggerAnalysis failed:", err.message, err.response?.data || err);
    }
}
