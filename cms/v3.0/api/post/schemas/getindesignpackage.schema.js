const getindesignpackageBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        packageType: { type: 'string', enum: ['full', 'text-only', 'images-only'] },
        includeAssets: { type: 'boolean', default: true },
        compressionLevel: { type: 'number', default: 6 },
        outputFormat: { type: 'string', enum: ['idml', 'indd', 'zip'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "InDesign package created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "packagePath": { "type": "string" },
                "packageSize": { "type": "number" },
                "includedFiles": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Failed to create InDesign package",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create InDesign package" }
            }
        }
    }
};

module.exports = {
    tags: 'Publishing',
    description: "Create InDesign package for typesetting with all required assets",
    summary: "Get InDesign package",
    body: getindesignpackageBody,
    response
};