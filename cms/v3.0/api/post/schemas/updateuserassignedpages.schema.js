const updateuserassignedpagesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        assignedPages: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    pageNumber: { type: 'number' },
                    pageType: { type: 'string' },
                    status: { type: 'string', enum: ['assigned', 'in-progress', 'completed'] },
                    assignedDate: { type: 'string', format: 'date-time' },
                    dueDate: { type: 'string', format: 'date-time' }
                }
            }
        },
        role: { type: 'string' },
        updateType: { type: 'string', enum: ['assign', 'update', 'complete'] }
    },
    required: ['customer', 'project', 'doi', 'email', 'assignedPages'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User assigned pages updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedPages": { "type": "number" },
                "assignedTo": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "User assigned pages update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update pages assigned to users with status tracking",
    summary: "Update user assigned pages",
    body: updateuserassignedpagesBody,
    response
};