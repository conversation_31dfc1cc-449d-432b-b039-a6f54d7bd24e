const createchaserschedulerBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        chaserType: { type: 'string', enum: ['author', 'reviewer', 'editor'] },
        reminderDays: { type: 'array', items: { type: 'number' } },
        emailTemplate: { type: 'string' },
        escalationLevel: { type: 'number', default: 1 }
    },
    required: ['customer', 'project', 'doi', 'chaserType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Chaser scheduler created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "schedulerId": { "type": "string" },
                "nextReminderDate": { "type": "string" },
                "workingDaysCalculated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Failed to create chaser scheduler",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid chaser configuration" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Create automated chaser scheduler for authors, reviewers, or editors with working day calculations",
    summary: "Create chaser scheduler",
    body: createchaserschedulerBody,
    response
};