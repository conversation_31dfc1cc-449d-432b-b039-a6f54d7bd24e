const getlatexfileBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        conversionType: { type: 'string', enum: ['tex-to-xml', 'xml-to-tex'] },
        includeImages: { type: 'boolean', default: true },
        outputFormat: { type: 'string', enum: ['zip', 'tar', 'individual'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "LaTeX file processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "outputPath": { "type": "string" },
                "conversionLog": { "type": "string" },
                "includedFiles": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "LaTeX processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "LaTeX conversion failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Process LaTeX files for conversion to XML or other formats",
    summary: "Get LaTeX file",
    body: getlatexfileBody,
    response
};