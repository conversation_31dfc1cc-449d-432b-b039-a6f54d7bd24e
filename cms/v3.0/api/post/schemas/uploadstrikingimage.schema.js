const uploadstrikingimagebody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        strikingImage: {
            type: 'string',
            format: 'binary',
            description: 'Striking image file for the article'
        },
        imageCaption: { type: 'string' },
        imageCredits: { type: 'string' },
        imagePosition: { type: 'string', enum: ['cover', 'header', 'featured'] },
        replaceExisting: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'strikingImage'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Striking image uploaded successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "imageUrl": { "type": "string" },
                "imageId": { "type": "string" },
                "uploadedAt": { "type": "string", "format": "date-time" }
            }
        }
    },
    "400": {
        "description": "Striking image upload failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Upload striking image for article with caption and positioning options",
    summary: "Upload striking image",
    body: uploadstrikingimagebody,
    response,
    consumes: ['multipart/form-data']
};