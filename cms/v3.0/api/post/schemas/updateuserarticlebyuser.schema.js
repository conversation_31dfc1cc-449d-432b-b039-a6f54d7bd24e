const updateuserarticlebyuserBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        elasticID: { type: 'string' },
        role: { type: 'string' },
        status: { type: 'string' },
        assignedDate: { type: 'string', format: 'date-time' },
        completedDate: { type: 'string', format: 'date-time' },
        userComments: { type: 'string' },
        updateFields: { type: 'object' }
    },
    required: ['customer', 'project', 'doi', 'email'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User article updated by user successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "elasticResponse": { "type": "object" },
                "uniqueId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "User article update by user failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update user-article relationship by user with elastic ID handling",
    summary: "Update user article by user",
    body: updateuserarticlebyuserBody,
    response
};