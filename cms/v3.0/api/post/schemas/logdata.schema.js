const logdataBody = {
    type: 'object',
    properties: {
        logType: { type: 'string', minLength: 1 },
        logMessage: { type: 'string', minLength: 1 },
        logName: { type: 'string' },
        logStatus: { type: 'string', enum: ['info', 'warn', 'error', 'debug'] },
        customer: { type: 'string' },
        project: { type: 'string' },
        doi: { type: 'string' },
        addDate: { type: 'string' }
    },
    required: ['logType', 'logMessage'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Log data recorded successfully",
        "schema": {
            "type": "object",
            "properties": {
                "logType": { "type": "string" },
                "logMessage": { "type": "string" },
                "logName": { "type": "string" },
                "logStatus": { "type": "string" },
                "timestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Log data recording failed",
        "schema": {
            "type": "object",
            "properties": {
                "code": { "type": "number", "example": 400 },
                "error": { "type": "string", "example": "logType is required" }
            }
        }
    }
};

module.exports = {
    tags: 'Logging',
    description: "Record log data with different types and status levels for system monitoring",
    summary: "Log data",
    body: logdataBody,
    response
};