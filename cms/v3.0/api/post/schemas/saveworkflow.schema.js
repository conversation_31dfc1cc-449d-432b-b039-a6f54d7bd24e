const saveworkflowBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        workflowData: { type: 'object', minLength: 1 },
        workflowType: { type: 'string', enum: ['submission', 'review', 'production', 'publication'] },
        stage: { type: 'string' },
        status: { type: 'string' },
        validateWorkflow: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'workflowData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Workflow saved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "workflowId": { "type": "string" },
                "stage": { "type": "string" },
                "validationResults": { "type": "object" },
                "saveTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Workflow save failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Save workflow data with validation and stage management",
    summary: "Save workflow",
    body: saveworkflowBody,
    response
};