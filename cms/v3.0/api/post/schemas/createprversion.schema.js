const createprversionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        html: { type: 'string' },
        versionType: { type: 'string', enum: ['snapshot', 'milestone', 'backup'] },
        versionNote: { type: 'string' },
        createSnapshot: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "PR version created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "versionId": { "type": "string" },
                "snapshotCreated": { "type": "boolean" },
                "transformationComplete": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Failed to create PR version",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create version or snapshot" }
            }
        }
    }
};

module.exports = {
    tags: 'Version Management',
    description: "Create PR version with snapshot creation and XSLT transformation",
    summary: "Create PR version",
    body: createprversionBody,
    response
};