const updatepretranformBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        filePath: { type: 'string' },
        transformConfig: {
            type: 'object',
            properties: {
                inputFormat: { type: 'string' },
                outputFormat: { type: 'string' },
                preserveFormatting: { type: 'boolean', default: true },
                validateOutput: { type: 'boolean', default: true }
            }
        },
        batchProcess: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Pre-transform completed successfully",
        "schema": {
            "type": "string",
            "example": "files updated"
        }
    },
    "400": {
        "description": "Pre-transform failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Update and pre-transform files with format conversion",
    summary: "Update pre-transform",
    body: updatepretranformBody,
    response
};