const getinvoiceBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        invoiceId: { type: 'string' },
        doi: { type: 'string' },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        status: { type: 'string', enum: ['pending', 'paid', 'overdue'] },
        includeDetails: { type: 'boolean', default: true }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Invoice data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "invoices": { "type": "array" },
                "totalAmount": { "type": "number" },
                "currency": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve invoice data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve invoice" }
            }
        }
    }
};

module.exports = {
    tags: 'Financial',
    description: "Retrieve invoice information and billing details",
    summary: "Get invoice data",
    body: getinvoiceBody,
    response
};