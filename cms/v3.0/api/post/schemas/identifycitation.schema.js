const identifycitationBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        citationText: { type: 'string' },
        autoLink: { type: 'boolean', default: true },
        validateDOI: { type: 'boolean', default: true },
        includePubMed: { type: 'boolean', default: true },
        format: { type: 'string', enum: ['json', 'xml'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Citations identified successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "citations": { "type": "array" },
                "identifiedCount": { "type": "number" },
                "linkedCount": { "type": "number" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Citation identification failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to identify citations" }
            }
        }
    }
};

module.exports = {
    tags: 'Reference Management',
    description: "Identify and validate citations with DOI and PubMed linking",
    summary: "Identify citations",
    body: identifycitationBody,
    response
};