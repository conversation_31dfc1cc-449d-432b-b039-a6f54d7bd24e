const updatebinderdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        binderData: { type: 'object', minLength: 1 },
        updateType: { type: 'string', enum: ['full', 'partial', 'metadata'] },
        validateData: { type: 'boolean', default: true },
        createBackup: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'binderData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Binder data updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedFields": { "type": "array" },
                "backupCreated": { "type": "boolean" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Binder data update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Update binder data with validation and backup options",
    summary: "Update binder data",
    body: updatebinderdataBody,
    response
};