const getplagiarismdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reportType: { type: 'string', enum: ['summary', 'detailed', 'sources'] },
        threshold: { type: 'number', default: 10 },
        includeExcluded: { type: 'boolean', default: false },
        format: { type: 'string', enum: ['json', 'xml', 'pdf'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Plagiarism data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "plagiarismScore": { "type": "number" },
                "sources": { "type": "array" },
                "reportUrl": { "type": "string" },
                "lastChecked": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve plagiarism data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve plagiarism data" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Retrieve plagiarism check results and similarity reports",
    summary: "Get plagiarism data",
    body: getplagiarismdataBody,
    response
};