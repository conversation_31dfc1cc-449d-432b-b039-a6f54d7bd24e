const getproofprefixBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        proofType: { type: 'string', enum: ['galley', 'page', 'final'] },
        includeHistory: { type: 'boolean', default: false },
        format: { type: 'string', enum: ['json', 'xml'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Proof prefix retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "proofPrefix": { "type": "string" },
                "proofNumber": { "type": "string" },
                "proofHistory": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve proof prefix",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve proof prefix" }
            }
        }
    }
};

module.exports = {
    tags: 'Publishing',
    description: "Get proof prefix and numbering for article proofing process",
    summary: "Get proof prefix",
    body: getproofprefixBody,
    response
};