const autocontentstructuringBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string' },
        structureType: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Content structured automatically",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "structuredContent": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Automatically structure article content",
    summary: "Auto structure content",
    body: autocontentstructuringBody,
    response
};