const transformhtmlBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        htmlContent: { type: 'string', minLength: 1 },
        transformationType: { type: 'string', enum: ['clean', 'structure', 'validate', 'convert'] },
        outputFormat: { type: 'string', enum: ['html', 'xml', 'markdown'], default: 'html' },
        preserveStyles: { type: 'boolean', default: true },
        removeScripts: { type: 'boolean', default: true },
        validateOutput: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'htmlContent', 'transformationType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "HTML transformation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "transformedContent": { "type": "string" },
                "outputFormat": { "type": "string" },
                "validationResults": { "type": "object" },
                "transformationLog": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "HTML transformation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Transform HTML content with cleaning, structuring, and validation options",
    summary: "Transform HTML",
    body: transformhtmlBody,
    response
};