const labelBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        labelType: { type: 'string', enum: ['category', 'status', 'priority', 'custom'] },
        labelValue: { type: 'string', minLength: 1 },
        action: { type: 'string', enum: ['add', 'remove', 'update'], default: 'add' },
        color: { type: 'string' },
        description: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'labelType', 'labelValue'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Label operation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "labelId": { "type": "string" },
                "action": { "type": "string" },
                "currentLabels": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Label operation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process label" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Add, remove, or update labels for articles with categorization and status tracking",
    summary: "Manage labels",
    body: labelBody,
    response
};