const grammercheckBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        checkType: { type: 'string', enum: ['grammar', 'spelling', 'style', 'all'] },
        language: { type: 'string', default: 'en' },
        autoCorrect: { type: 'boolean', default: false },
        includeContext: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Grammar check completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "errors": { "type": "array" },
                "suggestions": { "type": "array" },
                "errorCount": { "type": "number" },
                "correctedText": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Grammar check failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Grammar check failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Perform grammar, spelling, and style checks on article content",
    summary: "Grammar check",
    body: grammercheckBody,
    response
};