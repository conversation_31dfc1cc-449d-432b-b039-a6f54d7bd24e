const addstageBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        stageName: { type: 'string', minLength: 1 },
        workflowType: { 
            type: 'string', 
            enum: ['milestone', 'stub'],
            description: 'Type of workflow for stage addition'
        },
        resupply: { 
            type: 'string', 
            enum: ['true', 'false'],
            description: 'Flag to indicate resupply operation'
        },
        releaseHold: { 
            type: 'boolean',
            description: 'Flag to release article from hold'
        },
        supportComment: {
            type: 'object',
            properties: {
                content: { type: 'string', minLength: 1 },
                parent: { type: 'string' },
                fullname: { type: 'string' }
            },
            description: 'Support comment data'
        },
        notesData: {
            type: 'object',
            description: 'Notes data to be saved with stage addition'
        },
        removeAcceptDate: {
            type: 'string',
            enum: ['true', 'false'],
            description: 'Flag to remove accept date from article'
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Stage added successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "number",
                            "example": 200
                        },
                        "message": {
                            "type": "string",
                            "example": "Stage added successfully"
                        }
                    }
                }
            }
        }
    },
    "400": {
        "description": "Bad Request - Invalid parameters or operation failed",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "string",
                            "example": "400"
                        },
                        "message": {
                            "type": "string",
                            "examples": [
                                "Could not fetch data from database",
                                "Project not found",
                                "Stage template not found",
                                "Could not update data to database"
                            ]
                        }
                    }
                },
                "step": {
                    "type": "string",
                    "example": "add stage"
                }
            }
        }
    },
    "500": {
        "description": "Server Error - Missing required parameters",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": {
                            "type": "number",
                            "example": 500
                        },
                        "message": {
                            "type": "string",
                            "example": "One or more of required parameters (customer id, project id, current stage) is/are not provided. requested action on stage cannot be done. Unexpected input"
                        }
                    }
                }
            }
        }
    }
};

module.exports = {
    public: true,
    tags: 'Workflow',
    description: "This endpoint adds a new stage to an article's workflow. It supports different workflow types including milestone and stub workflows, handles hold/release operations, and manages stage transitions with proper validation and logging.",
    summary: "Add a new stage to article workflow",
    body: addstageBody,
    response
};