const updaterorBody = {
    type: 'object',
    properties: {
        'funding-registry': {
            type: 'string',
            format: 'binary',
            description: 'RDF file containing ROR registry data'
        },
        cmsVersion: { type: 'string', default: 'v3.0' },
        batchSize: { type: 'number', default: 100 },
        maxRetries: { type: 'number', default: 2 }
    },
    required: ['funding-registry'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "ROR update initiated successfully",
        "schema": {
            "type": "string",
            "example": "Elastic update for ROR initiated. Check these following emails for the update"
        }
    },
    "400": {
        "description": "ROR update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Update ROR (Research Organization Registry) data to Elasticsearch from registry file",
    summary: "Update ROR",
    body: updaterorBody,
    response,
    consumes: ['multipart/form-data']
};