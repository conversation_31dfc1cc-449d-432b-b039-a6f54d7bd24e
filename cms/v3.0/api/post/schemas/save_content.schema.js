const save_contentBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string', minLength: 1 },
        contentType: { type: 'string', enum: ['html', 'xml', 'text'] },
        section: { type: 'string' },
        createVersion: { type: 'boolean', default: false },
        validateContent: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Content saved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "contentId": { "type": "string" },
                "versionCreated": { "type": "boolean" },
                "validationResults": { "type": "object" },
                "saveTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Content save failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Save article content with validation and optional version creation",
    summary: "Save content",
    body: save_contentBody,
    response
};