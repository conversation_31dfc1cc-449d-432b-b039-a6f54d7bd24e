const generatekeyBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        keyType: { type: 'string', enum: ['api', 'access', 'encryption'] },
        keyLength: { type: 'number', default: 32 },
        expirationDays: { type: 'number' },
        permissions: { type: 'array', items: { type: 'string' } }
    },
    required: ['customer', 'project', 'doi', 'keyType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Key generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "key": { "type": "string" },
                "keyId": { "type": "string" },
                "expiresAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Key generation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate key" }
            }
        }
    }
};

module.exports = {
    tags: 'Security',
    description: "Generate API keys, access tokens, or encryption keys",
    summary: "Generate security key",
    body: generatekeyBody,
    response
};