const workflowBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        action: { type: 'string', minLength: 1 },
        stage: { type: 'string' },
        workflowType: { type: 'string', enum: ['milestone', 'stub'] },
        data: { type: 'object' }
    },
    required: ['customer', 'project', 'doi', 'action'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Workflow action completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Workflow action completed successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid workflow parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid workflow action or parameters" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow',
    description: "Execute workflow actions including stage transitions and workflow management",
    summary: "Execute workflow actions",
    body: workflowBody,
    response
};