const updateprojectlistBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        projects: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    projectId: { type: 'string' },
                    name: { type: 'string' },
                    status: { type: 'string', enum: ['active', 'inactive', 'archived'] },
                    description: { type: 'string' },
                    settings: { type: 'object' }
                },
                required: ['projectId', 'name']
            }
        },
        updateType: { type: 'string', enum: ['add', 'update', 'remove', 'bulk'] },
        validateProjects: { type: 'boolean', default: true }
    },
    required: ['customer', 'projects'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Project list updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedProjects": { "type": "number" },
                "addedProjects": { "type": "number" },
                "removedProjects": { "type": "number" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Project list update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Project Management',
    description: "Update project list with bulk operations and validation",
    summary: "Update project list",
    body: updateprojectlistBody,
    response
};