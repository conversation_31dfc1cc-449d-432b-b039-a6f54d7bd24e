const logerrorsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        errorType: { type: 'string', enum: ['system', 'validation', 'processing', 'network'] },
        errorMessage: { type: 'string', minLength: 1 },
        errorCode: { type: 'string' },
        stackTrace: { type: 'string' },
        severity: { type: 'string', enum: ['low', 'medium', 'high', 'critical'], default: 'medium' },
        context: { type: 'object' }
    },
    required: ['customer', 'project', 'doi', 'errorType', 'errorMessage'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Error logged successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "errorId": { "type": "string" },
                "timestamp": { "type": "string" },
                "severity": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Error logging failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to log error" }
            }
        }
    }
};

module.exports = {
    tags: 'Logging',
    description: "Log system errors with categorization and severity levels for debugging",
    summary: "Log errors",
    body: logerrorsBody,
    response
};