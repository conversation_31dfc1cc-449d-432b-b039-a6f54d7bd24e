const paginationBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        processType: { type: 'string', enum: ['InDesignSetter', 'ahformatter'] },
        pageSize: { type: 'string', enum: ['A4', 'Letter', 'Custom'] },
        startPage: { type: 'number', default: 1 },
        updatePageNumbers: { type: 'boolean', default: true },
        generateTOC: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'processType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Pagination processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "totalPages": { "type": "number" },
                "pageRange": { "type": "string" },
                "paginatedDocument": { "type": "string" },
                "tocGenerated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Pagination processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Pagination failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Process document pagination with page numbering and table of contents generation",
    summary: "Process pagination",
    body: paginationBody,
    response
};