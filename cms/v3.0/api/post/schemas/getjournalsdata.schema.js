const getjournalsdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        journalCode: { type: 'string' },
        includeMetrics: { type: 'boolean', default: false },
        includeIssues: { type: 'boolean', default: true },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Journal data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "journals": { "type": "array" },
                "metrics": { "type": "object" },
                "totalCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve journal data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve journal data" }
            }
        }
    }
};

module.exports = {
    tags: 'Journal Management',
    description: "Retrieve journal information, metrics, and issue data",
    summary: "Get journal data",
    body: getjournalsdataBody,
    response
};