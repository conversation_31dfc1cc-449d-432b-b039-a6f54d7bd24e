const updatedigestBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        type: { type: 'string', enum: ['decision', 'digest'] },
        content: { type: 'string', minLength: 1 }
    },
    required: ['customer', 'project', 'doi', 'type', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Digest updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Digest update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update digest or decision letter content via external API",
    summary: "Update digest",
    body: updatedigestBody,
    response
};