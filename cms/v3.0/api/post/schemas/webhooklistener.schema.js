const webhooklistenerBody = {
    type: 'object',
    properties: {
        eventType: { type: 'string', enum: ['SUBMISSION_COMPLETE', 'REPORT_COMPLETE', 'STATUS_UPDATE'] },
        status: { type: 'string', enum: ['COMPLETE', 'PROCESSING', 'FAILED'] },
        id: { type: 'string', minLength: 1 },
        timestamp: { type: 'string', format: 'date-time' },
        source: { type: 'string' },
        data: { type: 'object' },
        signature: { type: 'string' },
        retryCount: { type: 'number', default: 0 }
    },
    required: ['eventType', 'status', 'id'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Webhook processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Report generated!" },
                "processedAt": { "type": "string", "format": "date-time" },
                "eventId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Webhook processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" },
                "eventType": { "type": "string" },
                "retryable": { "type": "boolean" }
            }
        }
    }
};

module.exports = {
    tags: 'Integration',
    description: "Process webhook events for plagiarism reports and external system integrations",
    summary: "Webhook listener",
    body: webhooklistenerBody,
    response
};