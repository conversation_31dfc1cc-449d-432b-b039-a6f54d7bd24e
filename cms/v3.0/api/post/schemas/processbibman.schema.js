const processbibmanBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        bibmanData: { type: 'object' },
        processType: { type: 'string', enum: ['import', 'export', 'sync', 'validate'] },
        format: { type: 'string', enum: ['bibtex', 'endnote', 'ris', 'xml'] },
        validateEntries: { type: 'boolean', default: true },
        deduplicateEntries: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'processType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Bibliography manager data processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "processedEntries": { "type": "array" },
                "validationResults": { "type": "object" },
                "duplicatesRemoved": { "type": "number" },
                "exportedData": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Bibliography manager processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process bibliography data" }
            }
        }
    }
};

module.exports = {
    tags: 'Reference Management',
    description: "Process bibliography manager data with import/export, validation, and deduplication",
    summary: "Process bibliography manager",
    body: processbibmanBody,
    response
};