const supportmetricsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string' },
        metricType: { type: 'string', enum: ['performance', 'usage', 'error', 'custom'] },
        startDate: { type: 'string', format: 'date' },
        endDate: { type: 'string', format: 'date' },
        aggregation: { type: 'string', enum: ['daily', 'weekly', 'monthly'], default: 'daily' },
        includeDetails: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'metricType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Support metrics retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "metrics": { "type": "object" },
                "summary": { "type": "object" },
                "period": { "type": "string" },
                "generatedAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Support metrics retrieval failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Analytics',
    description: "Retrieve support metrics with various aggregation options",
    summary: "Get support metrics",
    body: supportmetricsBody,
    response
};