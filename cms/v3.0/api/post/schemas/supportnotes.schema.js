const supportnotesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string' },
        noteContent: { type: 'string', minLength: 1 },
        category: { type: 'string', enum: ['technical', 'editorial', 'production', 'general'] },
        priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' },
        assignedTo: { type: 'string' },
        tags: { type: 'array', items: { type: 'string' } },
        attachments: { type: 'array' }
    },
    required: ['customer', 'project', 'noteContent', 'category'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Support note created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "noteId": { "type": "string" },
                "ticketNumber": { "type": "string" },
                "assignedTo": { "type": "string" },
                "createdAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Support note creation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Support',
    description: "Create support notes with categorization and assignment",
    summary: "Create support note",
    body: supportnotesBody,
    response
};