const updatewordcountBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        wordCountData: {
            type: 'object',
            properties: {
                totalWords: { type: 'number' },
                abstractWords: { type: 'number' },
                bodyWords: { type: 'number' },
                referenceWords: { type: 'number' },
                figureCount: { type: 'number' },
                tableCount: { type: 'number' },
                equationCount: { type: 'number' }
            }
        },
        countType: { type: 'string', enum: ['automatic', 'manual', 'verified'] },
        updateElastic: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'wordCountData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Word count updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "totalWords": { "type": "number" },
                "countBreakdown": { "type": "object" },
                "elasticUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Word count update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update article word count with detailed breakdown and elastic integration",
    summary: "Update word count",
    body: updatewordcountBody,
    response
};