const updateuserimageBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        userimage: {
            type: 'string',
            format: 'binary',
            description: 'User image file (PNG/JPEG, max 50MB)'
        },
        imageType: { type: 'string', enum: ['profile', 'avatar', 'signature'] },
        replaceExisting: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'email', 'userimage'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User image updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "imageUrl": { "type": "string" },
                "imageSize": { "type": "number" },
                "uploadedAt": { "type": "string", "format": "date-time" }
            }
        }
    },
    "400": {
        "description": "User image update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" },
                "code": { "type": "string", "example": "LIMIT_FILE_TYPE" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update user profile image with file validation (PNG/JPEG, max 50MB)",
    summary: "Update user image",
    body: updateuserimageBody,
    response,
    consumes: ['multipart/form-data']
};