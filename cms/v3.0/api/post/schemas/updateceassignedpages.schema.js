const updateceassignedpagesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        assignedPages: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    pageNumber: { type: 'number' },
                    assignedTo: { type: 'string' },
                    status: { type: 'string' },
                    dueDate: { type: 'string' }
                }
            }
        },
        copyEditor: { type: 'string' },
        updateType: { type: 'string', enum: ['assign', 'reassign', 'complete'] }
    },
    required: ['customer', 'project', 'doi', 'assignedPages'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "CE assigned pages updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedPages": { "type": "number" },
                "assignedTo": { "type": "string" },
                "updateTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "CE assigned pages update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Copy Editing',
    description: "Update copy editor assigned pages with status and due dates",
    summary: "Update CE assigned pages",
    body: updateceassignedpagesBody,
    response
};