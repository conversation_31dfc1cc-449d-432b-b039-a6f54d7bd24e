const exportallarticlesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        issueId: { type: 'string', minLength: 1 },
        exportType: { type: 'string', enum: ['pdf', 'xml', 'package'] },
        proofType: { type: 'string', default: 'print_online' },
        processType: { type: 'string', default: 'InDesignSetter' },
        includeSupplementary: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'issueId'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "All articles exported successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "exportedCount": { "type": "number" },
                "packagePath": { "type": "string" },
                "processedArticles": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Export failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "No articles found to export" }
            }
        }
    }
};

module.exports = {
    tags: 'Export',
    description: "Export all articles from an issue to PDF or package format",
    summary: "Export all articles",
    body: exportallarticlesBody,
    response
};