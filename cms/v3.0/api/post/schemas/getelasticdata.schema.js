const getelasticdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        index: { type: 'string', minLength: 1 },
        query: { type: 'object' },
        size: { type: 'number', default: 100 },
        from: { type: 'number', default: 0 },
        sort: { type: 'array' },
        aggregations: { type: 'object' }
    },
    required: ['customer', 'project', 'index'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Elasticsearch data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "hits": { "type": "object" },
                "aggregations": { "type": "object" },
                "took": { "type": "number" },
                "timed_out": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve Elasticsearch data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to query Elasticsearch" }
            }
        }
    }
};

module.exports = {
    tags: 'Search & Indexing',
    description: "Query Elasticsearch for article and project data",
    summary: "Get Elasticsearch data",
    body: getelasticdataBody,
    response
};