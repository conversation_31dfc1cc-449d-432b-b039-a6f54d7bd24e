const pageredirectBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        redirectType: { type: 'string', enum: ['permanent', 'temporary', 'conditional'] },
        targetUrl: { type: 'string', minLength: 1 },
        conditions: { type: 'object' },
        preserveQuery: { type: 'boolean', default: true },
        statusCode: { type: 'number', enum: [301, 302, 307, 308], default: 302 }
    },
    required: ['customer', 'project', 'doi', 'redirectType', 'targetUrl'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Page redirect configured successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "redirectId": { "type": "string" },
                "sourceUrl": { "type": "string" },
                "targetUrl": { "type": "string" },
                "statusCode": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Page redirect configuration failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to configure redirect" }
            }
        }
    }
};

module.exports = {
    tags: 'Web Management',
    description: "Configure page redirects with different types and status codes for URL management",
    summary: "Configure page redirect",
    body: pageredirectBody,
    response
};