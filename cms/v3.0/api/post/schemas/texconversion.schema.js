const texconversionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        texContent: { type: 'string', minLength: 1 },
        outputFormat: { type: 'string', enum: ['html', 'xml', 'pdf'], default: 'html' },
        mathRenderer: { type: 'string', enum: ['mathjax', 'katex', 'mathml'], default: 'mathjax' },
        preserveComments: { type: 'boolean', default: false },
        includeBibliography: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'texContent'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "TeX conversion completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "convertedContent": { "type": "string" },
                "outputFormat": { "type": "string" },
                "mathEquations": { "type": "number" },
                "conversionLog": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "TeX conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Convert TeX/LaTeX content to various formats with math rendering",
    summary: "Convert TeX",
    body: texconversionBody,
    response
};