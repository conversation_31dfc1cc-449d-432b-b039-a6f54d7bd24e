const runahsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        ahsType: { type: 'string', enum: ['full', 'partial', 'validation'] },
        processingOptions: { type: 'object' },
        outputFormat: { type: 'string', enum: ['xml', 'html', 'pdf'], default: 'xml' },
        includeMetrics: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "AHS processing completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "processedContent": { "type": "string" },
                "processingMetrics": { "type": "object" },
                "outputPath": { "type": "string" },
                "processingTime": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "AHS processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'AI Processing',
    description: "Run AHS (Automated Hyperlinking System) processing with various output formats",
    summary: "Run AHS",
    body: runahsBody,
    response
};