const process_referenceBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        references: { type: 'array', items: { type: 'object' } },
        processType: { type: 'string', enum: ['validate', 'format', 'link', 'all'] },
        citationStyle: { type: 'string', enum: ['apa', 'mla', 'chicago', 'vancouver'] },
        validateDOI: { type: 'boolean', default: true },
        linkPubMed: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'references'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "References processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "processedReferences": { "type": "array" },
                "validationResults": { "type": "object" },
                "linkedCount": { "type": "number" },
                "formattedReferences": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Reference processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process references" }
            }
        }
    }
};

module.exports = {
    tags: 'Reference Management',
    description: "Process article references with validation, formatting, and linking to external databases",
    summary: "Process references",
    body: process_referenceBody,
    response
};