const updatepageinfoBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        pageInfo: {
            type: 'object',
            properties: {
                startPage: { type: 'number' },
                endPage: { type: 'number' },
                totalPages: { type: 'number' },
                pageRange: { type: 'string' },
                elocationId: { type: 'string' }
            }
        },
        updateType: { type: 'string', enum: ['pagination', 'elocation', 'both'] },
        validatePages: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'pageInfo'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Page information updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "pageRange": { "type": "string" },
                "totalPages": { "type": "number" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Page information update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update article page information with validation",
    summary: "Update page info",
    body: updatepageinfoBody,
    response
};