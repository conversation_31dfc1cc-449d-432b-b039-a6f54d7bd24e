const publishsocketmessageBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        messageType: { type: 'string', enum: ['notification', 'update', 'alert', 'broadcast'] },
        message: { type: 'string', minLength: 1 },
        recipients: { type: 'array', items: { type: 'string' } },
        priority: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' },
        persistent: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'messageType', 'message'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Socket message published successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "messageId": { "type": "string" },
                "recipientCount": { "type": "number" },
                "deliveryStatus": { "type": "object" },
                "timestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Socket message publishing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to publish message" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Publish real-time socket messages with priority and recipient targeting",
    summary: "Publish socket message",
    body: publishsocketmessageBody,
    response
};