const validatevolumeissuenumberBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        volume: { type: 'string', minLength: 1 },
        issue: { type: 'string', minLength: 1 },
        year: { type: 'number' },
        validateAgainstExisting: { type: 'boolean', default: true },
        checkSequence: { type: 'boolean', default: true },
        allowDuplicates: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'volume', 'issue'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Volume issue number validation completed",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "isValid": { "type": "boolean" },
                "validationResults": {
                    "type": "object",
                    "properties": {
                        "volumeValid": { "type": "boolean" },
                        "issueValid": { "type": "boolean" },
                        "sequenceValid": { "type": "boolean" },
                        "duplicateFound": { "type": "boolean" }
                    }
                },
                "suggestions": { "type": "array", "items": { "type": "string" } }
            }
        }
    },
    "400": {
        "description": "Volume issue number validation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" },
                "validationErrors": { "type": "array" }
            }
        }
    }
};

module.exports = {
    tags: 'Validation',
    description: "Validate volume and issue numbers against existing data with sequence checking",
    summary: "Validate volume issue number",
    body: validatevolumeissuenumberBody,
    response
};