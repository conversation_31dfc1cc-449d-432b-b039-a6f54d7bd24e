const updatemetaBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        metadata: {
            type: 'object',
            properties: {
                title: { type: 'string' },
                abstract: { type: 'string' },
                keywords: { type: 'array', items: { type: 'string' } },
                authors: { type: 'array' },
                affiliations: { type: 'array' },
                publicationDate: { type: 'string', format: 'date' },
                volume: { type: 'string' },
                issue: { type: 'string' },
                pages: { type: 'string' }
            }
        },
        updateType: { type: 'string', enum: ['full', 'partial', 'authors', 'bibliographic'] },
        validateMetadata: { type: 'boolean', default: true },
        updateCrossref: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'metadata'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Metadata updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedFields": { "type": "array" },
                "validationResults": { "type": "object" },
                "crossrefUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Metadata update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Metadata',
    description: "Update article metadata with validation and Crossref integration",
    summary: "Update metadata",
    body: updatemetaBody,
    response
};