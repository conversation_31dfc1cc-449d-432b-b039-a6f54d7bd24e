const executeactionsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        workflowXML: { type: 'string', minLength: 1 },
        actionType: { type: 'string' },
        variables: { type: 'object' },
        executeAll: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'workflowXML'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Actions executed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "executedActions": { "type": "array", "items": { "type": "object" } },
                "results": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Action execution failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "No triggeraction found in the workflow template" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Execute workflow actions based on trigger actions defined in workflow XML",
    summary: "Execute workflow actions",
    body: executeactionsBody,
    response
};