const contentstructuringBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string' },
        structureType: { type: 'string', enum: ['auto', 'manual', 'template'] },
        templateId: { type: 'string' },
        preserveFormatting: { type: 'boolean', default: true },
        validateStructure: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Content structured successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "structuredContent": { "type": "string" },
                "structureInfo": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Content structuring failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to structure content" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Structure article content using automated or template-based approaches",
    summary: "Structure article content",
    body: contentstructuringBody,
    response
};