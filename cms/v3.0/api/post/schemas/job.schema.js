const jobBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        jobType: { type: 'string', enum: ['processing', 'conversion', 'validation', 'export'] },
        priority: { type: 'string', enum: ['low', 'normal', 'high', 'urgent'], default: 'normal' },
        parameters: { type: 'object' },
        scheduledTime: { type: 'string' },
        retryCount: { type: 'number', default: 0 }
    },
    required: ['customer', 'project', 'doi', 'jobType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Job created/updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "jobId": { "type": "string" },
                "jobStatus": { "type": "string", "example": "queued" },
                "estimatedCompletion": { "type": "string" },
                "queuePosition": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Job creation/update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create job" }
            }
        }
    }
};

module.exports = {
    tags: 'Job Management',
    description: "Create, update, or manage processing jobs with priority and scheduling",
    summary: "Manage job",
    body: jobBody,
    response
};