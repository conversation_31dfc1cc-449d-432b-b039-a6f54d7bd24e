const authenticateBody = {
    type: 'object',
    properties: {
        username: { type: 'string', minLength: 1 },
        password: { type: 'string', minLength: 1 },
        customer: { type: 'string' },
        loginType: { type: 'string', enum: ['standard', 'sso', 'token'] },
        token: { type: 'string' }
    },
    required: ['username', 'password'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Authentication successful",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "token": { "type": "string", "example": "jwt_token_here" },
                "user": {
                    "type": "object",
                    "properties": {
                        "id": { "type": "string" },
                        "username": { "type": "string" },
                        "role": { "type": "string" }
                    }
                }
            }
        }
    },
    "401": {
        "description": "Authentication failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid credentials" }
            }
        }
    }
};

module.exports = {
    tags: 'Authentication',
    description: "Authenticate user credentials and generate access token",
    summary: "User authentication",
    body: authenticateBody,
    response
};