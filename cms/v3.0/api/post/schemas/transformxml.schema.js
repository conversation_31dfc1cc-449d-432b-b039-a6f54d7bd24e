const transformxmlBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        xmlContent: { type: 'string', minLength: 1 },
        xsltStylesheet: { type: 'string' },
        outputFormat: { type: 'string', enum: ['xml', 'html', 'text'], default: 'xml' },
        validateInput: { type: 'boolean', default: true },
        validateOutput: { type: 'boolean', default: true },
        parameters: { type: 'object' }
    },
    required: ['customer', 'project', 'doi', 'xmlContent'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "XML transformation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "transformedContent": { "type": "string" },
                "outputFormat": { "type": "string" },
                "validationResults": { "type": "object" },
                "transformationTime": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "XML transformation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Transform XML content using XSLT stylesheets with validation",
    summary: "Transform XML",
    body: transformxmlBody,
    response
};