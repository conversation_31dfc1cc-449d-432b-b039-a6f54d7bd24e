const updatecorrectionqueryBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        queries: {
            type: 'array',
            items: { type: 'string' }
        },
        typeSetter: { type: 'string' },
        lastUpdated: { type: 'string', format: 'date-time' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Correction query updated successfully",
        "schema": {
            "type": "string",
            "example": "Updated"
        }
    },
    "400": {
        "description": "Correction query update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    },
    "404": {
        "description": "Resource not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Update correction queries in Elasticsearch with typesetter information",
    summary: "Update correction query",
    body: updatecorrectionqueryBody,
    response
};