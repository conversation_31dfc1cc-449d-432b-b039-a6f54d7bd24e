const getarticletypeBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string' },
        articleTypes: { type: 'array', items: { type: 'string' } },
        includeMetadata: { type: 'boolean', default: false }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article types retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "articleTypes": { "type": "array" },
                "metadata": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve article types",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve article types" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Get available article types for a project or specific article",
    summary: "Get article types",
    body: getarticletypeBody,
    response
};