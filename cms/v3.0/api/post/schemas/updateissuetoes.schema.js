const updateissuetoesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        issueData: {
            type: 'object',
            properties: {
                volume: { type: 'string' },
                issue: { type: 'string' },
                year: { type: 'number' },
                articles: { type: 'array' },
                status: { type: 'string' }
            },
            required: ['volume', 'issue']
        },
        elasticsearchIndex: { type: 'string' },
        updateType: { type: 'string', enum: ['full', 'partial', 'articles'] },
        validateData: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'issueData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Issue updated to Elasticsearch successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "issueId": { "type": "string" },
                "articlesUpdated": { "type": "number" },
                "elasticsearchResponse": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Issue update to Elasticsearch failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Update issue information to Elasticsearch with validation",
    summary: "Update issue to ES",
    body: updateissuetoesBody,
    response
};