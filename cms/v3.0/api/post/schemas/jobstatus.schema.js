const jobstatusBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        jobId: { type: 'string', minLength: 1 },
        doi: { type: 'string' },
        includeHistory: { type: 'boolean', default: false },
        includeDetails: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'jobId'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Job status retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "jobStatus": { "type": "string", "enum": ["queued", "running", "completed", "failed", "cancelled"] },
                "progress": { "type": "number" },
                "startTime": { "type": "string" },
                "endTime": { "type": "string" },
                "errorMessage": { "type": "string" },
                "history": { "type": "array" }
            }
        }
    },
    "404": {
        "description": "Job not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Job not found" }
            }
        }
    }
};

module.exports = {
    tags: 'Job Management',
    description: "Get current status and history of processing jobs",
    summary: "Get job status",
    body: jobstatusBody,
    response
};