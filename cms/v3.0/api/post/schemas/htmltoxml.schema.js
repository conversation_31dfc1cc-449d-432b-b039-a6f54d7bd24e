const htmltoxmlBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        htmlContent: { type: 'string', minLength: 1 },
        xmlSchema: { type: 'string' },
        preserveFormatting: { type: 'boolean', default: true },
        validateOutput: { type: 'boolean', default: true },
        cleanupHtml: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'htmlContent'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "HTML to XML conversion completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "xmlContent": { "type": "string" },
                "validationErrors": { "type": "array" },
                "conversionLog": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "HTML to XML conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "XML conversion failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Convert HTML content to structured XML with validation and cleanup",
    summary: "HTML to XML",
    body: htmltoxmlBody,
    response
};