const updateinvoiceBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        invoiceData: {
            type: 'object',
            properties: {
                invoiceNumber: { type: 'string' },
                amount: { type: 'number' },
                currency: { type: 'string', default: 'USD' },
                dueDate: { type: 'string', format: 'date' },
                status: { type: 'string', enum: ['pending', 'paid', 'overdue'] },
                items: { type: 'array' }
            },
            required: ['invoiceNumber', 'amount']
        },
        updateType: { type: 'string', enum: ['create', 'update', 'status'] },
        sendNotification: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'invoiceData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Invoice updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "invoiceId": { "type": "string" },
                "invoiceNumber": { "type": "string" },
                "amount": { "type": "number" },
                "notificationSent": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Invoice update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Financial',
    description: "Update invoice information with status tracking and notifications",
    summary: "Update invoice",
    body: updateinvoiceBody,
    response
};