const getreviewersfromwosBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        keywords: { type: 'array', items: { type: 'string' } },
        subjectArea: { type: 'string' },
        excludeAuthors: <AUTHORS>
        maxResults: { type: 'number', default: 50 },
        minPublications: { type: 'number', default: 5 }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reviewers from Web of Science retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "reviewers": { "type": "array" },
                "totalFound": { "type": "number" },
                "searchCriteria": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve reviewers from WoS",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to search Web of Science" }
            }
        }
    }
};

module.exports = {
    tags: 'Peer Review',
    description: "Find potential reviewers from Web of Science database based on article keywords and subject area",
    summary: "Get reviewers from WoS",
    body: getreviewersfromwosBody,
    response
};