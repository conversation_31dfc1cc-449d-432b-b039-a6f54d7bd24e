const collectlqcreportinfoBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reportType: { type: 'string', enum: ['full', 'summary', 'errors'] },
        includeMetrics: { type: 'boolean', default: true },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "LQC report information collected successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "reportData": { "type": "object" },
                "metrics": { "type": "object" },
                "generatedAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Invalid report parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid report type or date range" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Collect Language Quality Check (LQC) report information for articles",
    summary: "Collect LQC report info",
    body: collectlqcreportinfoBody,
    response
};