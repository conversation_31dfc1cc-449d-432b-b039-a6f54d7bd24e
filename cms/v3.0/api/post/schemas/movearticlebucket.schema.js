const movearticlebucketBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        sourceBucket: { type: 'string', minLength: 1 },
        destinationBucket: { type: 'string', minLength: 1 },
        preserveStructure: { type: 'boolean', default: true },
        updateReferences: { type: 'boolean', default: true },
        createBackup: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'sourceBucket', 'destinationBucket'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article moved between buckets successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "movedFiles": { "type": "array" },
                "newLocation": { "type": "string" },
                "backupLocation": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Article bucket move failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to move article" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Move article files between storage buckets with structure preservation and reference updates",
    summary: "Move article bucket",
    body: movearticlebucketBody,
    response
};