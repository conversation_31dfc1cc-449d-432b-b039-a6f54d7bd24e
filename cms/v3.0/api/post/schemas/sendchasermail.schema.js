const chasermailBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        detail: {
            type: 'object',
            properties: {
                detail: {
                    type: 'object',
                    properties: {
                        stageName: { type: 'string' },
                        flow: { type: 'string' },
                        type: { type: 'string' },
                        customer: { type: 'string' },
                        project: { type: 'string' },
                        doi: { type: 'string' }
                    }
                }
            }
        },
        triggerWorkflow: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'detail'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Chaser mail sent successfully",
        "schema": {
            "type": "object",
            "properties": {
                "message": { "type": "string", "example": "Mail sent Successfully" },
                "workflowTriggered": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Chaser mail failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Send chaser emails with workflow trigger capability",
    summary: "Send chaser mail",
    body: chasermailBody,
    response
};