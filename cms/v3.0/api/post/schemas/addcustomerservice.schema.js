const addcustomerserviceBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        serviceName: { type: 'string', minLength: 1 },
        serviceType: { type: 'string' },
        configuration: { type: 'object' },
        isActive: { type: 'boolean', default: true }
    },
    required: ['customer', 'serviceName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Customer service added successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Customer service added successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid service parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid service configuration" }
            }
        }
    }
};

module.exports = {
    tags: 'Customer Management',
    description: "Add new service configuration for customer",
    summary: "Add customer service",
    body: addcustomerserviceBody,
    response
};