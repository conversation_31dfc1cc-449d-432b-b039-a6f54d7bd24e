const uploadtabletoawsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        tableFile: {
            type: 'string',
            format: 'binary',
            description: 'Table file to upload to AWS'
        },
        tableId: { type: 'string' },
        tableCaption: { type: 'string' },
        tableFormat: { type: 'string', enum: ['xlsx', 'csv', 'html', 'xml'] },
        awsBucket: { type: 'string' },
        awsPath: { type: 'string' },
        replaceExisting: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'tableFile'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Table uploaded to AWS successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "awsUrl": { "type": "string" },
                "tableId": { "type": "string" },
                "uploadedAt": { "type": "string", "format": "date-time" },
                "fileSize": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Table upload to AWS failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Upload table files to AWS S3 with format validation and path management",
    summary: "Upload table to AWS",
    body: uploadtabletoawsBody,
    response,
    consumes: ['multipart/form-data']
};