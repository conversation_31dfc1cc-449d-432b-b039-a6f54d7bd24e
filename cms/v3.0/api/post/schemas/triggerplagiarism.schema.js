const triggerplagiarismBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        checkType: { type: 'string', enum: ['full', 'partial', 'similarity'], default: 'full' },
        threshold: { type: 'number', minimum: 0, maximum: 100, default: 15 },
        excludeReferences: { type: 'boolean', default: true },
        excludeQuotes: { type: 'boolean', default: true },
        generateReport: { type: 'boolean', default: true },
        notifyOnCompletion: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Plagiarism check triggered successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "jobId": { "type": "string" },
                "estimatedTime": { "type": "number" },
                "checkType": { "type": "string" },
                "threshold": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Plagiarism check trigger failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Trigger plagiarism detection with configurable thresholds and exclusions",
    summary: "Trigger plagiarism check",
    body: triggerplagiarismBody,
    response
};