const convertxlsxBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        filePath: { type: 'string', minLength: 1 },
        sheetName: { type: 'string' },
        outputFormat: { type: 'string', enum: ['xml', 'json', 'csv'], default: 'xml' },
        includeHeaders: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'filePath'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Excel file converted successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "convertedData": { "type": "string" },
                "format": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Excel conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to convert Excel file" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Conversion',
    description: "Convert Excel files (.xlsx) to XML, JSON, or CSV format",
    summary: "Convert Excel to XML/JSON",
    body: convertxlsxBody,
    response
};