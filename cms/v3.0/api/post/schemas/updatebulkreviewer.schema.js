const updatebulkreviewerBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        reviewers: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    doi: { type: 'string' },
                    email: { type: 'string', format: 'email' },
                    action: { type: 'string', enum: ['add', 'remove', 'update'] },
                    status: { type: 'string' },
                    dueDate: { type: 'string' }
                },
                required: ['doi', 'email', 'action']
            }
        },
        notifyReviewers: { type: 'boolean', default: true },
        batchSize: { type: 'number', default: 10 }
    },
    required: ['customer', 'project', 'reviewers'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Bulk reviewer update completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "processed": { "type": "number" },
                "successful": { "type": "number" },
                "failed": { "type": "number" },
                "batchId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Bulk reviewer update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Reviewer Management',
    description: "Update multiple reviewers in bulk with batch processing",
    summary: "Update bulk reviewers",
    body: updatebulkreviewerBody,
    response
};