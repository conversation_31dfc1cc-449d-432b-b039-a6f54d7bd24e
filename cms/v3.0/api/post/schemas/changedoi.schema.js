const changedoiBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        oldDoi: { type: 'string', minLength: 1 },
        newDoi: { type: 'string', minLength: 1 },
        updateReferences: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'oldDoi', 'newDoi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "DOI changed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "DOI updated successfully" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Change article DOI with reference updates",
    summary: "Change article DOI",
    body: changedoiBody,
    response
};