const klicksBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        action: { type: 'string', enum: ['automation', 'package-failure', 'retry'] },
        triggerWorkflow: { type: 'boolean', default: true },
        notificationData: { type: 'object' },
        failureReason: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'action'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Klicks action processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "object" },
                "workflowTriggered": { "type": "boolean" },
                "notificationSent": { "type": "boolean" },
                "actionResult": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Klicks action failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Action processing failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Automation',
    description: "Process Klicks automation actions with workflow triggers and failure handling",
    summary: "Process Klicks action",
    body: klicksBody,
    response
};