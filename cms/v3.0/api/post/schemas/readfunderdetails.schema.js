const readfunderdetailsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        funderId: { type: 'string' },
        funderName: { type: 'string' },
        includeGrants: { type: 'boolean', default: true },
        includeHistory: { type: 'boolean', default: false },
        format: { type: 'string', enum: ['json', 'xml'], default: 'json' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Funder details retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "funderDetails": { "type": "object" },
                "grants": { "type": "array" },
                "fundingHistory": { "type": "array" },
                "totalFunding": { "type": "number" }
            }
        }
    },
    "404": {
        "description": "Funder details not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Funder not found" }
            }
        }
    }
};

module.exports = {
    tags: 'Funding Management',
    description: "Read funder details with grant information and funding history",
    summary: "Read funder details",
    body: readfunderdetailsBody,
    response
};