const updatepawBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        pawData: {
            type: 'object',
            properties: {
                workflowStage: { type: 'string' },
                assignedTo: { type: 'string' },
                status: { type: 'string' },
                priority: { type: 'string', enum: ['low', 'medium', 'high'] },
                dueDate: { type: 'string', format: 'date-time' }
            }
        },
        updateType: { type: 'string', enum: ['assignment', 'status', 'priority'] },
        notifyAssignee: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'pawData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "PAW (Production Article Workflow) updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "workflowId": { "type": "string" },
                "assignedTo": { "type": "string" },
                "notificationSent": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "PAW update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Update Production Article Workflow with assignment and notifications",
    summary: "Update PAW",
    body: updatepawBody,
    response
};