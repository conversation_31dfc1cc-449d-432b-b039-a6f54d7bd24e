const updateelplicenseBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        apiKey: { type: 'string' },
        licenseType: { type: 'string' },
        licenseUrl: { type: 'string', format: 'uri' },
        addNotes: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "ELP license updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "licenseUpdated": { "type": "boolean" },
                "notesAdded": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "ELP license update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Legal',
    description: "Update ELP license information and add notes based on configuration",
    summary: "Update ELP license",
    body: updateelplicenseBody,
    response
};