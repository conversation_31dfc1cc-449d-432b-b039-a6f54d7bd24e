const presubmitreportBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reportType: { type: 'string', enum: ['validation', 'quality', 'completeness', 'all'] },
        includeDetails: { type: 'boolean', default: true },
        format: { type: 'string', enum: ['json', 'pdf', 'html'], default: 'json' },
        checkCriteria: { type: 'array', items: { type: 'string' } }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Pre-submit report generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "reportData": { "type": "object" },
                "validationScore": { "type": "number" },
                "issues": { "type": "array" },
                "recommendations": { "type": "array" },
                "reportPath": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Pre-submit report generation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate report" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Generate comprehensive pre-submission reports with validation and quality checks",
    summary: "Pre-submit report",
    body: presubmitreportBody,
    response
};