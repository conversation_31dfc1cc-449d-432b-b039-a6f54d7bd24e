const generatepdfBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        pdfType: { type: 'string', enum: ['proof', 'final', 'author'] },
        includeAnnotations: { type: 'boolean', default: false },
        watermark: { type: 'string' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "PDF generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "pdfUrl": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Generation',
    description: "Generate PDF from article content",
    summary: "Generate PDF",
    body: generatepdfBody,
    response
};