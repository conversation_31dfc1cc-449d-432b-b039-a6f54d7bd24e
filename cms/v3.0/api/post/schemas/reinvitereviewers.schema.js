const reinvitereviewersBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reviewers: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    email: { type: 'string', format: 'email' },
                    givenName: { type: 'string' },
                    surName: { type: 'string' }
                },
                required: ['email']
            }
        },
        bulkProcess: { type: 'boolean', default: false },
        customMessage: { type: 'string' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reviewers reinvited successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string" },
                "jobsQueued": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Reviewer reinvitation failed",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "number" },
                "message": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Reviewer Management',
    description: "Reinvite reviewers with bulk processing support and queue management",
    summary: "Reinvite reviewers",
    body: reinvitereviewersBody,
    response
};