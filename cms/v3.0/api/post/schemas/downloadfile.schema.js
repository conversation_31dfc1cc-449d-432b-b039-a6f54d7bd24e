const downloadfileBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        fileName: { type: 'string', minLength: 1 },
        fileType: { type: 'string' },
        downloadType: { type: 'string', enum: ['direct', 'zip', 'secure'] },
        includeMetadata: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'fileName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "File download initiated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "downloadUrl": { "type": "string" },
                "fileSize": { "type": "number" },
                "expiresAt": { "type": "string" }
            }
        }
    },
    "404": {
        "description": "File not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "File not found" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Download files associated with articles",
    summary: "Download article file",
    body: downloadfileBody,
    response
};