const loggerreportBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reportType: { type: 'string', enum: ['error', 'activity', 'performance', 'all'] },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        severity: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
        format: { type: 'string', enum: ['json', 'csv', 'pdf'], default: 'json' },
        includeStackTrace: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'reportType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Logger report generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "reportData": { "type": "array" },
                "totalEntries": { "type": "number" },
                "reportPath": { "type": "string" },
                "generatedAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Logger report generation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate report" }
            }
        }
    }
};

module.exports = {
    tags: 'Reporting',
    description: "Generate comprehensive logger reports with filtering and export options",
    summary: "Generate logger report",
    body: loggerreportBody,
    response
};