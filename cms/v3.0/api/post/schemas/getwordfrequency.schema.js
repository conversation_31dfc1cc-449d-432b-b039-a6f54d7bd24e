const getwordfrequencyBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        minLength: { type: 'number', default: 3 },
        maxResults: { type: 'number', default: 100 },
        excludeStopWords: { type: 'boolean', default: true },
        caseSensitive: { type: 'boolean', default: false },
        includeNumbers: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Word frequency analysis completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "wordFrequency": { "type": "array" },
                "totalWords": { "type": "number" },
                "uniqueWords": { "type": "number" },
                "topWords": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Failed to analyze word frequency",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to analyze word frequency" }
            }
        }
    }
};

module.exports = {
    tags: 'Text Analysis',
    description: "Analyze word frequency in article content with filtering options",
    summary: "Get word frequency",
    body: getwordfrequencyBody,
    response
};