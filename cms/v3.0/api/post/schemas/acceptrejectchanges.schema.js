const acceptrejectchangesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        action: { type: 'string', enum: ['accept', 'reject'] },
        changes: { type: 'array', items: { type: 'object' } },
        comment: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'action'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Changes processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Changes accepted/rejected successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid action parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid action or change data" }
            }
        }
    }
};

module.exports = {
    tags: 'Review',
    description: "Accept or reject tracked changes in article content",
    summary: "Process tracked changes",
    body: acceptrejectchangesBody,
    response
};