const clonearticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        sourceDoi: { type: 'string', minLength: 1 },
        targetDoi: { type: 'string', minLength: 1 },
        cloneContent: { type: 'boolean', default: true },
        cloneMetadata: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'sourceDoi', 'targetDoi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article cloned successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "clonedDoi": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Clone article with content and metadata",
    summary: "Clone article",
    body: clonearticleBody,
    response
};