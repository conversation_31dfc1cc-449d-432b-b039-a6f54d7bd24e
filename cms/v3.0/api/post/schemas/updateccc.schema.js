const updatecccBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        manuscript: { type: 'string' },
        attempt: { type: 'number', default: 0 },
        cccAction: { type: 'string', enum: ['create', 'update', 'validate'] },
        updateObjects: { type: 'array' },
        skipValidation: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "CCC update completed successfully",
        "schema": {
            "type": "boolean",
            "example": true
        }
    },
    "400": {
        "description": "CCC update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update CCC (Content Creation and Curation) with manuscript processing",
    summary: "Update CCC",
    body: updatecccBody,
    response
};