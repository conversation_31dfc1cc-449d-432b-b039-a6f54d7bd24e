const commonfunctionBody = {
    type: 'object',
    properties: {
        functionName: { type: 'string', minLength: 1 },
        parameters: { type: 'object' },
        customer: { type: 'string' },
        project: { type: 'string' },
        doi: { type: 'string' },
        context: { type: 'string' }
    },
    required: ['functionName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Common function executed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "result": { "type": "object" },
                "executionTime": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Invalid function parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid function name or parameters" }
            }
        }
    }
};

module.exports = {
    tags: 'Utilities',
    description: "Execute common utility functions with configurable parameters",
    summary: "Execute common function",
    body: commonfunctionBody,
    response
};