const signoffBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        signoff: { type: 'string', minLength: 1 },
        currStage: { type: 'string', minLength: 1 },
        nextStage: { type: 'string' },
        comment: { type: 'string' },
        reviewer: { type: 'string' },
        flowName: { type: 'string', default: 'signoff' }
    },
    required: ['customer', 'project', 'doi', 'signoff', 'currStage'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Signoff completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Signoff completed successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid signoff parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid signoff option or missing required parameters" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow',
    description: "Process signoff actions for workflow stages with conditional logic and email triggers",
    summary: "Process workflow signoff with stage transitions",
    body: signoffBody,
    response
};