const updatefundrefBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        fundingInfo: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    funderName: { type: 'string' },
                    funderId: { type: 'string' },
                    grantNumber: { type: 'string' },
                    awardNumber: { type: 'string' }
                }
            }
        },
        validateFunders: { type: 'boolean', default: true },
        updateCrossref: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'fundingInfo'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "FundRef information updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedFunders": { "type": "number" },
                "validationResults": { "type": "object" },
                "crossrefUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "FundRef update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Metadata',
    description: "Update funding reference information with validation and Crossref integration",
    summary: "Update FundRef",
    body: updatefundrefBody,
    response
};