const getemailDataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        type: { type: 'string', enum: ['email', 'paw'] },
        emailType: { type: 'string' },
        recipient: { type: 'string' },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        size: { type: 'number', default: 100 }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Email data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "body": { "type": "object" },
                "emails": { "type": "array" },
                "totalCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve email data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve email data" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Retrieve email data and PAW (Publisher Author Workflow) communications",
    summary: "Get email data",
    body: getemailDataBody,
    response
};