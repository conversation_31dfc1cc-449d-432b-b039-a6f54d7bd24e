const createversionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        versionType: { type: 'string', enum: ['major', 'minor', 'patch'] },
        versionNote: { type: 'string' },
        updateExistDb: { type: 'boolean', default: true },
        createBackup: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Version created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "versionNumber": { "type": "string" },
                "versionPath": { "type": "string" },
                "existDbUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Failed to create version",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create version" }
            }
        }
    }
};

module.exports = {
    tags: 'Version Management',
    description: "Create new version of article with ExistDB update and file storage",
    summary: "Create article version",
    body: createversionBody,
    response
};