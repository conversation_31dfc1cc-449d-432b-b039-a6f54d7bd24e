const getarticlelistBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        index: { type: 'string' },
        table: { type: 'string' },
        urlToPost: { type: 'string' },
        size: { type: 'number', default: 100 },
        from: { type: 'number', default: 0 },
        filters: { type: 'object' },
        sortBy: { type: 'string' },
        includeFields: { type: 'object' }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article list retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "hits": { "type": "array" },
                "total": { "type": "number" },
                "took": { "type": "number" },
                "timed_out": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve article list",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve articles" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Get paginated list of articles with filtering and sorting options",
    summary: "Get article list",
    body: getarticlelistBody,
    response
};