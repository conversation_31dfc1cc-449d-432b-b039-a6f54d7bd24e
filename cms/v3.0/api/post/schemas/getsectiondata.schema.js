const getsectiondataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        sectionType: { type: 'string', enum: ['abstract', 'introduction', 'methods', 'results', 'discussion', 'conclusion'] },
        includeSubsections: { type: 'boolean', default: true },
        format: { type: 'string', enum: ['xml', 'html', 'text'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Section data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "sections": { "type": "array" },
                "sectionCount": { "type": "number" },
                "wordCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve section data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve section data" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Retrieve specific sections of an article with subsection details",
    summary: "Get section data",
    body: getsectiondataBody,
    response
};