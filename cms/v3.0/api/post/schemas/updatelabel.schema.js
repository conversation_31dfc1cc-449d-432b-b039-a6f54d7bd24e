const updatelabelBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        labelData: {
            type: 'object',
            properties: {
                labelType: { type: 'string' },
                labelText: { type: 'string' },
                position: { type: 'string' },
                color: { type: 'string' },
                priority: { type: 'number' }
            }
        },
        labelStage: { type: 'string', enum: ['Label', 'Review', 'Approval'] },
        updateLog: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'labelData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Label updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "labelId": { "type": "string" },
                "logUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Label update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update article labels with logging and stage tracking",
    summary: "Update label",
    body: updatelabelBody,
    response
};