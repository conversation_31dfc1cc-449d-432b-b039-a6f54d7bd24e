const getfileBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        fileName: { type: 'string', minLength: 1 },
        fileType: { type: 'string' },
        version: { type: 'string' },
        includeMetadata: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'fileName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "File retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "fileContent": { "type": "string" },
                "fileSize": { "type": "number" },
                "lastModified": { "type": "string" },
                "metadata": { "type": "object" }
            }
        }
    },
    "404": {
        "description": "File not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "File not found" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Retrieve specific files associated with articles",
    summary: "Get file",
    body: getfileBody,
    response
};