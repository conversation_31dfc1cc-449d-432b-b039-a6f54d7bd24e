const multiqueuecountBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        queueTypes: { type: 'array', items: { type: 'string' } },
        includeDetails: { type: 'boolean', default: false },
        groupBy: { type: 'string', enum: ['status', 'priority', 'type'] },
        timeRange: {
            type: 'object',
            properties: {
                startTime: { type: 'string' },
                endTime: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Multi-queue count retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "queueCounts": { "type": "object" },
                "totalItems": { "type": "number" },
                "queueDetails": { "type": "array" },
                "lastUpdated": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Queue count retrieval failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to get queue counts" }
            }
        }
    }
};

module.exports = {
    tags: 'Queue Management',
    description: "Get counts and statistics for multiple processing queues with grouping options",
    summary: "Multi-queue count",
    body: multiqueuecountBody,
    response
};