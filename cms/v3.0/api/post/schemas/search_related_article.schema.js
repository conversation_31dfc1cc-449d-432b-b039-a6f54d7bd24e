const search_related_articleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        searchType: { type: 'string', enum: ['crossref', 'local', 'both'], default: 'both' },
        searchQuery: { type: 'string' },
        maxResults: { type: 'number', default: 10 },
        includeMetadata: { type: 'boolean', default: true },
        format: { type: 'string', enum: ['html', 'json', 'xml'], default: 'html' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Related articles found successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "results": { "type": "array" },
                "totalFound": { "type": "number" },
                "searchType": { "type": "string" },
                "searchQuery": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Related article search failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Search',
    description: "Search for related articles using CrossRef and local databases",
    summary: "Search related articles",
    body: search_related_articleBody,
    response
};