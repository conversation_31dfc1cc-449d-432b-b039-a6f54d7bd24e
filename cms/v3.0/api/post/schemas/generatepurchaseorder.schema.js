const generatepurchaseorderBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        vendorId: { type: 'string', minLength: 1 },
        items: { type: 'array', items: { type: 'object' } },
        totalAmount: { type: 'number' },
        currency: { type: 'string', default: 'USD' },
        deliveryDate: { type: 'string' },
        notes: { type: 'string' }
    },
    required: ['customer', 'project', 'vendorId', 'items'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Purchase order generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "purchaseOrderId": { "type": "string" },
                "poNumber": { "type": "string" },
                "totalAmount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Purchase order generation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate purchase order" }
            }
        }
    }
};

module.exports = {
    tags: 'Financial',
    description: "Generate purchase orders for vendors and services",
    summary: "Generate purchase order",
    body: generatepurchaseorderBody,
    response
};