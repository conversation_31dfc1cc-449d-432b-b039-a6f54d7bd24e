const reassignreviewersBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reviewers: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    email: { type: 'string', format: 'email' },
                    givenName: { type: 'string' },
                    surName: { type: 'string' },
                    action: { type: 'string', enum: ['add', 'remove', 'replace'] }
                },
                required: ['email', 'action']
            }
        },
        reason: { type: 'string' },
        notifyReviewers: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'reviewers'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reviewers reassigned successfully",
        "schema": {
            "type": "string",
            "example": "Reviewers reassigned successfully"
        }
    },
    "400": {
        "description": "Reviewer reassignment failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Reviewer Management',
    description: "Reassign reviewers for an article with automatic note creation",
    summary: "Reassign reviewers",
    body: reassignreviewersBody,
    response
};