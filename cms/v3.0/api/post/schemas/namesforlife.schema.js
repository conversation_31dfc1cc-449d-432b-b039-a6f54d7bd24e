const namesforlifeBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        organismNames: { type: 'array', items: { type: 'string' } },
        validateNames: { type: 'boolean', default: true },
        includeHierarchy: { type: 'boolean', default: false },
        format: { type: 'string', enum: ['json', 'xml'], default: 'json' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Names for Life data processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "validatedNames": { "type": "array" },
                "taxonomyData": { "type": "object" },
                "validationResults": { "type": "object" },
                "hierarchyData": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Names for Life processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process organism names" }
            }
        }
    }
};

module.exports = {
    tags: 'Scientific Data',
    description: "Process and validate organism names using Names for Life database with taxonomy hierarchy",
    summary: "Names for Life",
    body: namesforlifeBody,
    response
};