const parsedataBody = {
    type: 'object',
    properties: {
        type: { type: 'string', enum: ['parseAuthors', 'parseReference'], minLength: 1 },
        data: { type: 'string', minLength: 1 },
        format: { type: 'string', enum: ['xml', 'json', 'text'] },
        strictMode: { type: 'boolean', default: false },
        includeMetadata: { type: 'boolean', default: true }
    },
    required: ['type', 'data'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Data parsed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "parsedData": { "type": "object" },
                "parseType": { "type": "string" },
                "metadata": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Data parsing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to parse data" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Processing',
    description: "Parse author and reference data using resource manager with format options",
    summary: "Parse data",
    body: parsedataBody,
    response
};