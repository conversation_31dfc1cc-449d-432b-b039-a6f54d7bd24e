const resetguidedtourarticlesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string' },
        userId: { type: 'string' },
        tourType: { type: 'string', enum: ['submission', 'review', 'editing', 'production'] },
        resetAll: { type: 'boolean', default: false }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Guided tour articles reset successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "articlesReset": { "type": "number" },
                "tourType": { "type": "string" },
                "resetTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Guided tour reset failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Experience',
    description: "Reset guided tour progress for articles to allow users to retake tours",
    summary: "Reset guided tour",
    body: resetguidedtourarticlesBody,
    response
};