const languagequalitycheckBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        language: { type: 'string', default: 'en' },
        checkTypes: { type: 'array', items: { type: 'string', enum: ['grammar', 'spelling', 'style', 'readability'] } },
        severity: { type: 'string', enum: ['low', 'medium', 'high'], default: 'medium' },
        autoFix: { type: 'boolean', default: false },
        generateReport: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Language quality check completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "qualityScore": { "type": "number" },
                "issues": { "type": "array" },
                "suggestions": { "type": "array" },
                "reportPath": { "type": "string" },
                "autoFixedCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Language quality check failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Quality check failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Perform comprehensive language quality checks with grammar, spelling, style, and readability analysis",
    summary: "Language quality check",
    body: languagequalitycheckBody,
    response
};