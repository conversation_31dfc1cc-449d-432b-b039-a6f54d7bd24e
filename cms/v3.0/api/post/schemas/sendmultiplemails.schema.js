const multiplemailsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        emails: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    to: { type: 'string', format: 'email' },
                    subject: { type: 'string' },
                    body: { type: 'string' },
                    template: { type: 'string' },
                    attachments: { type: 'array' }
                },
                required: ['to', 'subject', 'body']
            }
        },
        batchSize: { type: 'number', default: 10 },
        delay: { type: 'number', default: 1000 },
        trackDelivery: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'emails'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Multiple emails sent successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "totalSent": { "type": "number" },
                "failed": { "type": "number" },
                "batchId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Multiple emails sending failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Send multiple emails in batches with delivery tracking",
    summary: "Send multiple emails",
    body: multiplemailsBody,
    response
};