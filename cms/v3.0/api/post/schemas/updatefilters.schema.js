const updatefiltersBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        filters: {
            type: 'object',
            properties: {
                status: { type: 'array', items: { type: 'string' } },
                stage: { type: 'array', items: { type: 'string' } },
                dateRange: {
                    type: 'object',
                    properties: {
                        start: { type: 'string', format: 'date' },
                        end: { type: 'string', format: 'date' }
                    }
                },
                assignedTo: { type: 'array', items: { type: 'string' } }
            }
        },
        filterType: { type: 'string', enum: ['dashboard', 'report', 'search'] },
        saveAsDefault: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'filters'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Filters updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "filterId": { "type": "string" },
                "appliedFilters": { "type": "object" },
                "savedAsDefault": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Filters update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Interface',
    description: "Update dashboard and search filters with save options",
    summary: "Update filters",
    body: updatefiltersBody,
    response
};