const applyhouserrulesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        ruleType: { type: 'string' },
        rules: { type: 'array', items: { type: 'object' } },
        applyToContent: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "House rules applied successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "House rules applied successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid rule parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid house rules or application failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Apply customer-specific house rules to article content",
    summary: "Apply house rules",
    body: applyhouserrulesBody,
    response
};