const publicationacceptedBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        acceptanceDate: { type: 'string' },
        publicationDate: { type: 'string' },
        notifyAuthors: <AUTHORS>
        generateCertificate: { type: 'boolean', default: false },
        updateStatus: { type: 'boolean', default: true },
        triggerWorkflow: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Publication acceptance processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "acceptanceId": { "type": "string" },
                "notificationsSent": { "type": "boolean" },
                "certificateGenerated": { "type": "boolean" },
                "workflowTriggered": { "type": "boolean" },
                "nextSteps": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Publication acceptance processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process acceptance" }
            }
        }
    }
};

module.exports = {
    tags: 'Publication Management',
    description: "Process publication acceptance with author notifications and workflow triggers",
    summary: "Publication accepted",
    body: publicationacceptedBody,
    response
};