const convertequationsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        equationType: { type: 'string', enum: ['tex', 'mathml', 'image'] },
        outputFormat: { type: 'string', enum: ['svg', 'png', 'mathml'] },
        resolution: { type: 'number', default: 300 }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Equations converted successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "XML Updated Successfully with Converted Equations" },
                "convertedCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Equation conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to convert equations" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Convert mathematical equations from TeX to images or other formats",
    summary: "Convert equations",
    body: convertequationsBody,
    response
};