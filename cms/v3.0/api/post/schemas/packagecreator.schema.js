const packagecreatorBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        packageType: { type: 'string', enum: ['production', 'proof', 'submission', 'archive'] },
        includeAssets: { type: 'boolean', default: true },
        includeMetadata: { type: 'boolean', default: true },
        compressionLevel: { type: 'number', default: 6 },
        outputFormat: { type: 'string', enum: ['zip', 'tar', 'tar.gz'], default: 'zip' }
    },
    required: ['customer', 'project', 'doi', 'packageType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Package created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "packagePath": { "type": "string" },
                "packageSize": { "type": "number" },
                "includedFiles": { "type": "array" },
                "manifest": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Package creation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create package" }
            }
        }
    }
};

module.exports = {
    tags: 'Package Management',
    description: "Create comprehensive packages for different workflow stages with assets and metadata",
    summary: "Create package",
    body: packagecreatorBody,
    response
};