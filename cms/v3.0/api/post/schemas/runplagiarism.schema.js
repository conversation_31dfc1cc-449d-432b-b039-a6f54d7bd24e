const runplagiarismBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        checkType: { type: 'string', enum: ['full', 'partial', 'references'] },
        threshold: { type: 'number', minimum: 0, maximum: 100, default: 15 },
        excludeReferences: { type: 'boolean', default: true },
        excludeQuotes: { type: 'boolean', default: true },
        generateReport: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Plagiarism check completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "similarityScore": { "type": "number" },
                "matches": { "type": "array" },
                "reportPath": { "type": "string" },
                "passed": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Plagiarism check failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Run plagiarism detection with configurable thresholds and exclusion options",
    summary: "Run plagiarism check",
    body: runplagiarismBody,
    response
};