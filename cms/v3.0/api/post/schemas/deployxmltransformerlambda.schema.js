const deployxmltransformerlambdaBody = {
    type: 'object',
    properties: {
        email: { type: 'string', format: 'email', minLength: 1 },
        deploymentType: { type: 'string', enum: ['full', 'code-only', 'config-only'] },
        environment: { type: 'string', enum: ['development', 'staging', 'production'] },
        forceUpdate: { type: 'boolean', default: false }
    },
    required: ['email'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "XML Transformer Lambda deployed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "message": { "type": "string", "example": "lambda deployed successfully" },
                "timestamp": { "type": "string" },
                "email": { "type": "string" },
                "deploymentId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Deployment failed or endpoint disabled",
        "schema": {
            "type": "object",
            "properties": {
                "message": { "type": "string", "example": "Deployment endpoint is disabled for this instance." }
            }
        }
    }
};

module.exports = {
    tags: 'Deployment',
    description: "Deploy XML Transformer Lambda function with XSL files and dependencies",
    summary: "Deploy XML Transformer Lambda",
    body: deployxmltransformerlambdaBody,
    response
};