const changemanuscriptBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        articleHTML: { type: 'string', minLength: 1 },
        version: { type: 'string' },
        notesData: { type: 'object' }
    },
    required: ['customer', 'project', 'doi', 'articleHTML'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Manuscript changed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "number", "example": 200 },
                        "message": { "type": "string", "example": "Manuscript Changed" }
                    }
                },
                "step": { "type": "string", "example": "changemanuscript" }
            }
        }
    },
    "400": {
        "description": "Bad Request - Invalid parameters",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "number", "example": 400 },
                        "message": { "type": "string", "example": "Failed to process manuscript change" }
                    }
                }
            }
        }
    },
    "500": {
        "description": "Server Error - Missing parameters",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "number", "example": 500 },
                        "message": { "type": "string", "example": "ERROR: Parameters missed" },
                        "resolve": { "type": "boolean", "example": true }
                    }
                },
                "step": { "type": "string", "example": "changemanuscript" }
            }
        }
    }
};

module.exports = {
    public: true,
    tags: 'Manuscript Management',
    description: "Change the manuscript content for an article. Updates the manuscript file and file list XML with new content.",
    summary: "Change manuscript content",
    body: changemanuscriptBody,
    response
};