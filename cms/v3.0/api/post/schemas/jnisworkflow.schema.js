const jnisworkflowBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        workflowType: { type: 'string', enum: ['submission', 'review', 'production'] },
        action: { type: 'string', enum: ['start', 'continue', 'complete', 'abort'] },
        parameters: { type: 'object' },
        autoProgress: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'workflowType', 'action'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "JNIS workflow processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "workflowId": { "type": "string" },
                "currentStage": { "type": "string" },
                "nextActions": { "type": "array" },
                "completionPercentage": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "JNIS workflow processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Workflow processing failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Process JNIS (Journal and Issue System) workflow operations",
    summary: "JNIS workflow",
    body: jnisworkflowBody,
    response
};