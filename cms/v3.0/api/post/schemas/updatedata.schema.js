const updatedataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        type: { type: 'string', enum: ['update', 'replace'] },
        data: { type: 'object' },
        skipElasticUpdate: { type: 'boolean' },
        xpath: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'type', 'data'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Data updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Article data updated successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid update parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid data format or missing required fields" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Update article data with support for partial updates and elastic search synchronization",
    summary: "Update article data in database",
    body: updatedataBody,
    response
};