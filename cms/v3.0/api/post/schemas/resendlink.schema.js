const resendlinkBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        stageName: { type: 'string', minLength: 1 },
        linkType: { type: 'string', enum: ['accept', 'reject', 'review', 'edit'] },
        recipient: { type: 'string', format: 'email' },
        customMessage: { type: 'string' },
        invalidateOld: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'stageName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Link resent successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "linkGenerated": { "type": "boolean" },
                "emailSent": { "type": "boolean" },
                "oldLinksInvalidated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Link resend failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Resend workflow links with option to invalidate old links and send notifications",
    summary: "Resend link",
    body: resendlinkBody,
    response
};