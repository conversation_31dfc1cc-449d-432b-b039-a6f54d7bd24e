const uploadtoawsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        file: {
            type: 'string',
            format: 'binary',
            description: 'File to upload to AWS'
        },
        fileType: { type: 'string', enum: ['image', 'document', 'video', 'audio', 'data'] },
        awsBucket: { type: 'string' },
        awsPath: { type: 'string' },
        fileName: { type: 'string' },
        contentType: { type: 'string' },
        replaceExisting: { type: 'boolean', default: false },
        makePublic: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'file'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "File uploaded to AWS successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "awsUrl": { "type": "string" },
                "fileId": { "type": "string" },
                "fileName": { "type": "string" },
                "uploadedAt": { "type": "string", "format": "date-time" },
                "fileSize": { "type": "number" },
                "contentType": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "File upload to AWS failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Upload files to AWS S3 with content type validation and access control",
    summary: "Upload to AWS",
    body: uploadtoawsBody,
    response,
    consumes: ['multipart/form-data']
};