const convertwordtohtmlBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        key: { type: 'string', minLength: 1 },
        service: { type: 'string', default: 'doctohtml' },
        data: {
            type: 'object',
            properties: {
                extn: { type: 'string', enum: ['.docx', '.doc'] },
                name: { type: 'string' }
            }
        },
        userRole: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'key'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Word document converted to HTML successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": {
                    "type": "object",
                    "properties": {
                        "jobid": { "type": "string" }
                    }
                },
                "htmlContent": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Word to HTML conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to convert Word document to HTML" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Conversion',
    description: "Convert Word documents (.doc/.docx) to HTML format",
    summary: "Convert Word to HTML",
    body: convertwordtohtmlBody,
    response
};