const getfiledesignatorsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        process: { type: 'string', enum: ['get', 'update'] },
        raw: { type: 'string', enum: ['true', 'false'] },
        cmsVersion: { type: 'string', default: 'v3.0' }
    },
    required: ['customer', 'project', 'doi', 'process'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "File designators retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "fileDesignations": { "type": "string" },
                "options": { "type": "string" },
                "rawXML": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve file designators",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to fetch file designators" }
            }
        }
    }
};

module.exports = {
    tags: 'File Management',
    description: "Get file designators and options for article files with manuscript, figure, table classifications",
    summary: "Get file designators",
    body: getfiledesignatorsBody,
    response
};