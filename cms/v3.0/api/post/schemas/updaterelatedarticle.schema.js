const updaterelatedarticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        relatedArticles: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    relatedDoi: { type: 'string' },
                    relationType: { type: 'string', enum: ['correction', 'retraction', 'commentary', 'response', 'update'] },
                    title: { type: 'string' },
                    authors: { type: 'array' },
                    publicationDate: { type: 'string', format: 'date' }
                },
                required: ['relatedDoi', 'relationType']
            }
        },
        updateType: { type: 'string', enum: ['add', 'remove', 'update', 'replace'] },
        validateRelations: { type: 'boolean', default: true },
        updateCrossref: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'relatedArticles'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Related articles updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "updatedRelations": { "type": "number" },
                "addedRelations": { "type": "number" },
                "removedRelations": { "type": "number" },
                "validationResults": { "type": "object" },
                "crossrefUpdated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Related articles update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Update related article relationships with validation and Crossref integration",
    summary: "Update related articles",
    body: updaterelatedarticleBody,
    response
};