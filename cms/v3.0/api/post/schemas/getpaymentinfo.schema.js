const getpaymentinfoBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        paymentId: { type: 'string' },
        transactionId: { type: 'string' },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        paymentStatus: { type: 'string', enum: ['pending', 'completed', 'failed', 'refunded'] },
        includeDetails: { type: 'boolean', default: true }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Payment information retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "payments": { "type": "array" },
                "totalAmount": { "type": "number" },
                "currency": { "type": "string" },
                "paymentMethods": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve payment information",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve payment info" }
            }
        }
    }
};

module.exports = {
    tags: 'Financial',
    description: "Retrieve payment information and transaction details",
    summary: "Get payment info",
    body: getpaymentinfoBody,
    response
};