const getduplicatesBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        searchField: { type: 'string', enum: ['title', 'doi', 'author', 'content'] },
        threshold: { type: 'number', default: 0.8 },
        includePartialMatches: { type: 'boolean', default: true },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project', 'searchField'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Duplicates found successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "duplicates": { "type": "array" },
                "totalCount": { "type": "number" },
                "threshold": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to find duplicates",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to search for duplicates" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Find duplicate articles based on various criteria",
    summary: "Find duplicate articles",
    body: getduplicatesBody,
    response
};