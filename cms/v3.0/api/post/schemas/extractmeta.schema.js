const extractmetaBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        sourceType: { type: 'string', enum: ['xml', 'pdf', 'docx'] },
        metaFields: { type: 'array', items: { type: 'string' } },
        includeAuthors: <AUTHORS>
        includeReferences: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'sourceType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Metadata extracted successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "metadata": { "type": "object" },
                "authors": { "type": "array" },
                "references": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Metadata extraction failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to extract metadata" }
            }
        }
    }
};

module.exports = {
    tags: 'Metadata Processing',
    description: "Extract metadata from various document formats",
    summary: "Extract metadata",
    body: extractmetaBody,
    response
};