const getarticlebyuserBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        userId: { type: 'string', minLength: 1 },
        userRole: { type: 'string' },
        includeAssigned: { type: 'boolean', default: true },
        includeCompleted: { type: 'boolean', default: false },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project', 'userId'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Articles retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "articles": { "type": "array" },
                "totalCount": { "type": "number" },
                "userInfo": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve articles",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve user articles" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Get articles assigned to or associated with a specific user",
    summary: "Get articles by user",
    body: getarticlebyuserBody,
    response
};