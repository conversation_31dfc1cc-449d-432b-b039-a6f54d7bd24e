const generatelqcBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        lqcType: { type: 'string', enum: ['full', 'quick', 'custom'] },
        checkTypes: { type: 'array', items: { type: 'string' } },
        generateReport: { type: 'boolean', default: true },
        autoFix: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "LQC generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "lqcId": { "type": "string" },
                "reportPath": { "type": "string" },
                "issuesFound": { "type": "number" },
                "autoFixedCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "LQC generation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate LQC" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Generate Language Quality Check (LQC) reports for articles",
    summary: "Generate LQC",
    body: generatelqcBody,
    response
};