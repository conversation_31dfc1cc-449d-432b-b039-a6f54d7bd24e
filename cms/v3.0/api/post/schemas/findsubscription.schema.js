const findsubscriptionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        subscriberEmail: { type: 'string', format: 'email' },
        subscriptionType: { type: 'string' },
        status: { type: 'string', enum: ['active', 'inactive', 'pending'] },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Subscription found successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "subscriptions": { "type": "array" },
                "totalCount": { "type": "number" }
            }
        }
    },
    "404": {
        "description": "No subscriptions found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "No subscriptions found" }
            }
        }
    }
};

module.exports = {
    tags: 'Subscription Management',
    description: "Find and retrieve subscription information",
    summary: "Find subscription",
    body: findsubscriptionBody,
    response
};