const authorstubsummaryBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        authorData: { type: 'object' },
        summaryType: { type: 'string', enum: ['full', 'brief'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Author stub summary generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "summary": { "type": "object" }
            }
        }
    }
};

module.exports = {
    tags: 'Author Management',
    description: "Generate author stub summary for article",
    summary: "Generate author stub summary",
    body: authorstubsummaryBody,
    response
};