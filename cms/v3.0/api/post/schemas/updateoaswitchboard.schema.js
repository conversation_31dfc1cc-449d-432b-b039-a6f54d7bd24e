const updateoaswitchboardBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        oaData: {
            type: 'object',
            properties: {
                oaStatus: { type: 'string', enum: ['open', 'closed', 'hybrid'] },
                license: { type: 'string' },
                embargoDate: { type: 'string', format: 'date' },
                repository: { type: 'string' },
                fundingCompliance: { type: 'boolean' }
            }
        },
        switchboardAction: { type: 'string', enum: ['register', 'update', 'validate'] },
        notifyRepositories: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'oaData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "OA Switchboard updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "switchboardId": { "type": "string" },
                "oaStatus": { "type": "string" },
                "repositoriesNotified": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "OA Switchboard update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Open Access',
    description: "Update Open Access Switchboard with repository notifications",
    summary: "Update OA Switchboard",
    body: updateoaswitchboardBody,
    response
};