const sendmailBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        to: { type: 'string', minLength: 1 },
        cc: { type: 'string' },
        bcc: { type: 'string' },
        subject: { type: 'string', minLength: 1 },
        body: { type: 'string', minLength: 1 },
        templateName: { type: 'string' },
        attachments: { type: 'array', items: { type: 'string' } }
    },
    required: ['customer', 'project', 'doi', 'to', 'subject', 'body'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Email sent successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Email sent successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid email parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid email address or missing required fields" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Send emails with template support and attachment handling",
    summary: "Send email notifications",
    body: sendmailBody,
    response
};