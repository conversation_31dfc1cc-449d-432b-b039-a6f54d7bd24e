const verifyauthenticationBody = {
    type: 'object',
    properties: {
        token: { type: 'string', minLength: 1 },
        authType: { type: 'string', enum: ['jwt', 'oauth', 'sso', 'api-key'] },
        email: { type: 'string', format: 'email' },
        userId: { type: 'string' },
        sessionId: { type: 'string' },
        refreshToken: { type: 'string' },
        validatePermissions: { type: 'boolean', default: true },
        requiredRole: { type: 'string', enum: ['admin', 'editor', 'reviewer', 'author', 'user'] }
    },
    required: ['token', 'authType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Authentication verified successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "isValid": { "type": "boolean" },
                "userId": { "type": "string" },
                "email": { "type": "string" },
                "role": { "type": "string" },
                "permissions": { "type": "array" },
                "expiresAt": { "type": "string", "format": "date-time" },
                "refreshRequired": { "type": "boolean" }
            }
        }
    },
    "401": {
        "description": "Authentication verification failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid token" },
                "code": { "type": "string", "example": "INVALID_TOKEN" }
            }
        }
    },
    "403": {
        "description": "Insufficient permissions",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Insufficient permissions" },
                "requiredRole": { "type": "string" },
                "userRole": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Authentication',
    description: "Verify user authentication tokens with role and permission validation",
    summary: "Verify authentication",
    body: verifyauthenticationBody,
    response
};