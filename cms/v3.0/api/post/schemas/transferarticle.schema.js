const transferarticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        targetCustomer: { type: 'string', minLength: 1 },
        targetProject: { type: 'string', minLength: 1 },
        transferType: { type: 'string', enum: ['move', 'copy'] },
        preserveHistory: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'targetCustomer', 'targetProject'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article transferred successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "newLocation": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Article Management',
    description: "Transfer article between customers/projects",
    summary: "Transfer article",
    body: transferarticleBody,
    response
};