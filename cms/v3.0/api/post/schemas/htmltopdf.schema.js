const htmltopdfBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        htmlContent: { type: 'string' },
        pageSize: { type: 'string', enum: ['A4', 'Letter', 'Legal'], default: 'A4' },
        orientation: { type: 'string', enum: ['portrait', 'landscape'], default: 'portrait' },
        includeHeaders: { type: 'boolean', default: true },
        includeFooters: { type: 'boolean', default: true },
        margins: { type: 'object' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "HTML to PDF conversion completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "pdfPath": { "type": "string" },
                "fileSize": { "type": "number" },
                "pageCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "HTML to PDF conversion failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "PDF conversion failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Convert HTML content to PDF with customizable formatting options",
    summary: "HTML to PDF",
    body: htmltopdfBody,
    response
};