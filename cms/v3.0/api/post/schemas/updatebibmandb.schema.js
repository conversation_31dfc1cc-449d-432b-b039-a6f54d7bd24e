const updatebibmandbBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        editedRefs: {
            type: 'array',
            items: { type: 'string' }
        },
        updateType: { type: 'string', enum: ['full', 'partial', 'references'] },
        validateRefs: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Bibman database updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "success": { "type": "string" },
                "updatedRefs": { "type": "number" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Bibman database update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "object" }
            }
        }
    }
};

module.exports = {
    tags: 'Bibliography Management',
    description: "Update bibliography management database with edited references",
    summary: "Update Bibman DB",
    body: updatebibmandbBody,
    response
};