const getclinicaldataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        trialId: { type: 'string' },
        dataType: { type: 'string', enum: ['trial', 'protocol', 'results'] },
        includeHistory: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Clinical data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "clinicalData": { "type": "object" },
                "trialInfo": { "type": "object" },
                "lastUpdated": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve clinical data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve clinical data" }
            }
        }
    }
};

module.exports = {
    tags: 'Clinical Data',
    description: "Retrieve clinical trial data and protocols for medical articles",
    summary: "Get clinical data",
    body: getclinicaldataBody,
    response
};