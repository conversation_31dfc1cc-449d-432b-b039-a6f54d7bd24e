const getpublishedarticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        version: { type: 'string' },
        format: { type: 'string', enum: ['xml', 'html', 'pdf'] },
        includeMetadata: { type: 'boolean', default: true },
        includeAssets: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Published article retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "articleContent": { "type": "string" },
                "metadata": { "type": "object" },
                "publishedDate": { "type": "string" },
                "assets": { "type": "array" }
            }
        }
    },
    "404": {
        "description": "Published article not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Published article not found" }
            }
        }
    }
};

module.exports = {
    tags: 'Publishing',
    description: "Retrieve published article content and metadata",
    summary: "Get published article",
    body: getpublishedarticleBody,
    response
};