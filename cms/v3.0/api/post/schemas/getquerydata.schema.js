const getquerydataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        queryType: { type: 'string', enum: ['author', 'editor', 'copyeditor', 'all'] },
        status: { type: 'string', enum: ['open', 'resolved', 'pending'] },
        includeResponses: { type: 'boolean', default: true },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Query data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "queries": { "type": "array" },
                "totalCount": { "type": "number" },
                "openQueries": { "type": "number" },
                "resolvedQueries": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve query data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve queries" }
            }
        }
    }
};

module.exports = {
    tags: 'Editorial Process',
    description: "Retrieve author queries, editor queries, and copyeditor queries with responses",
    summary: "Get query data",
    body: getquerydataBody,
    response
};