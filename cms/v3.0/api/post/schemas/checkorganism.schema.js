const checkoragnismBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        organismName: { type: 'string', minLength: 1 },
        taxonomyId: { type: 'string' },
        validateTaxonomy: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'organismName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Organism validation completed",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "isValid": { "type": "boolean" },
                "taxonomyInfo": { "type": "object" },
                "suggestions": { "type": "array", "items": { "type": "string" } }
            }
        }
    },
    "400": {
        "description": "Invalid organism parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid organism name or taxonomy ID" }
            }
        }
    }
};

module.exports = {
    tags: 'Validation',
    description: "Validate organism names against taxonomy databases",
    summary: "Check organism taxonomy",
    body: checkoragnismBody,
    response
};