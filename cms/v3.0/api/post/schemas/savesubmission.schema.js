const savesubmissionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        convertjsontoxml: { type: 'boolean', default: false },
        updateESAndAWS: { type: 'boolean', default: false },
        type: { type: 'string', enum: ['statusupdate', 'content', 'metadata'] },
        fileName: { type: 'string' },
        xmldata: { type: 'object' },
        skipElasticUpdate: { type: 'string', enum: ['true', 'false'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Submission saved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "submissionId": { "type": "string" },
                "elasticUpdated": { "type": "boolean" },
                "awsUpdated": { "type": "boolean" },
                "xmlConverted": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Submission save failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Submission Management',
    description: "Save submission data with XML conversion and Elasticsearch/AWS updates",
    summary: "Save submission",
    body: savesubmissionBody,
    response
};