const readrordetailsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        rorId: { type: 'string' },
        organizationName: { type: 'string' },
        includeAffiliations: { type: 'boolean', default: true },
        includeHierarchy: { type: 'boolean', default: false },
        format: { type: 'string', enum: ['json', 'xml'], default: 'json' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "ROR details retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "rorDetails": { "type": "object" },
                "affiliations": { "type": "array" },
                "organizationHierarchy": { "type": "object" },
                "relatedOrganizations": { "type": "array" }
            }
        }
    },
    "404": {
        "description": "ROR details not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "ROR organization not found" }
            }
        }
    }
};

module.exports = {
    tags: 'Organization Management',
    description: "Read Research Organization Registry (ROR) details with affiliations and hierarchy",
    summary: "Read ROR details",
    body: readrordetailsBody,
    response
};