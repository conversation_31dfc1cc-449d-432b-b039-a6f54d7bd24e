const updateuserdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        reviewStatus: { type: 'string', enum: ['pending', 'completed', 'declined'] },
        modifiedData: { type: 'string', enum: ['true', 'false'] },
        authType: { type: 'string', enum: ['sso', 'local', 'oauth'] },
        authId: { type: 'string' },
        userData: {
            type: 'object',
            properties: {
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                affiliation: { type: 'string' },
                orcid: { type: 'string' },
                bio: { type: 'string' }
            }
        }
    },
    required: ['email'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User data updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "User data updated successfully" },
                "userId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "User data update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update user data including authentication and profile information",
    summary: "Update user data",
    body: updateuserdataBody,
    response
};