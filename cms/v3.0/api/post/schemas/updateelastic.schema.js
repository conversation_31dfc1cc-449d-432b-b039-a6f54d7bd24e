const updateelasticBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        index: { type: 'string' },
        table: { type: 'string' },
        data: { type: 'object' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Elastic search updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Elastic search index updated successfully" }
            }
        }
    },
    "500": {
        "description": "Elastic search update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to update elastic search index" }
            }
        }
    }
};

module.exports = {
    tags: 'Search',
    description: "Update elastic search indices with article data for search functionality",
    summary: "Update elastic search index",
    body: updateelasticBody,
    response
};