const structurecontentBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string', minLength: 1 },
        structureType: { type: 'string', enum: ['auto', 'manual', 'template'] },
        template: { type: 'string' },
        preserveFormatting: { type: 'boolean', default: true },
        validateStructure: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Content structured successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "structuredContent": { "type": "string" },
                "structure": { "type": "object" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Content structuring failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Structure content automatically or using templates with validation",
    summary: "Structure content",
    body: structurecontentBody,
    response
};