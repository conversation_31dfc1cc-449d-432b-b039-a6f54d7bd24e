const revokearticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        revokeReason: { type: 'string', minLength: 1 },
        revokeType: { type: 'string', enum: ['retraction', 'withdrawal', 'suspension'] },
        notifyStakeholders: { type: 'boolean', default: true },
        publicNotice: { type: 'boolean', default: false },
        effectiveDate: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'revokeReason', 'revokeType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article revoked successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "revokeId": { "type": "string" },
                "effectiveDate": { "type": "string" },
                "notificationsSent": { "type": "boolean" },
                "publicNoticeCreated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Article revocation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Publication Management',
    description: "Revoke article publication with reason, type, and stakeholder notifications",
    summary: "Revoke article",
    body: revokearticleBody,
    response
};