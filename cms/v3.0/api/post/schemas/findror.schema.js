const findrorBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        organizationName: { type: 'string', minLength: 1 },
        country: { type: 'string' },
        exactMatch: { type: 'boolean', default: false },
        includeAlternatives: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'organizationName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "ROR ID found successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "rorId": { "type": "string" },
                "organizationName": { "type": "string" },
                "alternatives": { "type": "array" },
                "confidence": { "type": "number" }
            }
        }
    },
    "404": {
        "description": "ROR ID not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "ROR ID not found for organization" }
            }
        }
    }
};

module.exports = {
    tags: 'External Services',
    description: "Find Research Organization Registry (ROR) ID for organizations",
    summary: "Find ROR ID",
    body: findrorBody,
    response
};