const moveissuearticletoarchiveBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        issueId: { type: 'string' },
        archiveType: { type: 'string', enum: ['cold', 'warm', 'deep'], default: 'cold' },
        retentionPeriod: { type: 'number', default: 365 },
        compressFiles: { type: 'boolean', default: true },
        createManifest: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Issue article moved to archive successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "archiveLocation": { "type": "string" },
                "archiveId": { "type": "string" },
                "manifestPath": { "type": "string" },
                "compressionRatio": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Archive move failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to move to archive" }
            }
        }
    }
};

module.exports = {
    tags: 'Archive Management',
    description: "Move issue articles to archive storage with compression and manifest creation",
    summary: "Move to archive",
    body: moveissuearticletoarchiveBody,
    response
};