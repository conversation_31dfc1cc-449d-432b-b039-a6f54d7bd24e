const updatecustomerBody = {
    type: 'object',
    properties: {
        customer: {
            type: 'object',
            properties: {
                name: { type: 'string', minLength: 1 },
                fullName: { type: 'string', minLength: 1 },
                type: { type: 'string', default: 'journal' }
            },
            required: ['name', 'fullName']
        },
        project: {
            type: 'object',
            properties: {
                style: { type: 'string' },
                workflowTemplate: { type: 'string' },
                styleTemplate: { type: 'string' },
                tableSetterConfig: { type: 'string' },
                tableSetterCSS: { type: 'string' },
                'publisher-email': { type: 'string', format: 'email' },
                'typesetter-email': { type: 'string', format: 'email' }
            }
        }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Customer updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "customerId": { "type": "string" },
                "projectId": { "type": "string" }
            }
        }
    },
    "500": {
        "description": "Customer update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Customer Management',
    description: "Update customer and project configuration with templates and settings",
    summary: "Update customer",
    body: updatecustomerBody,
    response
};