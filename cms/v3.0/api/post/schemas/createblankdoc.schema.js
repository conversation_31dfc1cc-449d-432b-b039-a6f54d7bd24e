const createblankdocBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        templateType: { type: 'string' },
        articleType: { type: 'string' },
        journalCode: { type: 'string' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Blank document created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "string", "example": "200" },
                        "message": { "type": "string", "example": "Blank document created successfully" }
                    }
                },
                "doi": { "type": "string" },
                "stageName": { "type": "string", "example": "Pre-editing" }
            }
        }
    },
    "400": {
        "description": "Failed to create blank document",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "string", "example": "400" },
                        "message": { "type": "string" }
                    }
                }
            }
        }
    }
};

module.exports = {
    tags: 'Document Management',
    description: "Create a blank document with basic XML structure and unique IDs",
    summary: "Create blank document",
    body: createblankdocBody,
    response
};