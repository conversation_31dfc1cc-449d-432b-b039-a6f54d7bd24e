const addjobBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        title: { type: 'string' },
        authors: { type: 'array', items: { type: 'object' } },
        submissionDate: { type: 'string' },
        articleType: { type: 'string' },
        metadata: { type: 'object' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Job added successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "Job added successfully" },
                "jobId": { "type": "string", "example": "job_12345" }
            }
        }
    },
    "400": {
        "description": "Invalid job parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid job data or duplicate DOI" }
            }
        }
    }
};

module.exports = {
    tags: 'Job Management',
    description: "Add new job/article to the system with metadata",
    summary: "Add new job",
    body: addjobBody,
    response
};