const replaceworkflowBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        currentWorkflow: { type: 'string' },
        newWorkflow: { type: 'string', minLength: 1 },
        preserveHistory: { type: 'boolean', default: true },
        transferData: { type: 'boolean', default: true },
        reason: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'newWorkflow'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Workflow replaced successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "oldWorkflow": { "type": "string" },
                "newWorkflow": { "type": "string" },
                "dataTransferred": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Workflow replacement failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Workflow Management',
    description: "Replace existing workflow with new workflow while preserving history and data",
    summary: "Replace workflow",
    body: replaceworkflowBody,
    response
};