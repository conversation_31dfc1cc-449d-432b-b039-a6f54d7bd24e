const getelasticdatabydateBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        index: { type: 'string', minLength: 1 },
        dateField: { type: 'string', default: 'created_date' },
        startDate: { type: 'string', minLength: 1 },
        endDate: { type: 'string', minLength: 1 },
        size: { type: 'number', default: 100 },
        aggregateBy: { type: 'string', enum: ['day', 'week', 'month'] }
    },
    required: ['customer', 'project', 'index', 'startDate', 'endDate'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Date-filtered Elasticsearch data retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "hits": { "type": "object" },
                "dateAggregations": { "type": "object" },
                "totalCount": { "type": "number" },
                "dateRange": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve date-filtered data",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid date range or query" }
            }
        }
    }
};

module.exports = {
    tags: 'Search & Indexing',
    description: "Query Elasticsearch data filtered by date range with aggregations",
    summary: "Get Elasticsearch data by date",
    body: getelasticdatabydateBody,
    response
};