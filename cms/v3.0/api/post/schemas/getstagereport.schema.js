const getstagereportBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        stage: { type: 'string' },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        includeMetrics: { type: 'boolean', default: true },
        groupBy: { type: 'string', enum: ['stage', 'user', 'date'] },
        format: { type: 'string', enum: ['json', 'csv', 'excel'] }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Stage report retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "report": { "type": "object" },
                "metrics": { "type": "object" },
                "totalArticles": { "type": "number" },
                "averageTime": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Failed to generate stage report",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate stage report" }
            }
        }
    }
};

module.exports = {
    tags: 'Reporting',
    description: "Generate reports on article workflow stages with timing and performance metrics",
    summary: "Get stage report",
    body: getstagereportBody,
    response
};