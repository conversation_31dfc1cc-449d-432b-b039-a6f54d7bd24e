const assignuserBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        userId: { type: 'string', minLength: 1 },
        role: { type: 'string', minLength: 1 },
        stage: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } }
    },
    required: ['customer', 'project', 'doi', 'userId', 'role'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User assigned successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "message": { "type": "string", "example": "User assigned successfully" }
            }
        }
    },
    "400": {
        "description": "Invalid assignment parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid user ID or role assignment" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Assign user to article with specific role and permissions",
    summary: "Assign user to article",
    body: assignuserBody,
    response
};