const updatecopyrightBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        copyrightInfo: {
            type: 'object',
            properties: {
                holder: { type: 'string' },
                year: { type: 'number' },
                license: { type: 'string' },
                statement: { type: 'string' }
            },
            required: ['holder', 'year']
        },
        updateType: { type: 'string', enum: ['full', 'partial', 'license'] },
        validateLicense: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'copyrightInfo'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Copyright information updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "copyrightId": { "type": "string" },
                "holder": { "type": "string" },
                "year": { "type": "number" },
                "validationResults": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Copyright update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Legal',
    description: "Update copyright information with license validation",
    summary: "Update copyright",
    body: updatecopyrightBody,
    response
};