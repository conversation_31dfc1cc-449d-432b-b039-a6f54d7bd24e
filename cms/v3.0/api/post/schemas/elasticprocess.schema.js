const elasticprocessBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        operation: { type: 'string', enum: ['index', 'update', 'delete', 'reindex'] },
        indexName: { type: 'string' },
        documentType: { type: 'string' },
        bulkOperation: { type: 'boolean', default: false },
        refreshIndex: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'operation'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Elasticsearch operation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "number", "example": 200 },
                "message": { "type": "string", "example": "Document indexed successfully" },
                "documentId": { "type": "string" },
                "indexName": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Elasticsearch operation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process Elasticsearch operation" }
            }
        }
    }
};

module.exports = {
    tags: 'Search & Indexing',
    description: "Process Elasticsearch operations for article indexing and search",
    summary: "Process Elasticsearch operations",
    body: elasticprocessBody,
    response
};