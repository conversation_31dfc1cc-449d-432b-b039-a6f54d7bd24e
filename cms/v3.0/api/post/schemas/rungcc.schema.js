const rungccBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        gccType: { type: 'string', enum: ['grammar', 'style', 'comprehensive'] },
        language: { type: 'string', default: 'en' },
        strictMode: { type: 'boolean', default: false },
        includeRecommendations: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "GCC processing completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "corrections": { "type": "array" },
                "score": { "type": "number" },
                "recommendations": { "type": "array" },
                "processedContent": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "GCC processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Run Grammar and Content Checker (GCC) with language and style analysis",
    summary: "Run GCC",
    body: rungccBody,
    response
};