const publisharticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        publishDate: { type: 'string' },
        volume: { type: 'string' },
        issue: { type: 'string' },
        pageNumbers: { type: 'string' },
        publishType: { type: 'string', enum: ['online', 'print', 'both'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Article published successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "publicationUrl": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Publication',
    description: "Publish article with publication metadata",
    summary: "Publish article",
    body: publisharticleBody,
    response
};