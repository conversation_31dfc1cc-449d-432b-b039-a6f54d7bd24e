const probevalidationBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        probeType: { type: 'string', enum: ['dna', 'rna', 'protein', 'chemical'] },
        probeSequence: { type: 'string' },
        validationCriteria: { type: 'array', items: { type: 'string' } },
        strictValidation: { type: 'boolean', default: false },
        includeAnalysis: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'probeType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Probe validation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "validationResult": { "type": "boolean" },
                "validationScore": { "type": "number" },
                "issues": { "type": "array" },
                "analysis": { "type": "object" },
                "recommendations": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Probe validation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Probe validation failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Scientific Validation',
    description: "Validate scientific probes (DNA, RNA, protein, chemical) with analysis and scoring",
    summary: "Probe validation",
    body: probevalidationBody,
    response
};