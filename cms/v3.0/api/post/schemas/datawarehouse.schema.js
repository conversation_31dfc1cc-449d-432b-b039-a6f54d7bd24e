const datawarehouseBody = {
    type: 'object',
    properties: {
        customer: { type: 'string' },
        project: { type: 'string' },
        operation: { type: 'string', enum: ['sync', 'export', 'import', 'backup'] },
        dataType: { type: 'string', enum: ['articles', 'users', 'metrics', 'all'] },
        targetFormat: { type: 'string', enum: ['json', 'xml', 'csv'] },
        includeMetadata: { type: 'boolean', default: true },
        compressionEnabled: { type: 'boolean', default: false }
    },
    required: ['operation', 'dataType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Data warehouse operation completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "operationId": { "type": "string" },
                "recordsProcessed": { "type": "number" },
                "outputPath": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Data warehouse operation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid operation parameters" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Perform data warehouse operations for sync, export, import, and backup",
    summary: "Data warehouse operations",
    body: datawarehouseBody,
    response
};