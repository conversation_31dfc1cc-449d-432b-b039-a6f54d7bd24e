const issuedataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        issueType: { type: 'string', enum: ['regular', 'special', 'supplement'] },
        includeArticles: { type: 'boolean', default: true },
        generateTOC: { type: 'boolean', default: true },
        updatePageNumbers: { type: 'boolean', default: true },
        format: { type: 'string', enum: ['xml', 'json'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Issue data processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "issueXML": { "type": "string" },
                "articleCount": { "type": "number" },
                "pageCount": { "type": "number" },
                "tocGenerated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Issue data processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process issue data" }
            }
        }
    }
};

module.exports = {
    tags: 'Issue Management',
    description: "Process issue data with article compilation, TOC generation, and page numbering",
    summary: "Process issue data",
    body: issuedataBody,
    response
};