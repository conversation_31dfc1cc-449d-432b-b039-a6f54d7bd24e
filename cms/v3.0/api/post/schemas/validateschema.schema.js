const validateschemaBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        schemaType: { type: 'string' },
        xmlContent: { type: 'string' },
        validationLevel: { type: 'string', enum: ['strict', 'warning', 'info'] }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Schema validation completed",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "isValid": { "type": "boolean" },
                "errors": { "type": "array", "items": { "type": "object" } }
            }
        }
    }
};

module.exports = {
    tags: 'Validation',
    description: "Validate article XML against schema",
    summary: "Validate XML schema",
    body: validateschemaBody,
    response
};