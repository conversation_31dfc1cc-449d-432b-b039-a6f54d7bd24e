const updatereviewerBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        userMailID: { type: 'string', format: 'email' },
        reviewerStatus: { type: 'string', enum: ['accepted', 'declined', 'completed', 'waiting'] },
        reviewerComments: { type: 'string' },
        reviewerDecision: { type: 'string' },
        updateUserXML: { type: 'boolean', default: false },
        updateUserArticle: { type: 'boolean', default: false },
        processDecisionCheck: { type: 'boolean', default: false },
        version: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'userMailID'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reviewer updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "number", "example": 200 },
                        "message": { "type": "string", "example": "Reviewer updated." }
                    }
                },
                "step": { "type": "string", "example": "updateReviewer" }
            }
        }
    },
    "400": {
        "description": "Reviewer update failed",
        "schema": {
            "type": "object",
            "properties": {
                "status": {
                    "type": "object",
                    "properties": {
                        "code": { "type": "number" },
                        "message": { "type": "string" }
                    }
                },
                "step": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Reviewer Management',
    description: "Update reviewer status and information with XML and elastic updates",
    summary: "Update reviewer",
    body: updatereviewerBody,
    response
};