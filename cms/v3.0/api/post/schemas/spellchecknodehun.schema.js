const spellcheckBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string', minLength: 1 },
        language: { type: 'string', default: 'en' },
        dictionary: { type: 'string' },
        ignoreCapitalized: { type: 'boolean', default: false },
        ignoreNumbers: { type: 'boolean', default: true },
        customWords: { type: 'array', items: { type: 'string' } }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Spell check completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "errors": { "type": "array" },
                "suggestions": { "type": "object" },
                "correctedContent": { "type": "string" },
                "errorCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Spell check failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Quality Control',
    description: "Perform spell checking using Hunspell with custom dictionaries",
    summary: "Spell check",
    body: spellcheckBody,
    response
};