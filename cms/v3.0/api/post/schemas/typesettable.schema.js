const typesettableBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: { type: 'string', minLength: 1 },
        typesetFormat: { type: 'string', enum: ['pdf', 'html', 'epub'], default: 'pdf' },
        template: { type: 'string' },
        includeImages: { type: 'boolean', default: true },
        includeTables: { type: 'boolean', default: true },
        quality: { type: 'string', enum: ['draft', 'standard', 'high'], default: 'standard' }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Typesetting completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "typesetContent": { "type": "string" },
                "format": { "type": "string" },
                "fileSize": { "type": "number" },
                "pageCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Typesetting failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Processing',
    description: "Generate typeset content in various formats with quality options",
    summary: "Typeset content",
    body: typesettableBody,
    response
};