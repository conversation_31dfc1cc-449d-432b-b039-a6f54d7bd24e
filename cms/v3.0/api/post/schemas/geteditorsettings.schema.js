const geteditorsettingsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        editorId: { type: 'string' },
        settingType: { type: 'string', enum: ['preferences', 'permissions', 'workflow'] },
        includeDefaults: { type: 'boolean', default: true }
    },
    required: ['customer', 'project'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Editor settings retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "settings": { "type": "object" },
                "editorInfo": { "type": "object" },
                "lastModified": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to retrieve editor settings",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve editor settings" }
            }
        }
    }
};

module.exports = {
    tags: 'Editor Management',
    description: "Get editor-specific settings and preferences",
    summary: "Get editor settings",
    body: geteditorsettingsBody,
    response
};