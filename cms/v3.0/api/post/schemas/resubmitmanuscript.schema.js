const resubmitmanuscriptBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        resubmissionType: { type: 'string', enum: ['revision', 'correction', 'resubmission'] },
        manuscriptData: { type: 'object' },
        coverLetter: { type: 'string' },
        responseToReviewers: { type: 'string' },
        notifyEditor: { type: 'boolean', default: true },
        createNewVersion: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'resubmissionType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Manuscript resubmitted successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "newVersionId": { "type": "string" },
                "resubmissionId": { "type": "string" },
                "notificationSent": { "type": "boolean" },
                "workflowTriggered": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Manuscript resubmission failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Manuscript Management',
    description: "Resubmit manuscript with revisions, cover letter, and reviewer responses",
    summary: "Resubmit manuscript",
    body: resubmitmanuscriptBody,
    response
};