const save_noteBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        content: {
            type: 'object',
            properties: {
                content: { type: 'string', minLength: 1 },
                created: { type: 'string' }
            },
            required: ['content']
        },
        noteType: { type: 'string', enum: ['general', 'autoInviteReviewer', 'workflow', 'system'] },
        visibility: { type: 'string', enum: ['public', 'private', 'internal'], default: 'internal' },
        priority: { type: 'string', enum: ['low', 'normal', 'high'], default: 'normal' }
    },
    required: ['customer', 'project', 'doi', 'content'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Note saved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "noteId": { "type": "string" },
                "timestamp": { "type": "string" },
                "noteType": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Note save failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Notes Management',
    description: "Save notes with different types, visibility levels, and priority settings",
    summary: "Save note",
    body: save_noteBody,
    response
};