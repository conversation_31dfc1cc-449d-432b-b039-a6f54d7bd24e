const updatepurchaseorderBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        purchaseOrderData: {
            type: 'object',
            properties: {
                poNumber: { type: 'string' },
                vendor: { type: 'string' },
                amount: { type: 'number' },
                currency: { type: 'string', default: 'USD' },
                status: { type: 'string', enum: ['pending', 'approved', 'rejected', 'completed'] },
                items: { type: 'array' },
                approver: { type: 'string' },
                dueDate: { type: 'string', format: 'date' }
            },
            required: ['poNumber', 'vendor', 'amount']
        },
        updateType: { type: 'string', enum: ['create', 'update', 'approve', 'reject'] },
        sendNotification: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'purchaseOrderData'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Purchase order updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "poId": { "type": "string" },
                "poNumber": { "type": "string" },
                "amount": { "type": "number" },
                "approvalStatus": { "type": "string" },
                "notificationSent": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Purchase order update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Financial',
    description: "Update purchase order information with approval workflow and notifications",
    summary: "Update purchase order",
    body: updatepurchaseorderBody,
    response
};