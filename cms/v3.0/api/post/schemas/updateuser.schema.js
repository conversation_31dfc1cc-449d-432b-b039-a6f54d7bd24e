const updateuserBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        role: { type: 'string', enum: ['author', 'reviewer', 'editor', 'admin'] },
        status: { type: 'string' },
        affiliation: { type: 'string' },
        orcid: { type: 'string' },
        updateType: { type: 'string', enum: ['profile', 'role', 'status', 'assignment'] }
    },
    required: ['customer', 'project', 'email'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "userId": { "type": "string" },
                "updatedFields": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "User update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update user information including profile, role, and status",
    summary: "Update user",
    body: updateuserBody,
    response
};