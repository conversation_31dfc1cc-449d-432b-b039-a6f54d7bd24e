const generatedecisionletterBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        decisionType: { type: 'string', enum: ['accept', 'reject', 'revise'] },
        templateId: { type: 'string' },
        customContent: { type: 'string' },
        reviewerComments: { type: 'array' },
        editorComments: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'decisionType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Decision letter generated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "letterContent": { "type": "string" },
                "letterId": { "type": "string" },
                "generatedAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to generate decision letter",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to generate decision letter" }
            }
        }
    }
};

module.exports = {
    tags: 'Editorial Process',
    description: "Generate decision letters for manuscript review process",
    summary: "Generate decision letter",
    body: generatedecisionletterBody,
    response
};