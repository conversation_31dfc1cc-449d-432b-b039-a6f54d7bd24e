const publicationstatusBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        statusType: { type: 'string', enum: ['current', 'history', 'timeline'] },
        includeMetrics: { type: 'boolean', default: false },
        includeWorkflow: { type: 'boolean', default: true },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Publication status retrieved successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "currentStatus": { "type": "string" },
                "statusHistory": { "type": "array" },
                "workflowStage": { "type": "string" },
                "metrics": { "type": "object" },
                "timeline": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "Publication status retrieval failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to retrieve status" }
            }
        }
    }
};

module.exports = {
    tags: 'Publication Management',
    description: "Retrieve publication status with history, workflow stage, and metrics",
    summary: "Publication status",
    body: publicationstatusBody,
    response
};