const schedulereminderBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        reminderType: { type: 'string', enum: ['reviewer', 'author', 'editor', 'deadline'] },
        scheduleDate: { type: 'string', format: 'date-time' },
        recipient: { type: 'string', format: 'email' },
        template: { type: 'string' },
        customMessage: { type: 'string' },
        recurring: { type: 'boolean', default: false },
        recurringInterval: { type: 'string', enum: ['daily', 'weekly', 'monthly'] }
    },
    required: ['customer', 'project', 'doi', 'reminderType', 'scheduleDate', 'recipient'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reminder scheduled successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "reminderId": { "type": "string" },
                "scheduledDate": { "type": "string" },
                "recipient": { "type": "string" },
                "nextReminder": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Reminder scheduling failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Communication',
    description: "Schedule reminders for different user types with recurring options",
    summary: "Schedule reminder",
    body: schedulereminderBody,
    response
};