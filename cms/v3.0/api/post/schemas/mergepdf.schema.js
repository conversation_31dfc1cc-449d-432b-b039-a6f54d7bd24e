const mergepdfBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        pdfFiles: { type: 'array', items: { type: 'string' }, minItems: 2 },
        outputFileName: { type: 'string' },
        mergeOrder: { type: 'array', items: { type: 'number' } },
        addBookmarks: { type: 'boolean', default: false },
        optimizeSize: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi', 'pdfFiles'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "PDF files merged successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "mergedPdfPath": { "type": "string" },
                "fileSize": { "type": "number" },
                "pageCount": { "type": "number" },
                "mergedFiles": { "type": "array" }
            }
        }
    },
    "400": {
        "description": "PDF merge failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to merge PDF files" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Merge multiple PDF files into a single document with ordering and optimization options",
    summary: "Merge PDF files",
    body: mergepdfBody,
    response
};