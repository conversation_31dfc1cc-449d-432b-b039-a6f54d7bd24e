const reminderconfigBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        reminderType: { type: 'string', enum: ['reviewer', 'author', 'editor', 'production'] },
        schedule: {
            type: 'object',
            properties: {
                frequency: { type: 'string', enum: ['daily', 'weekly', 'monthly'] },
                interval: { type: 'number', minimum: 1 },
                daysBefore: { type: 'number' }
            }
        },
        template: { type: 'string' },
        enabled: { type: 'boolean', default: true },
        conditions: { type: 'object' }
    },
    required: ['customer', 'project', 'reminderType', 'schedule'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Reminder configuration updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "configId": { "type": "string" },
                "nextReminder": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Reminder configuration failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Configuration',
    description: "Configure reminder schedules and templates for different user types",
    summary: "Configure reminders",
    body: reminderconfigBody,
    response
};