const readdatasetBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        datasetId: { type: 'string' },
        datasetType: { type: 'string', enum: ['research', 'supplementary', 'raw', 'processed'] },
        format: { type: 'string', enum: ['json', 'csv', 'xml', 'excel'] },
        includeMetadata: { type: 'boolean', default: true },
        filterCriteria: { type: 'object' }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Dataset read successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "datasetData": { "type": "object" },
                "metadata": { "type": "object" },
                "recordCount": { "type": "number" },
                "datasetInfo": { "type": "object" }
            }
        }
    },
    "404": {
        "description": "Dataset not found",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Dataset not found" }
            }
        }
    }
};

module.exports = {
    tags: 'Data Management',
    description: "Read and retrieve dataset information with metadata and filtering options",
    summary: "Read dataset",
    body: readdatasetBody,
    response
};