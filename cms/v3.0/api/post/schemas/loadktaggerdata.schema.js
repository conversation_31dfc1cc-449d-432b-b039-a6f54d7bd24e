const loadktaggerdataBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        processKTaggerData: { type: 'string', enum: ['true', 'false'], default: 'true' },
        conversionType: { type: 'string', enum: ['presubmit', 'production'], default: 'presubmit' },
        includeResources: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "K-Tagger data loaded and processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "convertedHTML": { "type": "string" },
                "resourcesMoved": { "type": "number" },
                "preSubmitJobId": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "K-Tagger data loading failed",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "object" },
                "message": { "type": "string", "example": "one or more of mandatory inputs has not been provided" }
            }
        }
    }
};

module.exports = {
    tags: 'AI Processing',
    description: "Load and process K-Tagger AI data with HTML conversion and resource management",
    summary: "Load K-Tagger data",
    body: loadktaggerdataBody,
    response
};