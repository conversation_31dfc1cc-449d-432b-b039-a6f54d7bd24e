const copyeditingprocessBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        processType: { type: 'string', enum: ['full', 'light', 'technical'] },
        editorId: { type: 'string' },
        instructions: { type: 'string' },
        deadline: { type: 'string' },
        priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] }
    },
    required: ['customer', 'project', 'doi', 'processType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Copy editing process initiated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "processId": { "type": "string" },
                "assignedEditor": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Copy editing process failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to initiate copy editing process" }
            }
        }
    }
};

module.exports = {
    tags: 'Editorial Process',
    description: "Initiate copy editing process for articles with editor assignment",
    summary: "Start copy editing process",
    body: copyeditingprocessBody,
    response
};