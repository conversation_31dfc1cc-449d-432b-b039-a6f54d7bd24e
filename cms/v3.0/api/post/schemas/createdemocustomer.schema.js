const createdemocustomerBody = {
    type: 'object',
    properties: {
        customerName: { type: 'string' },
        userEmail: { type: 'string', format: 'email' },
        projectType: { type: 'string', default: 'kriya_books' },
        templateFile: { type: 'string' },
        cmsVersion: { type: 'string', default: 'v3.0' }
    },
    required: [],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Demo customer created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "boolean", "example": true },
                "username": { "type": "string" },
                "password": { "type": "string", "example": "Happy_Authors" },
                "customer": { "type": "string" },
                "doi": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to create demo customer",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "User already exists or creation failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Customer Management',
    description: "Create demo customer with random ID, copy configuration from template, create user account and sample article",
    summary: "Create demo customer",
    body: createdemocustomerBody,
    response
};