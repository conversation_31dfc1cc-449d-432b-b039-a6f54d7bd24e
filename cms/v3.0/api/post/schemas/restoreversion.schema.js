const restoreversionBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        versionId: { type: 'string', minLength: 1 },
        restoreType: { type: 'string', enum: ['full', 'content', 'metadata'], default: 'full' },
        createBackup: { type: 'boolean', default: true },
        reason: { type: 'string' }
    },
    required: ['customer', 'project', 'doi', 'versionId'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Version restored successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "restoredVersion": { "type": "string" },
                "backupCreated": { "type": "boolean" },
                "backupId": { "type": "string" },
                "restoreTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Version restore failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Version Control',
    description: "Restore article to a previous version with backup creation and restore options",
    summary: "Restore version",
    body: restoreversionBody,
    response
};