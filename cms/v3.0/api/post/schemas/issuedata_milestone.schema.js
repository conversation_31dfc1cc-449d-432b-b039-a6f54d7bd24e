const issuedata_milestoneBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        milestoneType: { type: 'string', enum: ['proof', 'final', 'online'] },
        createSnapshot: { type: 'boolean', default: true },
        updatePageNumbers: { type: 'boolean', default: true },
        generateProofDetails: { type: 'boolean', default: true },
        k1toK2Conversion: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Issue milestone data processed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "milestoneId": { "type": "string" },
                "snapshotCreated": { "type": "boolean" },
                "proofDetails": { "type": "object" },
                "pageNumbers": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "Issue milestone processing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to process milestone" }
            }
        }
    }
};

module.exports = {
    tags: 'Issue Management',
    description: "Process issue milestone data with proof details and K1 to K2 XML conversion",
    summary: "Process issue milestone",
    body: issuedata_milestoneBody,
    response
};