const preprocessxmlBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        xmlContent: { type: 'string' },
        preprocessingRules: { type: 'array', items: { type: 'string' } },
        validateSchema: { type: 'boolean', default: true },
        cleanupWhitespace: { type: 'boolean', default: true },
        normalizeEntities: { type: 'boolean', default: true }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "XML preprocessing completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "preprocessedXML": { "type": "string" },
                "appliedRules": { "type": "array" },
                "validationResults": { "type": "object" },
                "processingLog": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "XML preprocessing failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "XML preprocessing failed" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Preprocess XML content with validation, cleanup, and normalization rules",
    summary: "Preprocess XML",
    body: preprocessxmlBody,
    response
};