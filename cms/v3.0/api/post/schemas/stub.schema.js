const stubBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        stubType: { type: 'string', enum: ['article', 'issue', 'volume'] },
        metadata: { type: 'object' },
        template: { type: 'string' },
        autoGenerate: { type: 'boolean', default: true },
        publishImmediately: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'stubType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Stub created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "stubId": { "type": "string" },
                "stubContent": { "type": "string" },
                "published": { "type": "boolean" },
                "createdTimestamp": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Stub creation failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Content Management',
    description: "Create article, issue, or volume stubs with metadata and templates",
    summary: "Create stub",
    body: stubBody,
    response
};