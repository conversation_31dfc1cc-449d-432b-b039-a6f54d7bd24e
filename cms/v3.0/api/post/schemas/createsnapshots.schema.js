const createsnapshotsBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        snapshotType: { type: 'string', enum: ['manual', 'auto', 'scheduled'] },
        description: { type: 'string' },
        includeFiles: { type: 'boolean', default: true },
        compressionLevel: { type: 'number', default: 6 }
    },
    required: ['customer', 'project', 'doi'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Snapshot created successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "snapshotId": { "type": "string" },
                "snapshotPath": { "type": "string" },
                "createdAt": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to create snapshot",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to create snapshot" }
            }
        }
    }
};

module.exports = {
    tags: 'Version Management',
    description: "Create snapshots of article state for backup and versioning",
    summary: "Create article snapshot",
    body: createsnapshotsBody,
    response
};