const extractfrompdfBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        pdfPath: { type: 'string', minLength: 1 },
        extractionType: { type: 'string', enum: ['text', 'images', 'metadata', 'all'] },
        pageRange: { type: 'string' },
        outputFormat: { type: 'string', enum: ['xml', 'json', 'html'] }
    },
    required: ['customer', 'project', 'doi', 'pdfPath'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "PDF extraction completed successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "extractedContent": { "type": "string" },
                "extractedImages": { "type": "array" },
                "metadata": { "type": "object" }
            }
        }
    },
    "400": {
        "description": "PDF extraction failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Failed to extract content from PDF" }
            }
        }
    }
};

module.exports = {
    tags: 'Document Processing',
    description: "Extract text, images, or metadata from PDF files",
    summary: "Extract from PDF",
    body: extractfrompdfBody,
    response
};