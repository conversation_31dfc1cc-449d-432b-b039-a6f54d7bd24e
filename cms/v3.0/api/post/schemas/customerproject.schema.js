const customerprojectBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        projectName: { type: 'string', minLength: 1 },
        projectType: { type: 'string' },
        description: { type: 'string' },
        settings: { type: 'object' },
        templateId: { type: 'string' },
        isActive: { type: 'boolean', default: true }
    },
    required: ['customer', 'projectName'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Customer project created/updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "projectId": { "type": "string" },
                "projectPath": { "type": "string" }
            }
        }
    },
    "400": {
        "description": "Failed to create/update customer project",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid project parameters" }
            }
        }
    }
};

module.exports = {
    tags: 'Project Management',
    description: "Create or update customer project configuration",
    summary: "Manage customer project",
    body: customerprojectBody,
    response
};