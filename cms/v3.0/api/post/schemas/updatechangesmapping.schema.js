const updatechangesmappingBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        changesMapping: { type: 'object', minLength: 1 },
        mappingType: { type: 'string', enum: ['content', 'structure', 'metadata'] },
        version: { type: 'string' },
        trackChanges: { type: 'boolean', default: true },
        createSnapshot: { type: 'boolean', default: false }
    },
    required: ['customer', 'project', 'doi', 'changesMapping'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Changes mapping updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "mappingId": { "type": "string" },
                "changesTracked": { "type": "number" },
                "snapshotCreated": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "Changes mapping update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'Version Control',
    description: "Update changes mapping with tracking and snapshot options",
    summary: "Update changes mapping",
    body: updatechangesmappingBody,
    response
};