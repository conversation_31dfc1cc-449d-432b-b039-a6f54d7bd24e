const dashboardfiltersBody = {
    type: 'object',
    properties: {
        customer: { type: 'string' },
        project: { type: 'string' },
        filterType: { type: 'string', enum: ['stage', 'date', 'user', 'status'] },
        filterValues: { type: 'array', items: { type: 'string' } },
        dateRange: {
            type: 'object',
            properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
            }
        },
        sortBy: { type: 'string' },
        sortOrder: { type: 'string', enum: ['asc', 'desc'] }
    },
    required: ['filterType'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "Dashboard filters applied successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "filteredData": { "type": "array" },
                "totalCount": { "type": "number" }
            }
        }
    },
    "400": {
        "description": "Invalid filter parameters",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string", "example": "Invalid filter configuration" }
            }
        }
    }
};

module.exports = {
    tags: 'Dashboard',
    description: "Apply filters to dashboard data for articles and projects",
    summary: "Apply dashboard filters",
    body: dashboardfiltersBody,
    response
};