const updateuserarticleBody = {
    type: 'object',
    properties: {
        customer: { type: 'string', minLength: 1 },
        project: { type: 'string', minLength: 1 },
        doi: { type: 'string', minLength: 1 },
        email: { type: 'string', format: 'email' },
        role: { type: 'string', enum: ['author', 'reviewer', 'editor'] },
        name: { type: 'string' },
        status: { type: 'string' },
        message: { type: 'string' },
        version: { type: 'string' },
        title: { type: 'string' },
        keywords: { type: 'array', items: { type: 'string' } },
        assignedStartDate: { type: 'string', format: 'date-time' },
        assignedEndDate: { type: 'string', format: 'date-time' },
        sourceAssignment: { type: 'string' },
        to: { type: 'string', format: 'email' }
    },
    required: ['customer', 'project', 'doi', 'email', 'role'],
    additionalProperties: true
};

const response = {
    "200": {
        "description": "User article updated successfully",
        "schema": {
            "type": "object",
            "properties": {
                "status": { "type": "string", "example": "Success" },
                "elasticId": { "type": "string" },
                "eventTriggered": { "type": "boolean" }
            }
        }
    },
    "400": {
        "description": "User article update failed",
        "schema": {
            "type": "object",
            "properties": {
                "error": { "type": "string" }
            }
        }
    }
};

module.exports = {
    tags: 'User Management',
    description: "Update user-article relationship in Elasticsearch with event triggering",
    summary: "Update user article",
    body: updateuserarticleBody,
    response
};