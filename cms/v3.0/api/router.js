const express = require('express');
const validateSchema = require('./helpers/validation.helper.js');
const APILogger = require('./helpers/logging.helper.js');
const path = require('path');
const fs = require('fs');


const router = express.Router();

// Helper function to get all route files from a directory
const getRouteFiles = (dir) => {
    const routeFiles = {};
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
        if (file.endsWith('.js')) {
            const routeName = path.basename(file, '.js');
            routeFiles[routeName] = require(path.join(dir, file));
        }
    });
    
    return routeFiles;
};

// Helper function to check if schema exists
const hasSchema = (method, routeName) => {
    const schemaPath = path.join(__dirname, method.toLowerCase(), 'schemas', `${routeName}.js`);
    return fs.existsSync(schemaPath);
};

// Load route handlers
const getRoutes = getRouteFiles(path.join(__dirname, 'get'));
const postRoutes = getRouteFiles(path.join(__dirname, 'post'));

// Register GET routes
Object.entries(getRoutes).forEach(([routeName, handler]) => {
    const logger = new APILogger();
    router.get(`/${routeName}`, 
        logger.createLoggingMiddleware('GET', routeName),
        async (req, res, next) => {
            try {
                if (hasSchema('get', routeName)) {
                    await validateSchema(req);
                }
                handler[routeName](req, res);
            } catch (error) {
                res.status(error.status || 400).json(error).end();
            }
        }
    );
});

// Register POST routes
Object.entries(postRoutes).forEach(([routeName, handler]) => {
    const logger = new APILogger();
    router.post(`/${routeName}`, 
        logger.createLoggingMiddleware('POST', routeName),
        async (req, res, next) => {
            try {
                if (hasSchema('post', routeName)) {
                    await validateSchema(req);
                }
                handler[routeName](req, res);
            } catch (error) {
                res.status(error.status || 400).json(error).end();
            }
        }
    );
});

module.exports = router;
