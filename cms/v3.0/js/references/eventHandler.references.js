var tableStartTime, tableEndTime, tableId;
(function(eventHandler) {
	var messages={
		"message1":'<i class="material-icons successMarkIcon">check_circle</i><div class="bgRefTextHeadSuccess">The reference shown here is styled as per the publication styling.</div><div class="bgRefTextContent">Example: As per the style guide.Article title has to be shown in title case and user changed it to sentence, the article title will come out as title case only after going through the styling.Volume number has to be shown in bold and the user changes it to not bold, then the volume will come out as bold only after going through the styling.</div>',
		"message2":'<i class="material-icons successMarkIcon">check_circle</i><div class="bgRefTextHeadSuccess">The reference shown here is styled as per the publication styling.</div><div class="bgRefTextContent">Example: As per the style guide Article title has to be shown in title case and user changed it to sentence, the article title will come out as title case only after going through the styling. Volume number has to be shown in bold and the user changes it to not bold, then the volume will come out as bold only after going through the styling.</div>',
		"message3":'<i class="material-icons failureMarkIcon">cancel</i><div class="bgRefTextHeadFailure">We were not able to find information in Pubmed/Crossref sources.</div><div class="bgRefTextContent">The reference shown here is styled as per the publication styling. Example: As per the style guide Article title has to be shown in title case and user changed it to sentence, the article title will come out as title case only after going through the styling.Volume number has to be shown in bold and the user changes it to not bold, then the volume will come out as bold only after going through the styling.<div>'
	}
	$('body').on({
		paste: function (e) {
			e.preventDefault();
			var text = (e.originalEvent || e).clipboardData.getData('text/plain');
			if (text) {
				text = text.replace(/(<([^>]+)>)/ig, '').replace(/<(\n)*?>/g, '');
			}
			document.execCommand("insertHTML", false, text);
		}
	}, '.cslRefTemples .text-line');
	eventHandler.components.references = {
		refEditorRegex: function(){
			return /(and|edited by|editors|editor|doi|no|accessed|Vol|in|pp?|…|eds?|no|edition|available\s?(to|from))/ig;
		},
		editCslRef: function(param, repCsl, directReponse, targetNode, displayFields) {
			//$('[data-component="jrnlRefCsl_edit"]').find(".previewContent .jrnlRefText").removeAttr("retain-title-data")
			if($(targetNode).attr("data-retain-title") == "true"){
				$('input[id ="retain-title-casing"]').each(function(){
					$(this).attr('checked','checked');
				})
			}
			else{
				$('input[id ="retain-title-casing"]').each(function(){	
					$(this).removeAttr('checked');
				})
			}
			if(eventHandler && eventHandler.menu && eventHandler.menu.insert && eventHandler.menu.insert.newSpecialChar){
				$('[data-component="jrnlRefCsl_edit"] .com-spchar').attr('data-topic','insert')
				$('[data-component="jrnlRefCsl_edit"] .com-spchar').attr('data-channel','menu')
			}
			// The below function is used to edit the reference.
			if(param && param.screen3) {
				return;
			}
			if(kriya && kriya.config && kriya.config.activeElements && kriya.config.activeElements.length!=undefined &&  kriya.config.activeElements.length==1 && param && param.editorPanel && param.editorPanel=="true"){
				targetNode = kriya.config.activeElements
			}
			//set all the necessary messages on screen 
			$('[data-component="jrnlRefCsl_edit"]').closest('[data-type="popUp"]').find("#headercontent").show();
			$('[data-component="jrnlRefCsl_edit"]').find(".screen3Title").html("Edit Reference");
			$('[data-component="jrnlRefCsl_edit"]').find(".EditRefTitle").html("Edit Reference");
			$('[data-component="jrnlRefCsl_edit"]').find(".screen1").show();
			$('[data-component="jrnlRefCsl_edit"]').find(".closepopupRefJrnl").removeClass("closepopupRefJrnl");
			$(this).closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
			 var skipattrcheck = false;
			if(param && param.goto && param.goto=='screen3'){
			    skipattrcheck = true;
			}
			//set if we were able to validate the information in second screen in order to mark that as green colour.
			if(!skipattrcheck){
            $('[data-component="jrnlRefCsl_edit"]').attr('validated','false');
			}
			var targetNodeEdit = $(targetNode).clone();
			//remove all track changes information.
			var delTags = $(targetNodeEdit).find(".del");
			for(var k = 0; k < delTags.length; k++) {
				if($(delTags[k]).parent().text() == $(delTags[k]).text()) {
					$(delTags[k]).parent().remove();
				}
			}
			$(targetNodeEdit).find(".del").remove();
			$(targetNodeEdit).find(".hidden").remove();
			$(targetNodeEdit).find(".ins").contents().unwrap();
			var childNodes = $(targetNodeEdit)[0].childNodes;
			for(var i = 0; i < childNodes.length; i++) {
				$(childNodes[i]).attr("id", uuid.v4());
			}
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				
				$('[data-component="jrnlRefCsl_edit"]').find(".refContent").find(".row").each(function() {
					// cleaning up popUp
					if(!$(this).hasClass("titleText")) {
						$(this).closest(".row").remove();
					}
				});
				var doNotShowField = '[value="citation-number"][selected="selected"]';
				var nonEditableFields = '';
				if(param && param.refTypeMethod && param.refTypeMethod =="insert" &&  param.goto &&  param.goto=="screen1"){
					nonEditableFields = false;
				}else{
					nonEditableFields ='[value="comments"][selected="selected"]';
				}
				var doNotShowInDropDown = '[value="comments"]';
				if(targetNodeEdit) {
					// HIde all un necessary buttons for screen 1
					var popper = $('[data-component="jrnlRefCsl_edit"]');
					$('[data-component="jrnlRefCsl_edit"]').find(".cslReferenceOutput").html("");
					$('[data-component="jrnlRefCsl_edit"]').find(".validatedCSLRefHTML").html("");
					$('[data-component="jrnlRefCsl_edit"]').find(".validatedCSLRefMismatchHTML").html("");
					$($('[data-component="jrnlRefCsl_edit"]').find(".refBtns")).removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".refContent").removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".validated").addClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".validationFailed").addClass("hidden");
					if(popper.length > 0 && popper[0].hasAttribute("data-node-xpath")) {
						var nodeXpath = popper.attr("data-node-xpath");
						editElement = kriya.xpath(nodeXpath)[0];
					} else if(directReponse != undefined && directReponse == true) {
						editElement = targetNodeEdit;
					}
				} else {
					editElement = repCsl;
					var nodeXpath = kriya.getElementXPath(editElement);
					$('[data-component="jrnlRefText"]').attr("data-node-xpath", nodeXpath);
				}
				var prevCloneNode = "";
				$('[data-component="jrnlRefCsl_edit"]').find(".screen2hide").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".screen3hide").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".screen1hide").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".inserthide").hide();
				$('[data-component="jrnlRefCsl_edit"]').removeClass("screen1");
				$('[data-component="jrnlRefCsl_edit"]').removeClass("screen2");
				$('[data-component="jrnlRefCsl_edit"]').removeClass("screen3");
				$('[data-component="jrnlRefCsl_edit"]').removeClass("screen3");
				if(param && param.goto && param.goto == "screen2") {
					// Hide or show all un necessary buttons for screen 2
					$(targetNodeEdit).closest("[data-component]").find("#headercontent").hide();
					$('[data-component="jrnlRefCsl_edit"]').addClass("screen2");
					// prevCloneNode = $($($('[data-component="jrnlRefCsl_edit"]').find('.previewContent')).clone());
					$('[data-component="jrnlRefCsl_edit"]').find(".screen2show").show();
					$('[data-component="jrnlRefCsl_edit"]').find(".screen2show").removeClass('hidden');
					$('[data-component="jrnlRefCsl_edit"]').find(".screen2hide").hide();
					$('[data-component="jrnlRefCsl_edit"]').find(".screen3Title").html("Edit Reference - Step 2 ( Please check all new Validated Fields and run styling on it. ) ");
					// $('[data-component="jrnlRefCsl_edit"]').find('.EditRefTitle').html('Additional Validated Fields Data')
				} else if(param && param.goto && param.goto == "screen1") {
					// Hide or show all un necessary buttons for screen 1
					if(param.refTypeMethod){
						$('[data-component="jrnlRefCsl_edit"]').attr('insert',true);
						
					}else{
						$('[data-component="jrnlRefCsl_edit"]').removeAttr('insert');
						
					}
					$(".saveRefData").removeClass("closepopupRefJrnl");
					$('[data-component="jrnlRefCsl_edit"]').addClass("screen1");
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").after(prevCloneNode);
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContentClonedTemp").remove();
					$('[data-component="jrnlRefCsl_edit"]').find(".screen1show").show();
					$('[data-component="jrnlRefCsl_edit"]').find(".screen3Title").html("Edit Reference - Step 1");
					if($(kriya.config.containerElm).find('.sub-article:not([data-article-type!=""])').length > 0 && $('[data-component="jrnlRefCsl_edit"]').find('.ref-section [data-class="ref-section-selector"]').length >0 ){
						var refSelector = $('[data-component="jrnlRefCsl_edit"]').find('.ref-section [data-class="ref-section-selector"]');
						refSelector.html('<option value="">-----</option><option value="main-article">Main article</option>');
						if($(kriya.config.containerElm).find('.sub-article:not([data-article-type!=""])').length >0){
							$(kriya.config.containerElm).find('.sub-article:not([data-article-type!=""])').each(function(i){
								refSelector.append('<option value="'+$(this).attr('data-id')+'">Sub Article '+(i+1)+'</option>')
							})
							if($(targetNode).length >0 && $(targetNode).closest('.sub-article').length >0){
								closestID = $(targetNode).closest('.sub-article').attr('id');$(kriya.config.containerElm).find('.sub-article:not([data-article-type!=""])')
								$(refSelector)[0].value = closestID
							}else{
								$(refSelector)[0].value = "main-article";
							}
							refSelector.parent().removeClass('hidden')
						}else{
							refSelector.parent().addClass('hidden')
						}
						
					}
					else if($(kriya.config.containerElm).find('.back .jrnlRefGroup').length > 1 && $('[data-component="jrnlRefCsl_edit"]').find('.ref-section [data-class="ref-section-selector"]').length >0 ){
						var refSelector = $('[data-component="jrnlRefCsl_edit"]').find('.ref-section [data-class="ref-section-selector"]');
						refSelector.html('');
						var refSelFirstVal = '';
						if($(kriya.config.containerElm).find('.back .jrnlRefGroup').length >0){
							$(kriya.config.containerElm).find('.back .jrnlRefGroup .jrnlRefHead').each(function(indx){
								var clonedRefhead = $(this).clone();
								clonedRefhead.find('.del').remove();
								var refSelValText = clonedRefhead.text().trim();
								refSelector.append('<option value="'+$(this).attr('data-refgroup-id')+'">' + refSelValText +'</option>');
								if (indx == 0){
									refSelector.val(refSelValText);
								}
							})
							refSelector.parent().removeClass('hidden')
						}else{
							refSelector.parent().addClass('hidden')
						}
					}
					$('[data-component="jrnlRefCsl_edit"]').find(".EditRefTitle").html("Edit Reference");
				} else if(param && param.goto && param.goto == "screen3") {
					// Hide or show all un necessary buttons for screen 2
					$('[data-component="jrnlRefCsl_edit"]').addClass("screen3");
					$('[data-component="jrnlRefCsl_edit"]').find(".screen3show").show();
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContentClonedTemp").remove();
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".screen3Title").html("Final Styled Reference ");
					if($(targetNode).closest("[data-component]").attr('validated') && $(targetNode).closest("[data-component]").attr('validated')=='true'){
						$(targetNode).closest("[data-component]").find('.referrorMessage5') .html('We were able to find information in Pubmed/Crossref data source. Reference is styled as per customer style sheet specification. ');
					}else{
						$(targetNode).closest("[data-component]").find('.referrorMessage5') .html('We were not able to find information in Pubmed/Crossref data source. Reference is styled as per customer style sheet specification.');
					}
				}
				if($('[data-component="jrnlRefCsl_edit"]').attr('insert')) {
					// Hide or show all un necessary buttons for insert reference options
					$('[data-component="jrnlRefCsl_edit"]').find(".insertshow").removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".insertshow").show();
					$('[data-component="jrnlRefCsl_edit"]').find(".inserthide").hide();
				} 
				var clonedNode = $(editElement).clone();
				$($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).html(clonedNode);

				// Get the data-original-ref from the original element (editElement)
				var encodedOriginalRef = $(editElement).attr('data-original-ref');

				// Decode it and Show it in previewContentOrg
				if(encodedOriginalRef !== "") {
					var decodedOriginalRef = decodeURIComponent(encodedOriginalRef || '');
					if(decodedOriginalRef !== ""){
						$('[data-component="jrnlRefCsl_edit"]').find(".previewContentOrg").html(decodedOriginalRef);
					}
				}			

				var clonedNodeTemp = $($(editElement).clone());
				$($('[data-component="jrnlRefCsl_edit"]').find(".previewContentTemp")).html(clonedNodeTemp);
				$($('[data-component="jrnlRefCsl_edit"]').find(".insertNewRef")).removeClass("hidden");
				if(prevCloneNode) {
					// prevCloneNode.removeClass('hidden')
					// prevCloneNode.attr('class', 'previewContentClonedTemp')
					// $('[data-component="jrnlRefCsl_edit"]').find('.previewContent').after(prevCloneNode)
					// $('[data-component="jrnlRefCsl_edit"]').find('.previewContent').addClass('hidden')
				}
				//constructing refTypes from csl mapping
				$('[data-component="jrnlRefCsl_edit"]').find('[data-class="CslRefType"]').find("option").remove();
				var dataRefTypes = eventHandler.components.references.sortJSONRefType(mappingObj["cslRefType"])
				for(var key in dataRefTypes) {
					var newRefTypes = document.createElement("option");
					// dataRefTypes[1] - will have field
					if(dataRefTypes[key][2]) 
					$(newRefTypes).attr("value", dataRefTypes[key][2]);
					// dataRefTypes[1] - will have name 
					if(dataRefTypes[key][1]) 
					$(newRefTypes).text(dataRefTypes[key][1]);
					$('[data-component="jrnlRefCsl_edit"]').find('[data-class="CslRefType"]').append($(newRefTypes));
				}
				//GIT ID #8680 - To select the reference type for pop-up by Tamil selvan 28-Apr-2020
				var refType = "journal";
				var refTypeFound = false;
				const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
				const isAuthorWuthSuffix = $('[data-component="jrnlRefCsl_edit"]').attr('author_suffix_enabled') ? true : false;
				let temporaryDiv,prevSuffixField;
				if(editElement && $(editElement).attr("data-reftype")) {
					refType = $(editElement).attr("data-reftype");
					refTypeFound = true;
				}
				if(refTypeFound) {
					refType = $(editElement).attr("data-reftype");
				} else {
					$('[data-component="jrnlRefCsl_edit"] [data-ref-type-selector]').find("[data-if-selector]").each(function() {
						console.log($(this));
						var ifSelector = $(this).attr("data-if-selector").split("|");
						var ifIdentified = true;
						for(var x = 0, xl = ifSelector.length; x < xl; x++) {
							var noflag = false;
							if(/^!/.test(ifSelector[x])) {
								ifSelector[x] = ifSelector[x].replace("!", "");
								noflag = true;
							}
							var condition = kriya.xpath(ifSelector[x], editElement);
							if(condition.length > 0 && noflag) {
								ifIdentified = false;
							} else if(condition.length == 0 && !noflag) {
								ifIdentified = false;
							}
						}
						if(ifIdentified) {
							refType = $(this).attr("data-ref-type").toLowerCase();
							$(editElement).attr("data-reftype", refType);
						}
					});
				}
				if(mappingObj["kriyaRefTypeToCslType"][refType] != undefined) {
					refType = mappingObj["kriyaRefTypeToCslType"][refType];
				}
				if(refType && refType == "other") {
					// We are removing other type since customer style doesnt have other. So whenever other comes, map it to article-journal so that user can edit
					refType = "article-journal";
				}
				$('[data-component="jrnlRefCsl_edit"]').find(".ref-type").find("[selected]").removeAttr("selected");
				$('[data-component="jrnlRefCsl_edit"]').find(".ref-type").find("select").find('[value="' + refType + '"]').attr("selected", "selected");
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText").attr("data-reftype", refType);
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContentTemp").find(".jrnlRefText").attr("data-reftype", refType);
				let existingRefType = $('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText").attr('data-reftype');
				// if the node already has this attribute, then need not change it.
				if(!$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText").attr('data-existing-reftype')){
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText").attr("data-existing-reftype", existingRefType);
					$('[data-component="jrnlRefCsl_edit"]').find(".previewContentTemp").find(".jrnlRefText").attr("data-existing-reftype", existingRefType);
				}
        //To check key reference
				if($('[data-component="jrnlRefCsl_edit"]').find('#key-reference').length > 0){
					if($('[data-component="jrnlRefCsl_edit"]').find(".previewContentTemp").find(".jrnlRefText[data-key-reference='true']").length >0){
						$('[data-component="jrnlRefCsl_edit"]').find('#key-reference').prop("checked", true);
					}else{
						$('[data-component="jrnlRefCsl_edit"]').find('#key-reference').prop("checked", false);
					}
				}
				if($('[data-component="jrnlRefCsl_edit"]').length > 0) {
					var objEle = mappingObj["kriyaClassToUi"][refType];
					var lookUpObj = mappingObj["cslFieldsMapping"][refType];

					if(isModifyEditorPresent && lookUpObj && lookUpObj.accessed && lookUpObj.accessed.name) {
						lookUpObj.accessed.name = 'Accessed Year';
					}
	
					if(!isModifyEditorPresent && lookUpObj && lookUpObj['accessed-day']) {
						delete lookUpObj['accessed-day']
					}
	
					if(!isModifyEditorPresent && lookUpObj && lookUpObj['accessed-month']) {
						delete lookUpObj['accessed-month']
					}
					// contructing options according to csl mapping
					var optionNode = $(".cslRefTemples").find('[data-temp="dataLine"]').clone().removeAttr("data-temp");
					$(optionNode).append($(".cslRefTemples").find('[data-temp="option"]').clone().removeAttr("data-temp"));
					// $(optionNode).attr('class','row dataLine').append('<div class="col s4 refData"><span class="input-type" style="padding-left:12px;" remove-class="hidden"><select data-class="refDataType"></select></span><div>');
					// below code is used to sort the values of json  object - objEle
					var sortedObj = [];
					for(var key in objEle) {
						if(lookUpObj && objEle && objEle[key] && lookUpObj[objEle[key]] && lookUpObj[objEle[key]].name) {
							sortedObj[lookUpObj[objEle[key]].name] = objEle[key];
						}
					}
					var requiredFields = {};
					if(lookUpObj) {
						for(var key in lookUpObj) {
							if(lookUpObj && lookUpObj[key] && lookUpObj[key].validationrequired) {
								requiredFields[key] = true;
							}
						}
					}
					var sortedField = {};
					Object.keys(sortedObj).sort().forEach(function(key) {
						sortedField[key] = sortedObj[key];
					});
					for(var key in sortedField) {
						var currNode = document.createElement("option");
						$(currNode).attr("value", sortedObj[key]).text(key);
						$(optionNode).find('[data-class="refDataType"]').append(currNode);
					}
					var flagScreen3 = true;
					if(param && param.screen3) {
						flagScreen3 = false;
					}
                 
					if(isModifyEditorPresent && editElement && editElement[0]) {
						 temporaryDiv = document.createElement('div');
                        temporaryDiv.innerHTML = editElement[0].outerHTML;
					}

					if(clonedNodeTemp != undefined && clonedNodeTemp[0] != undefined && clonedNodeTemp[0].childNodes != undefined && flagScreen3) {
						// constructing fields
						$(editElement[0].childNodes).each(function(i) {
							
							// loop through all ref node and construct an edit reference box
							var currOptionNode = $(optionNode).clone();
							var currFieldVal = $(this).clone();
							let isAccessedMonthorDayForBrill = /^(RefAccesedMonth|RefAccesedDay)$/.test($(currFieldVal).attr("class")) && isModifyEditorPresent;
							if((/^(RefAccesedMonth|RefAccesedDay|RefStartMonth|RefStartDay|RefEndMonth|RefStartYear|RefEndDay|RefEndYear)$/.test($(currFieldVal).attr("class"))) && !isAccessedMonthorDayForBrill){
								return;
							}
							if(objEle[$(currFieldVal).attr("class")] != undefined) {
								if(requiredFields) {
									delete requiredFields[objEle[$(currFieldVal).attr("class")]];
								}
								if(displayFields && displayFields.length > 0 && objEle[$(currFieldVal).attr("class")] != undefined) {
									var flag = 1;
									for(var i = 0; i < displayFields.length; i++) {
										if(displayFields[i] == $(currFieldVal).attr("class")) {
											flag = 0;
										}
									}
									if(flag) {
										return;
									}
								}
								$(currOptionNode).find('[value="' + objEle[$(currFieldVal).attr("class")] + '"]').attr("selected", "selected");
								$(currOptionNode).find('[data-class="refDataType"]').attr("prev-data", objEle[$(currFieldVal).attr("class")]);
								$(currOptionNode).find('[data-class="refDataType"]').attr("require-validation", objEle[$(currFieldVal).attr("class")]);
								var currFieldNode = document.createElement("div");
								$(currFieldNode).attr("class", "col s8");
								if(objEle[$(currFieldVal).attr("class")] == "author" || objEle[$(currFieldVal).attr("class")] == "editor"  || objEle[$(currFieldVal).attr("class")] == "translator") {
									// construct author field
									$(currOptionNode).find("select").attr("data-track-id", $(currFieldVal).attr("data-track-id"));
							        var currFieldNode;
									if(isModifyEditorPresent) {
                                        currFieldNode = $(".cslRefTemples").find('[data-temp="authorFieldWithDelimiter"]').clone().removeAttr("data-temp");
									}
									else if(isAuthorWuthSuffix) {
										currFieldNode = $(".cslRefTemples").find('[data-temp="authorFieldWithSuffix"]').clone().removeAttr("data-temp");
									}
                                    else {
										currFieldNode = $(".cslRefTemples").find('[data-temp="authorField"]').clone().removeAttr("data-temp");
									}
									if(isModifyEditorPresent) {
										$(currFieldNode).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
									}

									$(currFieldNode).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
									var givenNameField = $(currFieldNode).find('[data-class="jrnlGivenName"]');
									$(givenNameField).text($(currFieldVal).find(".RefGivenName").text());
									$(givenNameField).attr("data-focusin-data", $(currFieldVal).find(".RefGivenName").text());
									$(givenNameField).attr("data-node-xpath", '//*[@id="' + $(currFieldVal).find(".RefGivenName").attr("id") + '"]');
                                    $(givenNameField).addClass("saveRefField");
                                    
                                    $(givenNameField).attr("firstFieldValue", $(currFieldVal).find(".RefGivenName").html());
                                    $(givenNameField).attr("data-focusin-data", $(currFieldVal).text());
                                    $(givenNameField).attr("data-node-xpath", "//*[@id='" + $(currFieldVal).attr("id") + "']");
                                    $(givenNameField).attr("id-data", $(currFieldVal).attr("id"));
									var surNameField = $(currFieldNode).find('[data-class="jrnlSurName"]');
									$(surNameField).text($(currFieldVal).find(".RefSurName").text());
									$(surNameField).attr("data-focusin-data", $(currFieldVal).find(".RefSurName").text());
									$(surNameField).attr("data-node-xpath", '//*[@id="' + $(currFieldVal).find(".RefSurName").attr("id") + '"]');
                                    $(surNameField).addClass("saveRefField");
                          
                                    $(surNameField).attr("firstFieldValue", $(currFieldVal).find(".RefSurName").html());
                                    $(surNameField).attr("data-focusin-data", $(currFieldVal).text());
                                    $(surNameField).attr("data-node-xpath", "//*[@id='" + $(currFieldVal).attr("id") + "']");
                                    $(surNameField).attr("id-data", $(currFieldVal).attr("id"));

									if(isAuthorWuthSuffix) {
									let suffixNameField = $(currFieldNode).find('[data-class="jrnlAuthorSuffixName"]');
									$(suffixNameField).text($(currFieldVal).find(".RefSuffix").text());
									$(suffixNameField).attr("data-focusin-data", $(currFieldVal).find(".RefSuffix").text());
									$(suffixNameField).attr("data-node-xpath", '//*[@id="' + $(currFieldVal).find(".RefSuffix").attr("id") + '"]');
                                    $(suffixNameField).addClass("saveRefField");
                          
                                    $(suffixNameField).attr("firstFieldValue", $(currFieldVal).find(".RefSuffix").html());
                                    $(suffixNameField).attr("data-focusin-data", $(currFieldVal).text());
                                    $(suffixNameField).attr("data-node-xpath", "//*[@id='" + $(currFieldVal).attr("id") + "']");
                                    $(suffixNameField).attr("id-data", $(currFieldVal).attr("id"));
									}
                                    // Add delimiter punctation and the placevalue fn helps in place the punctuation
									if(isModifyEditorPresent) {
										let targeClassName = currFieldVal[0] && currFieldVal[0].firstChild && currFieldVal[0].firstChild.className;
									    eventHandler.components.references.placeValue(currFieldNode,"jrnlDelimiter",targeClassName,temporaryDiv);
									}
                                    
                                    $('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
                                    $(currOptionNode).append($(currFieldNode));
                                    if($(currFieldNode).find('[data-validate-min="true"]').length > 0) {
                                        $(currOptionNode).find(".refData").first().append('<span class="hidden" style="display:inline-block;margin-bottom:0px;vertical-align: bottom;color:green;margin-bottom:6px"><i class="material-icons" style="font-size:20px;object-fit: cover;vertical-align: bottom;">check_circle</i></span>');
                                    }
								} else if(objEle[$(currFieldVal).attr("class")] != undefined && lookUpObj[objEle[$(currFieldVal).attr("class")]] != undefined && lookUpObj[objEle[$(currFieldVal).attr("class")]].required) {
									var currFieldNode = $(".cslRefTemples").find('[data-temp="dataField"]').clone().removeAttr("data-temp");
									if(objEle[$(currFieldVal).attr("class")] && objEle[$(currFieldVal).attr("class")]=='title'){
										currFieldNode = $(".cslRefTemples").find('[data-temp="titleField"]').clone().removeAttr("data-temp");
										$(currFieldNode).find('.retaincase').attr('style','padding-top:10px !important')
										$(currFieldNode).find('input[type=checkbox]').change(function() {
										  if ($(this).is(':checked')) {
										    $('.previewContent p').attr('data-retain-title','true')
										  } else {
											$('.previewContent p').removeAttr('data-retain-title')
										  }
										});
									}
									if(!isModifyEditorPresent && objEle[$(currFieldVal).attr("class")] && /accessed/.test(objEle[$(currFieldVal).attr("class")])){
										currFieldNode = $(".cslRefTemples").find('[data-temp="accessedField"]').clone().removeAttr("data-temp");
									}
									if(objEle[$(currFieldVal).attr("class")] && (/event-date-range/.test(objEle[$(currFieldVal).attr("class")]))){
										currFieldNode = $(".cslRefTemples").find('[data-temp="dateRangeField"]').clone().removeAttr("data-temp");
									}
									// $(currFieldNode).find('span[class="text-line"]').attr("data-validate", "true");
									if(lookUpObj[objEle[$(currFieldVal).attr("class")]] && lookUpObj[objEle[$(currFieldVal).attr("class")]].validationrequired) {
										$(currFieldNode).find('span[class="text-line"]').attr("data-validate-min", "true");
									}
									if(isModifyEditorPresent) {
                                        $(currFieldNode).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
									}
									$(currFieldNode).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
                                    $(currFieldNode).find(".text-line").addClass("saveRefField");
								$(currFieldNode).find(".text-line").first().html($(currFieldVal).html());
								$(currFieldNode).find(".text-line").first().attr("firstFieldValue", $(currFieldVal).html());
								$(currFieldNode).find(".text-line").first().attr("data-focusin-data", $(currFieldVal).text());
								$(currFieldNode).find(".text-line").first().attr("data-node-xpath", "//*[@id='" + $(currFieldVal).attr("id") + "']");
								$(currFieldNode).find(".text-line").first().attr("id-data", $(currFieldVal).attr("id"));
								$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
								$(currOptionNode).append($(currFieldNode));
								if($(currFieldNode).find('[data-validate-min="true"]').length > 0) {
									$(currOptionNode).find(".refData").first().append('<span class="hidden" style="display:inline-block;margin-bottom:0px;vertical-align: bottom;color:green;margin-bottom:6px"><i class="material-icons" style="font-size:20px;object-fit: cover;vertical-align: bottom;">check_circle</i></span>');
								}
								} else {
									var currFieldNode = $(".cslRefTemples").find('[data-temp="dataField"]').clone().removeAttr("data-temp");
									if(!isModifyEditorPresent && objEle[$(currFieldVal).attr("class")] && /accessed/.test(objEle[$(currFieldVal).attr("class")])){
										currFieldNode = $(".cslRefTemples").find('[data-temp="accessedField"]').clone().removeAttr("data-temp");
									}
									if(objEle[$(currFieldVal).attr("class")] && (/event-date-range/.test(objEle[$(currFieldVal).attr("class")]))){
										currFieldNode = $(".cslRefTemples").find('[data-temp="dateRangeField"]').clone().removeAttr("data-temp");
									}
									if(objEle[$(currFieldVal).attr("class")] && objEle[$(currFieldVal).attr("class")]=='container-author' && $(".cslRefTemples").find('[data-temp="container-author"]').length >0){
										currFieldNode = $(".cslRefTemples").find('[data-temp="container-author"]').clone().removeAttr("data-temp");
									}
									if(lookUpObj[objEle[$(currFieldVal).attr("class")]] && lookUpObj[objEle[$(currFieldVal).attr("class")]].validationrequired) {
										$(currFieldNode).find('span[class="text-line"]').attr("data-validate-min", "true");
									}
									if(isModifyEditorPresent) {
										$(currFieldNode).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
									}
									$(currFieldNode).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
                                    $(currFieldNode).find(".text-line").addClass("saveRefField");
								if(objEle[$(currFieldVal).attr("class")] === 'accessed' && !isModifyEditorPresent) {
									    let accessedFieldVal = $(editElement[0]).find('.RefAccesedDay');
										if(accessedFieldVal.length > 0) {
										$($(currFieldNode).find(".text-line").first().html($(accessedFieldVal).html()));
										$($(currFieldNode).find(".text-line").first().attr("firstFieldValue", $(accessedFieldVal).html()));
										$($(currFieldNode).find(".text-line").first().attr("data-focusin-data", $(accessedFieldVal).text()));
										$($(currFieldNode).find(".text-line").first().attr("data-node-xpath", "//*[@id='" + $(accessedFieldVal).attr("id") + "']"));
										$($(currFieldNode).find(".text-line").first().attr("id-data", $(accessedFieldVal).attr("id")));
										}
								}
								else {
                                    $(currFieldNode).find(".text-line").first().html($(currFieldVal).html());
								    $(currFieldNode).find(".text-line").first().attr("firstFieldValue", $(currFieldVal).html());
								    $(currFieldNode).find(".text-line").first().attr("data-focusin-data", $(currFieldVal).text());
								    $(currFieldNode).find(".text-line").first().attr("data-node-xpath", "//*[@id='" + $(currFieldVal).attr("id") + "']");
								    $(currFieldNode).find(".text-line").first().attr("id-data", $(currFieldVal).attr("id"));
								}
								if($(currFieldVal).attr('data-collab-abbrev')){
									$(currFieldNode).find(".text-line[data-class='RefCollabAbbrev']").html($(currFieldVal).attr('data-collab-abbrev'))
								}
								if(!isModifyEditorPresent && objEle[$(currFieldVal).attr("class")] === 'accessed' && /accessed/.test(objEle[$(currFieldVal).attr("class")]) && ($(editElement[0]).find('.RefAccesedMonth').length>0 || $(editElement[0]).find('.RefAccesedDay').length>0 || $(editElement[0]).find('.RefLAD').length>0)){
									["RefAccesedDay","RefAccesedMonth","RefLAD"].forEach(function (queryObj, currentKey) {
										let accessedFieldVal = $(editElement[0]).find('.'+queryObj);
										if(accessedFieldVal.length > 0){
											$($(currFieldNode).find(".text-line")[(currentKey)]).html($(accessedFieldVal).html());
											$($(currFieldNode).find(".text-line")[(currentKey)]).attr("firstFieldValue", $(accessedFieldVal).html());
											$($(currFieldNode).find(".text-line")[(currentKey)]).attr("data-focusin-data", $(accessedFieldVal).text());
											$($(currFieldNode).find(".text-line")[(currentKey)]).attr("data-node-xpath", "//*[@id='" + $(accessedFieldVal).attr("id") + "']");
											$($(currFieldNode).find(".text-line")[(currentKey)]).attr("id-data", $(accessedFieldVal).attr("id"));
										}
									})
								}
								if(objEle[$(currFieldVal).attr("class")] && /event-date-range/.test(objEle[$(currFieldVal).attr("class")]) && $(editElement[0]).find('.RefEventDateRange').length > 0){
									var dateMapping = ["event-start-day", "event-start-month", "event-start-year", "event-end-day", "event-end-month", "event-end-year"];
									for(var mapping in dateMapping) {
										if($(editElement[0]).find('.RefEventDateRange').attr(dateMapping[mapping])) {
											$($(currFieldNode).find('.text-line')[(mapping)]).html($(editElement[0]).find('.RefEventDateRange').attr(dateMapping[mapping]));
											$($(currFieldNode).find('.text-line')[(mapping)]).attr("firstFieldValue", $(editElement[0]).find('.RefEventDateRange').attr(dateMapping[mapping]));
											$($(currFieldNode).find('.text-line')[(mapping)]).attr("data-focusin-data", $(editElement[0]).find('.RefEventDateRange').attr(dateMapping[mapping]));
										} else {
											$($(currFieldNode).find('.text-line')[(mapping)]).html('');
											$($(currFieldNode).find('.text-line')[(mapping)]).attr("firstFieldValue", '');
											$($(currFieldNode).find('.text-line')[(mapping)]).attr("data-focusin-data", '');
										}
									}
								}
								$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
								$(currOptionNode).append($(currFieldNode));
								if($(currFieldNode).find('[data-validate-min="true"]').length > 0) {
									$(currOptionNode).find(".refData").first().append('<span class="hidden" style="display:inline-block;margin-bottom:0px;vertical-align: bottom;color:green;margin-bottom:6px"><i class="material-icons" style="font-size:20px;object-fit: cover;vertical-align: bottom;">check_circle</i></span>');
								}
								}
								
							} else {
								var currOptionNode = $(optionNode).clone();
								var currOptionNode = $(optionNode).clone();
								$(currOptionNode[0]).find("select").attr("class", "untagged");
								var untagged = $($(currOptionNode).find('[data-class="refDataType"] option')[0]).clone();
								$(untagged).attr("value", "UnTagged").attr('selected', true);;
								$(untagged).text("UnTagged");
								$(currOptionNode[0]).find("select").prepend(untagged);
								var nodeVal = this.nodeType == 3 ? this.nodeValue : "";
								var skipTrackChange = true;
								if($(this).attr("class") && ($(this).attr("class") == "del cts-1" || $(this).attr("class") == "ins cts-1")) {
									skipTrackChange = false;
								}
								if(this.nodeValue && this.nodeValue.replace(/[\“\”\.\s\,\:\;\-\–\"\)\(\)\&\[\]\‘\’]+/g, "").replace(eventHandler.components.references.refEditorRegex(), "") != "" && skipTrackChange) {
									var newFieldID = uuid.v4();
									const span = document.createElement("span");
									$(span).attr("id", newFieldID);
									$(span).attr("class", "RefComments");
									clonedNode[0].childNodes[i].after(span);
									span.appendChild(clonedNode[0].childNodes[i]);
									var currFieldNode = $(".cslRefTemples").find('[data-temp="dataField"]').clone().removeAttr("data-temp");
									$(currFieldNode).find(".text-line").html(nodeVal);
									$(currFieldNode).find(".text-line").addClass("saveRefField");
									$(currFieldNode).find(".text-line").attr("firstFieldValue", nodeVal);
									$(currFieldNode).find(".text-line").attr("data-focusin-data", nodeVal);
									$(currFieldNode).find(".text-line").attr("data-node-xpath", "//*[@id='" + newFieldID + "']");
									$(currFieldNode).find(".text-line").attr("id-data", newFieldID);
									$(currFieldNode).find(".text-line").attr("data-focusin-data", $("<data>" + nodeVal + "</data>").text());
									if(isModifyEditorPresent) {
										$(currFieldNode).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
									}
									$(currFieldNode).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
									$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
									$(currOptionNode).append($(currFieldNode));
									$(currOptionNode).attr("style", "border: solid 2px red; margin:5px !important");
									$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
								} else if(this.textContent && this.textContent.replace(/[\“\”\.\s\,\:\;\-\–\"\)\(\)\&\[\]\‘\’]+/g, "").replace(eventHandler.components.references.refEditorRegex(), "") != "" && skipTrackChange) {
									nodeVal = this.textContent;
									var newFieldID = uuid.v4();
									const span = document.createElement("span");
									$(span).attr("id", newFieldID);
									$(span).attr("class", "RefComments");
									clonedNode[0].childNodes[i].after(span);
									span.appendChild(clonedNode[0].childNodes[i]);
									var currFieldNode = $(".cslRefTemples").find('[data-temp="dataField"]').clone().removeAttr("data-temp");
									$(currFieldNode).find(".text-line").html(nodeVal);
									$(currFieldNode).find(".text-line").addClass("saveRefField");
									$(currFieldNode).find(".text-line").attr("firstFieldValue", nodeVal);
									$(currFieldNode).find(".text-line").attr("data-focusin-data", nodeVal);
									$(currFieldNode).find(".text-line").attr("data-node-xpath", "//*[@id='" + newFieldID + "']");
									$(currFieldNode).find(".text-line").attr("id-data", newFieldID);
									$(currFieldNode).find(".text-line").attr("data-focusin-data", $("<data>" + nodeVal + "</data>").text());
									if(isModifyEditorPresent) {
										$(currFieldNode).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
									}
									$(currFieldNode).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
									$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
									$(currOptionNode).append($(currFieldNode));
									$(currOptionNode).attr("style", "border: solid 2px red; margin:5px !important");
									$('[data-component="jrnlRefCsl_edit"]').find(".refContent").append($(currOptionNode));
								}
							}
							if(isModifyEditorPresent && currFieldVal.attr('class')) {
                               prevSuffixField = currFieldNode
							}
							// Assign the punctuations by using prefix sum algo
							if(isModifyEditorPresent && prevSuffixField && !currFieldVal.attr('class')) {
								let requiredField = $(prevSuffixField).find(`[data-class="jrnlSuffixField"]`);
								let punctuation = currFieldVal.text().replace(/ /g, '\u00A0')
								$(requiredField).text(punctuation);
			                    $(requiredField).attr("data-focusin-data", punctuation);
                                $(requiredField).addClass("saveRefField");
							}
						});
					}
					$('[data-component="jrnlRefCsl_edit"]').find(".refContent").removeAttr("data-ref-edited");
					$('[data-component="jrnlRefCsl_edit"]').find(doNotShowField).closest(".row").addClass("hidden");
					if(nonEditableFields){
					$('[data-component="jrnlRefCsl_edit"]').find(nonEditableFields).closest(".row").find(".refData,.saveRefField").addClass("disabled");
					$('[data-component="jrnlRefCsl_edit"]').find(nonEditableFields).closest(".row").find(".saveRefField").attr("contenteditable","false");
					$('[data-component="jrnlRefCsl_edit"]').find(nonEditableFields).closest(".row").attr("style","pointer-events: auto !important;");
					$('[data-component="jrnlRefCsl_edit"]').find(nonEditableFields).closest(".row").attr("title","This field is coming from the styling system and hence cannot be changed or edited");
					}
					$('[data-component="jrnlRefCsl_edit"]').find(doNotShowInDropDown).addClass("hidden");
					var containerId = $(editElement).attr("id");
					var cards = $('#historyDivNode .historyCard[data-content-id="' + containerId + '"]').clone(true);
					$('[data-component="jrnlRefCsl_edit"]').find(".historyTab .historyCard").remove();
					$('[data-component="jrnlRefCsl_edit"]').find(".historyTab").append(cards);
					if($('[data-component="jrnlRefCsl_edit"]').attr('ref-save-field-deleted') === 'true') {
						$('[data-component="jrnlRefCsl_edit"]').removeAttr('ref-save-field-deleted');
					}
					if($('[data-component="jrnlRefCsl_edit"]').hasClass("hidden")) {
						$('[data-component="jrnlRefCsl_edit"]').attr("data-node-xpath", nodeXpath);
						$('[data-component="jrnlRefText"]').addClass("hidden");
						kriya.popUp.openPopUps($('[data-component="jrnlRefCsl_edit"]'));
					}
					$(".editResult").removeClass("hidden");
					$(".validationResult").addClass("hidden");
					$(".ref-type").removeClass("hidden");
					$(".refContent").removeClass("hidden");
					$(".refBtns").removeClass("hidden");
					$(".referrorMessage2").addClass("hidden");
					var target = document.querySelectorAll(".saveRefField");
					for (var i = 0; i < target.length; i++) {
						target[i].addEventListener("input", function (event) {
						
							if ($(".saveRefData").hasClass("closepopupRefJrnl")) {
								return;
							}
							if(event.inputType == 'insertFromPaste'){
								var tempRef = $(this).text();
								tempRef = cleanupOnPaste(tempRef);
								$(this).text(tempRef)
							}
							eventHandler.components.references.validateRefThroughCsl({
								save: "saveToRef",
							}, $(this));
							var diffdata = htmldiff($(".previewContentTemp").html(), $(".previewContent").html());
							if (diffdata && $(".previewContent").html() && diffdata == $(".previewContent").html()) {
								$(this).closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
							} else {
								$(this).closest('[data-type="popUp"]').find(".referrorMessage2").removeClass("hidden");
							}
							// if ($('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
							// 	eventHandler.components.references.editRefscreenCSL({
							// 		goto: "screen3",
							// 		skipValidation: "true",
							// 		runCSLAutomatically: true,
							// 	}, $(this));
							// } 
							event.preventDefault();
						}, false);
					}
					
					
					if(Object.keys(requiredFields).length > 0) {
						var data = "Please tag ";
						data += "<span id='requiredFieldsvalidationTags' style='font-weight: bold; color: red; font-size: 10px; margin-top: 3px;''>" + Object.keys(requiredFields).join(", ") + "</span>";
						data += " for better validation results.";
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").html(data);
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").show();
					} else {
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").html("");
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").css("display", "none");
					}
					$(".saveRefData").attr("data-message", "{'click':{'funcToCall': 'saveReferenceData','channel':'components','topic':'references'}}");
					$(".saveRefDataScreen3").attr("data-message", "{'click':{'funcToCall': 'saveReferenceData','channel':'components','topic':'references'}}");
				}
			} else {
                kriya.removeAllNotify({'type':'reference'});
				kriya.notification({
					title: "Failed",
					type: "error",
					content: "Mapping Object file missing - Kriya config error",
					timeout: 8000,
					"notifType":"reference",
					icon: "icon-info",
				});
			}
			if($(targetNode).attr("data-anystyletagged")) {
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").attr("class", "col s5 previewContent");
				$('[data-component="jrnlRefCsl_edit"]').find(".originalReference").attr("class", "col s5 originalReference").show();
				$('[data-component="jrnlRefCsl_edit"]').find("#extraspace").show();
				$('[data-component="jrnlRefCsl_edit"]').find(".headings").show();
				$('[data-component="jrnlRefCsl_edit"]').find("#accptmsref").show();
				if(!displayFields) {
					$('[data-component="jrnlRefCsl_edit"]').find(".originalReference").html($(targetNode).attr("data-anystyletagged"));
				}
			} else {
				$('[data-component="jrnlRefCsl_edit"]').find(".originalReference").attr("class", "col s5 originalReference").hide();
				$('[data-component="jrnlRefCsl_edit"]').find("#extraspace").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".headings").hide();
				$('[data-component="jrnlRefCsl_edit"]').find("#accptmsref").hide();
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").attr("class", "previewContent");
			}
		},
		placeValue: function(currFieldNode,changeFieldClassName,targetFieldClassName,temporaryDiv) {
			if(currFieldNode && changeFieldClassName && targetFieldClassName) {
			let requiredField = $(currFieldNode).find(`[data-class="${changeFieldClassName}"]`);
			let reftargetFieldName = temporaryDiv.querySelector(`.${targetFieldClassName}`);
			// If the nextSibling doesn't present, check for the opposite author field name
			if(!reftargetFieldName.nextSibling) {
				let authorName = {
					'RefGivenName' : 'RefSurName',
					'RefSurName' : 'RefGivenName'
				}
				reftargetFieldName = temporaryDiv.querySelector(`.${authorName[targetFieldClassName]}`);
			}
            let punctuationAftertargetName = reftargetFieldName.nextSibling.textContent;
			punctuationAftertargetName = punctuationAftertargetName.replace(/ /g, '\u00A0')
			$(requiredField).text(punctuationAftertargetName);
			$(requiredField).attr("data-focusin-data", punctuationAftertargetName);
            $(requiredField).addClass("saveRefField");
			}
		},
		changeKeyReference: function(param, targetNode) {
			if(targetNode && targetNode.is(":checked")){
				$('[data-type="popUp"]').find(".previewContent .jrnlRefText").attr("data-key-reference","true")
				$('[data-type="popUp"]').find(".previewContentTemp .jrnlRefText").attr("data-key-reference","true")
			}else{
				$('[data-type="popUp"]').find(".previewContent .jrnlRefText").removeAttr("data-key-reference")
				$('[data-type="popUp"]').find(".previewContentTemp .jrnlRefText").removeAttr("data-key-reference")
			}
		},
		validateRefThroughCsl: function(param, targetNode) {
			// Funcitonlaity used for validation of data of reference and saving it.
			if($(".saveRefData").hasClass("closepopupRefJrnl")) {
				return;
			}
			if($('[data-component="jrnlRefCsl_edit"] .ref-section:not(".hidden") [data-class="ref-section-selector"]:not(".hidden")').length > 0){
				var sectionsNode = $('[data-component="jrnlRefCsl_edit"] select[data-class="ref-section-selector"]')
				var sections = $(sectionsNode).find(":selected").val();
				if(sections == ""){
					$(sectionsNode).attr('data-validate-error','true')
					return;
				}
			}
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				var editElement = "";
				var popper = $(targetNode).closest("[data-component]");
				if(param == undefined || (param && param.save != "saveToRef")) {
					if($('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').length > 0) {
						$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').remove();
					}
				}
				if(popper.length > 0 && popper[0].hasAttribute("data-node-xpath")) {
					var nodeXpath = popper.attr("data-node-xpath");
					editElement = kriya.xpath(nodeXpath);
				}
				//if editElement is come as undefined at that time code will break
				if(editElement && editElement.length > 1) {
					for(var r = 1, rl = editElement.length; r < rl; r++) {
						currTargetEle = editElement[r];
					}
				} else if(editElement) {
					var currTargetEle = $(editElement).clone();
				}
				//getting ref type
				var cslRefType = $('[data-component="jrnlRefCsl_edit"]').find(":selected").val();
				// constructing as html
				var objEle = mappingObj["kriyaClassToUi"][cslRefType];
                var cslTagToKriyaTag = kriya.config.cslMappingObj.cslTagToKriyaTag[cslRefType];
				if(objEle == undefined) {
                    kriya.removeAllNotify({'type':'reference'});
					kriya.notification({
						title: "ERROR",
						type: "error",
						content: "For <b>" + cslRefType + "</b> type reference, config not present",
						"notifType":"reference",
						icon: "icon-warning2",
					});
					return false;
				}
				var objEleKeys = Object.keys(objEle);
				const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
				if(param && /saveToRef|saveChangedToRef|acceptRef/.test(param.save)) {
					// Below code is to save the data modified in edit reference box 
					currTargetEle = $('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput p');
					if(currTargetEle.length == 0) {
						var pmidChanged = false,
							doiChanged = false;
						$(targetNode).each(function() {
							var currentNode = $(this)
							let isDelimiterOrSuffixPresent = $(currentNode).attr('data-class');
							
							var xpath = $(currentNode).attr("data-node-xpath");
							var dataElement = kriya.xpath(xpath);
							var newTag  = '';
							if($(currentNode).closest('.dataLine').find(":selected").val() && cslTagToKriyaTag[$(currentNode).closest('.dataLine').find(":selected").val()]){
								newTag = cslTagToKriyaTag[$(currentNode).closest('.dataLine').find(":selected").val()];
								if(!dataElement && (newTag=='RefLAD') && !isModifyEditorPresent){
									let newFieldID = uuid.v4();
									let dataElement = '<span class="'+$(targetNode).attr('data-class')+'" id="' + newFieldID + '">' + $(targetNode).html() + '</span>';
									$(currentNode).attr('id-data',newFieldID);
									$(currentNode).attr("data-node-xpath", '//*[@id="' + newFieldID + '"]');
									$(targetNode).closest("[data-component]").find(".previewContent").find('p').append(dataElement)
								} else if(newTag=='RefEventDateRange') {
									var nodeClassName = $(targetNode).attr('data-class');
									if($(targetNode).closest("[data-component]").find(".previewContent").find('p').find(`.${nodeClassName}`)) {
										dataElement = '';
										$(targetNode).closest('.dataLine').attr('new-field-added',"true")
										$(targetNode).closest("[data-component]").find(".previewContent").find('p').find(`.${nodeClassName}`).html($(targetNode).html())
									}else {
										let newFieldID = uuid.v4();
										let dataElement = '<span class="'+$(targetNode).attr('data-class')+'" id="' + newFieldID + '">' + $(targetNode).html() + '</span>';
										$(currentNode).attr('id-data',newFieldID);
										$(currentNode).attr("data-node-xpath", '//*[@id="' + newFieldID + '"]');
										$(targetNode).closest("[data-component]").find(".previewContent").find('p').append(dataElement)
									}
								}
								if(newTag && newTag!='RefLAD' && newTag!='RefEventDateRange'){
									$(dataElement[0]).attr("class", newTag)
								}
								if(newTag == 'RefEventDateRange' && $(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.RefEventDateRange').length > 0) {
									var attrName = objEle[$(targetNode).attr('data-class')];
									$(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.RefEventDateRange').attr(attrName,  $(targetNode).html())
								}
							}else {
								$(dataElement).attr("class", 'RefComments')
							}
						
							if($(currentNode).attr('data-class') && $(currentNode).attr('data-class') == "RefCollabAbbrev"){
								collabNode = $(currentNode).closest('.dataLine').find('*[data-class="RefCollaboration"]:not(:empty)')
								if(collabNode.length >0 && $(currentNode).html() !=""){
									$(collabNode).attr('data-collab-abbrev', $(currentNode).html());
								}else{
									$(collabNode).removeAttr('data-collab-abbrev');
								}
								currentNode = $(collabNode);
								var xpath = $(currentNode).attr("data-node-xpath");
								var dataElement = kriya.xpath(xpath);
							}
							if(dataElement && dataElement.length > 0) {
								if(dataElement && $(dataElement).attr("class") != undefined) {
									if($(dataElement).attr("class") == "RefPMID") {
										pmidChanged = $(currentNode).html();
									} else if($(dataElement).attr("class") == "RefDOI") {
										doiChanged = $(currentNode).html();
									}else if($(dataElement).attr("class") == "RefCollaboration"){										
										if($(currentNode).attr('data-collab-abbrev')){
											$(dataElement).attr('data-collab-abbrev', $(currentNode).attr('data-collab-abbrev'));
										}else{
											$(dataElement).removeAttr('data-collab-abbrev');
										}
									}
								}
								var htmlDiffData = "";
								var currfieldval = $(currentNode).attr("firstFieldValue");
								var dataClass = $(currentNode).attr("data-class");
								if(dataClass && (dataClass=='jrnlSurName' || dataClass=='jrnlGivenName' || dataClass == 'jrnlAuthorSuffixName')){
									
									
									$(currentNode).attr("firstFieldValue",$(currentNode).html())
									if(currfieldval) {
										htmlDiffData = htmldiff(currfieldval,$(currentNode).html());
									}
									if(dataClass == 'jrnlSurName'){
										if($(dataElement).find('.RefSurName').length == 0 ){
											// IF in case inout is not having sur name and user is try to add a new surname field then create a new sur name field on top
											$(dataElement).append($($($(dataElement).find('.RefGivenName')[0]).clone().attr('class','RefSurName')).html(''))
										}
										$(dataElement).find('.RefSurName').html($(currentNode).html());
									}else if(dataClass == 'jrnlGivenName'){
										if($(dataElement).find('.RefGivenName').length == 0 ){
											// IF in case inout is not having given name and user is try to add a new surname field then create a new given name field on top
											$(dataElement).append($($($(dataElement).find('.RefSurName')[0]).clone().attr('class','RefGivenName')).html(''))
										}
										$(dataElement).find('.RefGivenName').html($(currentNode).html());
									}else if(dataClass == 'jrnlAuthorSuffixName'){
										if($(dataElement).find('.RefSuffix').length == 0 ){
											// IF in case inout is not having given name and user is try to add a new suffix field then create a new given name field on top
											let existingAuthorField = $(dataElement).find('.RefGivenName') ? 'RefGivenName' : $(dataElement).find('.RefSurName') ? 'RefSurName' : null;
											if(existingAuthorField){
											$(dataElement).append($($($(dataElement).find(`.${existingAuthorField}`)[0]).clone().attr('class','RefSuffix')).html(''));
											}
										}
										$(dataElement).find('.RefSuffix').html($(currentNode).html());
									}
									
									
						
								}else if(isDelimiterOrSuffixPresent !== 'jrnlDelimiter' && isDelimiterOrSuffixPresent !== 'jrnlSuffixField'){
									$(dataElement).html($(currentNode).html())
								}
								
								diffdata = htmldiff($(".previewContentTemp").html(), $(".previewContent").html());
								if($(".previewContentTemp").text().toLowerCase() == $(".previewContent").text().toLowerCase()) {
									$(currentNode).closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
								} else {
									$(currentNode).closest('[data-type="popUp"]').find(".referrorMessage2").removeClass("hidden");
								}
							}
                            // Logic for adding suffix and delimiter
							if(isModifyEditorPresent) {
								let suffixDataPrevElementXpath = currentNode.closest('.col').prev().find(".text-line").data("node-xpath");
								let isSuffixField = $(currentNode).attr('data-class') === 'jrnlSuffixField' ? true : false;
								let isDelimiterField = $(currentNode).attr('data-class') === 'jrnlDelimiter' ? true : false;
                                if(suffixDataPrevElementXpath && isSuffixField) {
									let suffixDataElement = kriya.xpath(suffixDataPrevElementXpath);
									let isSuffixTextNodeAdded = false;
									if($(suffixDataElement)[0] && $(suffixDataElement)[0].nextSibling) 
									{ 
										isSuffixTextNodeAdded = $(suffixDataElement)[0].nextSibling.className ? true : false;
									}
									// if the next element is tag, add suffix next
									if(isSuffixTextNodeAdded){
										$(suffixDataElement).after(currentNode.text());
									}
									// if the next element is a text node, assign the value
									else{
										if($(suffixDataElement)[0].nextSibling) {
											$(suffixDataElement)[0].nextSibling.nodeValue = currentNode.text();
										}
										// if we add any suffix at last element, then it should append
										else {
											$(suffixDataElement).after(currentNode.text());
										}
									}
								}

								if(suffixDataPrevElementXpath && isDelimiterField) {
									let delimiterDataElement = kriya.xpath(suffixDataPrevElementXpath);
									let isDelimiterTextNodeAdded = $(delimiterDataElement[0]).find('.RefSurName')[0].nextSibling.className ? true : false;
									if(isDelimiterTextNodeAdded){
										$(delimiterDataElement).find('.RefSurName').after(currentNode.text());
									}
									else{
                                        $(delimiterDataElement[0]).find('.RefSurName')[0].nextSibling.nodeValue = currentNode.text();
									}
								}
							}
						});
						$('[data-type="popUp"]').find(".previewContentCloned .jrnlRefText").attr("data-reftype", $('[data-component="jrnlRefCsl_edit"] .ref-type').find('option[selected="selected"]').val())
						$('[data-type="popUp"]').find(".previewContentTemp .jrnlRefText").attr("data-reftype", $('[data-component="jrnlRefCsl_edit"] .ref-type').find('option[selected="selected"]').val())
						
						if(pmidChanged != undefined && pmidChanged) {
							$(editElement[0]).attr("data-pmid", pmidChanged.replace(/( |<([^>]+)>)/gi, ""));
						}
						if(doiChanged != undefined && doiChanged) {
							$(editElement[0]).attr("data-doi", doiChanged.replace(/( |<([^>]+)>)/gi, ""));
						}
						currTargetEle = editElement;
					}
					var refParaId = $(currTargetEle).attr("data-id");
					var refType = $(currTargetEle).attr("data-refType");
					if(refType != undefined) {
						$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").attr("data-refType", refType);
					}
					//Remove empty node from reference added by vijayakumar on 28-10-2020
					$(currTargetEle).find('*[class^="Ref"]').each(function() {
						var refHTML = $(this).html();
						refHTML = refHTML.replace(/^[\s\u00A0\u2003\u2002\u2004\u2005\u2007\u2008\u2009\u200A\u200B\u200C\u200D]+$/g, "");
						if(refHTML == "") {
							$(this).remove();
						}
					});
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").find("> .jrnlQueryRef").each(function() {
						var id = $(this).attr("id");
						if($(currTargetEle).find(".jrnlQueryRef#" + id).length == 0) {
							$(currTargetEle).prepend($(this)).html();
						}
					});
					$(currTargetEle).removeAttr("xmlns");
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").html($(currTargetEle).html());
					var reftyppe = $(currTargetEle).attr("data-reftype");
					if(reftyppe != undefined) {
						$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").attr("data-reftype", reftyppe);
					}
					if($("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").length > 0) {
						currTargetEle = $("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]");
					}
					if(currTargetEle) {
						var refId = $(currTargetEle).attr("data-id");
						// reference will not reorder if skipAuthorYearReorder is present
						let skipAuthorYearReOrder = false;
						if(citeJS && citeJS.settings && citeJS.settings.R && citeJS.settings.R.skipAuthorYearReorder) {
							skipAuthorYearReOrder = true;
						}
						if(citeJS && citeJS.settings && citeJS.settings.R && citeJS.settings.R.citationType == "1" && !skipAuthorYearReOrder) {
							var refPosition = eventHandler.components.general.getRefAlphaPosition(refId);
							if(refPosition) {
								//if response has nextId then insertBefore to the next element
								if(refPosition.nextId && $(currTargetEle).next().attr("data-id") != refPosition.nextId) {
									$(currTargetEle).insertBefore($(kriya.config.containerElm + ' .jrnlRefText[data-id="' + refPosition.nextId + '"]:not([data-track="del"])'));
									if(kriya.config.activeElements.attr("id") == $(currTargetEle).attr("id")) {
										$(currTargetEle).attr("data-moved", "true");
									}
									if(!$(currTargetEle).attr("data-inserted")) {
										$(currTargetEle)[0].scrollIntoView();
									}
								} else if(refPosition.prevId && $(currTargetEle).prev().attr("data-id") != refPosition.prevId) {
									//if response has prevId then insertAfter to the prev element
									$(currTargetEle).insertAfter($(kriya.config.containerElm + ' .jrnlRefText[data-id="' + refPosition.prevId + '"]:not([data-track="del"])'));
									if(kriya.config.activeElements.attr("id") == $(currTargetEle).attr("id")) {
										$(currTargetEle).attr("data-moved", "true");
									}
									if(!$(currTargetEle).attr("data-inserted")) {
										$(currTargetEle)[0].scrollIntoView();
									}
								}
							}
						}
					}
				}
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText").attr("data-reftype", cslRefType);
				$('[data-component="jrnlRefCsl_edit"]').find(".previewContentTemp").find(".jrnlRefText").attr("data-reftype", cslRefType);
			}
		},
		saveCSLReferenceData: function(refToReplace, editorChangedReference) {
			// Below code is to save the data modified in edit reference box 
			refToReplace = $($('.back .jrnlRefText[id="' + $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText").attr("id") + '"], .sub-back .jrnlRefText[id="' + $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText").attr("id") + '"]'));
			if(!$('[data-component="jrnlRefCsl_edit"]').hasClass("screen3"))  {
				var param = [];
				param["skipValidation"] = true;
				param["saverefdata"] = true;
				eventHandler.components.references.validatenew(param, editorChangedReference);
				return false;
			}
			eventHandler.components.references.editRefscreenCSL({
				goto: "screen3",
				skipValidation: "true",
				runCSLAutomatically: true,
			}, $(this))
		},
		saveReferenceData: function(refToReplace, editorChangedReference) {
			const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
			// Below code is to save the data modified in edit reference box 
			refToReplace = $($('.back .jrnlRefText[id="' + $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText").attr("id") + '"], .sub-back .jrnlRefText[id="' + $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText").attr("id") + '"]'));
			// if(!$('[data-component="jrnlRefCsl_edit"]').hasClass("screen3"))  {
			// 	var param = [];
			// 	param["skipValidation"] = true;
			// 	param["saverefdata"] = true;
			// 	eventHandler.components.references.validatenew(param, editorChangedReference);
			// 	return false;
			// }
			if(($(editorChangedReference).closest("[data-component]").find("[new-field-added = 'true']").length > 0 || $(editorChangedReference).closest('[data-type="popUp"]').attr('ref-save-field-deleted') === 'true') && !isModifyEditorPresent){
				if($(editorChangedReference).closest('[data-type="popUp"]').attr('ref-save-field-deleted') === 'true') {
					$(editorChangedReference).closest('[data-type="popUp"]').removeAttr('ref-save-field-deleted');
				}
				var param = [];
				param["skipValidation"] = true;
				param["saverefdata"] = true;
				eventHandler.components.references.validatenew(param, editorChangedReference);
				return false;
			}
			var editRefCurrent = $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText")
			if($(editRefCurrent).attr('data-retain-title') =='true'){
				$('input[id ="retain-title-casing"]').each(function(){
					$(this).attr('checked','checked');
				})
			}
			else{
				$('input[id ="retain-title-casing"]').each(function(){	
					$(this).removeAttr('checked');
				})
			}
			var isAuthorEmpty = true;
			if ($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']").length > 0 || $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']").length > 0) {
				var givenames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']");
				for (var i = 0; i < givenames.length; i++) {
					if ($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']")[i]).closest('.dataLine').find('.text-line:empty').length != 1) {
						isAuthorEmpty = false
					}
				}
				var surnames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']");
				for (var i = 0; i < surnames.length; i++) {
					if ($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']")[i]).closest('.dataLine').find('.text-line:empty').length != 1) {
						isAuthorEmpty = false
					}
				}
			}
			if ($(editorChangedReference).closest("[data-component]").find(".selectionRequired:visible").length > 0 || $(editorChangedReference).closest("[data-component]").find(".untagged").length > 0) {
				kriya.removeAllNotify({ 'type': 'reference' });
				kriya.notification({
					title: "ERROR",
					type: "error",
					timeout: 5000,
					content: "Please tag all the fields in edit reference box in order to proceed.",
					"notifType": "reference",
					icon: "icon-warning2",
				});
				return false;

			} else if (!isAuthorEmpty || ($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-texthint-holder!='Month'][data-texthint-holder!='Day'][data-class!='jrnlGivenName'][data-class!='jrnlSurName'][data-class!='RefCollabAbbrev'][data-class!='RefAccesedMonth'][data-class!='RefAccesedDay'][data-class!='RefStartDay'][data-class!='RefStartMonth'][data-class!='RefEndDay'][data-class!='RefEndMonth'][data-class!='RefEndYear'][data-class!='jrnlSuffixField'][data-class!='jrnlDelimiter'][data-class!='jrnlAuthorSuffixName']").length > 0)) {
				kriya.removeAllNotify({ 'notifType': 'reference' });
				kriya.notification({
					title: "ERROR",
					type: "error",
					timeout: 5000,
					content: "Empty values present in one of the fields. Please remove the field or add the value.",
					notifType: "reference",
					icon: "icon-warning2",
				});
				$(targetNode).find('option[selected="selected"]').prop('selected', true)
				return false;
			}
			$(refToReplace).removeAttr("data-untag");
			var editedReference = $(editorChangedReference).closest('[data-type="popUp"]').find(".previewContent .jrnlRefText")
			if($(editedReference).attr("data-reftype")) {
				$(refToReplace).attr("data-reftype", $(editedReference).attr("data-reftype"));
			}
			let existingRefType = $(editedReference).attr("data-existing-reftype");
			let currentRefType = $(editedReference).attr("data-reftype");
			if(existingRefType && currentRefType && existingRefType != "" && currentRefType != "" && existingRefType != currentRefType){
				$(refToReplace).removeAttr("data-existing-reftype data-doi data-pmid");
				$(editedReference).removeAttr("data-existing-reftype data-doi data-pmid");
			}
        	//To check key reference
			if($(editedReference).attr("data-key-reference")) {
				$(refToReplace).attr("data-key-reference", $(editedReference).attr("data-key-reference"));
			}else{
				$(refToReplace).removeAttr("data-key-reference");
			}

			if($(editedReference).attr("data-doi")){
				$(refToReplace).attr("data-doi", $(editedReference).attr("data-doi"));
			}else{
				$(refToReplace).removeAttr("data-doi");
			}

			if($(editedReference).attr("data-pmid")){
				$(refToReplace).attr("data-pmid", $(editedReference).attr("data-pmid"));
			}else{
				$(refToReplace).removeAttr("data-pmid");
			}
			if($(editedReference).attr("data-retain-title")){
				$(refToReplace).attr("data-retain-title", 'true');
			}else{
				$(refToReplace).removeAttr("data-retain-title");
			}
			var authorQueries = $(refToReplace).find(".jrnlQueryRef");
			$(editedReference).find(".jrnlQueryRef").remove();//remove existing quries from preview panel
				$(refToReplace).html($(editedReference).html());
			if(authorQueries.length > 0){
				$(refToReplace).prepend(authorQueries);
			}
			if($(editorChangedReference).closest("[data-component]").attr('validated') && $(editorChangedReference).closest("[data-component]").attr('validated')=='true'){
				$(refToReplace).attr("data-validated-online",'true')
			}else{
				$(refToReplace).removeAttr("data-validated-online")
			}
			$(refToReplace).attr("data-reference-edited",'true')
			if(refToReplace){
				var refId = $(refToReplace).attr('data-id');
				// reference will not reorder if skipAuthorYearReorder is present
				let skipAuthorYearReOrder = false;
				if(citeJS && citeJS.settings && citeJS.settings.R && citeJS.settings.R.skipAuthorYearReorder) {
					skipAuthorYearReOrder = true;
				}
				if(citeJS && citeJS.settings && citeJS.settings.R && citeJS.settings.R.citationType == "1" && !skipAuthorYearReOrder){
					var refPosition = citeJS.general.getRefAlphaPosition(refId);
					if(refPosition){
						//if response has nextId then insertBefore to the next element
						if(refPosition.nextId && $(refToReplace).next().attr('data-id') != refPosition.nextId){
							$(refToReplace).insertBefore($(kriya.config.containerElm + ' .jrnlRefText[data-id="' + refPosition.nextId + '"]:not([data-track="del"])'));
							if (kriya.config.activeElements.attr('id') == $(refToReplace).attr('id')){
								$(refToReplace).attr('data-moved', 'true');
							}
							if(!$(refToReplace).attr('data-inserted')){
								$(refToReplace)[0].scrollIntoView();
							}
						}else if(refPosition.prevId && $(refToReplace).prev().attr('data-id') != refPosition.prevId){
							//if response has prevId then insertAfter to the prev element
							$(refToReplace).insertAfter($(kriya.config.containerElm + ' .jrnlRefText[data-id="' + refPosition.prevId + '"]:not([data-track="del"])'));
							if (kriya.config.activeElements.attr('id') == $(refToReplace).attr('id')){
								$(refToReplace).attr('data-moved', 'true');
							}
							if(!$(refToReplace).attr('data-inserted')){
								$(refToReplace)[0].scrollIntoView();
							}
						}
					}
				}
				kriya.general.updateRightNavPanel(refId, kriya.config.containerElm);
				let screenName = /structure_content/g.test(window.location.pathname);
				if (citeJS && citeJS.settings && citeJS.settings.R && citeJS.settings.R.citationType == "1" && refId && !screenName) {
					// Updated Jquery to not include deleted nodes as before it introduced unwanted insert and delete nodes PRD-979.
					$(kriya.config.containerElm + ' [data-citation-string=" ' + refId + ' "]:not([data-track="del"])').each(function (i) {
						let isCitationEdited  = $(this).attr('data-edited-citation') || false;
						let isAuthorYearCitation = (citeJS.settings && citeJS.settings.R && citeJS.settings.R && citeJS.settings.R.retainCitationEdit) || false;
						if((!isAuthorYearCitation) || (!isCitationEdited && isAuthorYearCitation)) {
						var citeString = $(this).attr('data-citation-string');
						citeString = citeString.replace(/^\s+|\s+$/g, '').split(' ');
						var pageNum = $(this).find('.jrnlCitePageNum:not([data-track="del"]):last').text();
						var isPossessive = $(this).attr('data-possessive');

						var cloneThis = $(this).clone().cleanTrackChanges();
						cloneThis.find('[id]').removeAttr('id');
						var citeText = cloneThis.text();

						var isDirect = false;
						if ($(this).html().match(/\((?:[A-Z]+,\s?)?(\d{4}[a-z]?)|(n.d.?a?)\)/)) {
							isDirect = true;
						}
						var isFollowing = (i != 0) ? true : false;
						var abbrCollab = (citeJS.settings.R.abbrCollab && parseFloat(citeJS.settings.R.abbrCollab) <= i) ? true : false;
						var newCiteHTML = citeJS.floats.getReferenceHTML(citeString.join(' '), '', '', isDirect, '', isFollowing, isPossessive, abbrCollab, pageNum);
						var newCiteText = $('<span>' + newCiteHTML + '</span>').text();
						if (newCiteText && newCiteText != citeText) {

							$(this.childNodes).each(function () { // to add track changes while changing citation
								if (!$(this).hasClass('del') && $(this).attr('data-track') != 'del') {
									$(this).kriyaTracker('del');
								}
							});
							var newElement = document.createElement('span');
							$(newElement).append(newCiteHTML);
							$(this).append($(newElement).kriyaTracker('ins'));

							kriyaEditor.settings.undoStack.push($(this));
						}
					}
					});
					// We need to identify multiref citation whose data-citation-string can be of format data-citation-string=" R12 R13 ". Below code is written in order to identify all such citation nodes.
					var citeNode = $(kriya.config.containerElm + ' [data-citation-string]:not([data-track="del"])').filter(function(){return $(this).attr('data-citation-string').match(new RegExp(" "+refId+" ")) });
					//Below code is to loop and update all such multi ref citations by using the same function which was implemented earlier to update citations
					if($(citeNode).length > 0 && $(citeNode).attr('data-citation-string')){
						$(citeNode).each(function (i) {
							citeJS.general.updatePageNumInCitation($(this), '');
							kriyaEditor.init.addUndoLevel('save-page-no', $(this));
						});
					}
				}
			}

			kriyaEditor.settings.undoStack.push($("#contentDivNode .jrnlRefGroup").find("[id=" + refToReplace.attr("id") + "]"));
			kriyaEditor.init.addUndoLevel("style-change");
			kriya.popUp.closePopUps($("[data-component='jrnlRefCsl_edit']").closest("[data-component]"));
			// to send no param to save function in structure content to skip the pre-loader spinner - JAI #16959
			if (window.location.pathname && /structure_content/g.test(window.location.pathname)){
				kriyaEditor.init.save();
			}else{
				kriyaEditor.init.save($("#contentDivNode .jrnlRefGroup").find("[id=" + refToReplace.attr("id") + "]"), "save-ref");
			}
		},
		validatenew: function(param, targetNode) {
			// Below code is to validate the reference 
			if(param && $('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
				param["screen3"] = true;
			}
			
			var trigvalidation = '';
			if(param && param["triggerValidation"]){
				// If some kind of field or type is changes then this has to be triggered
				trigvalidation = param["triggerValidation"];
			}
			var screen3 ='';
			if(param && param.goto && param.goto=="screen3"){
				screen3 = true;
			}
			if( !screen3 && !trigvalidation  ) {
				var skipCheck = '';
				if(param && param["skipCheck"]){
					skipCheck = param["skipCheck"] 
				}

				if( !skipCheck) {
					var params = [];
					params["skipValidation"] = true;
					params["triggerValidation"] = false;
					params["skipCheck"] = true;
                    params["goto"]="screen2";
                    if(param.saverefdata){
                    	 params["saverefdata"] = param.saverefdata;
                    }
					eventHandler.components.references.validatenew(params, targetNode);
					return false;
				}
			}
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				var editElement = "";
				var popper = $(targetNode).closest("[data-component]");
				if(param == undefined || (param && param.save != "saveToRef")) {
					if($('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').length > 0) {
						$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').remove();
					}
				}
				if(popper.length > 0 && popper[0].hasAttribute("data-node-xpath")) {
					var nodeXpath = popper.attr("data-node-xpath");
					editElement = kriya.xpath(nodeXpath);
				}
				//if editElement is come as undefined at that time code will break
				if(editElement && editElement.length > 1) {
					for(var r = 1, rl = editElement.length; r < rl; r++) {
						currTargetEle = editElement[r];
					}
				} else if(editElement) {
					var currTargetEle = $(editElement).clone();
				}
				//getting ref type
				var cslRefType = $('[data-component="jrnlRefCsl_edit"]').find(":selected").val();
				// constructing as html
				var objEle = mappingObj["kriyaClassToUi"][cslRefType];
				if(objEle == undefined) {
                    kriya.removeAllNotify({'type':'reference'});
					kriya.notification({
						title: "ERROR",
						timeout: 5000,
						type: "error",
						content: "For <b>" + cslRefType + "</b> type reference, config not present",
						"notifType":"reference",
						icon: "icon-warning2",
					});
					return false;
				}
				var objEleKeys = Object.keys(objEle);
				if(param && /saveToRef|saveChangedToRef|acceptRef/.test(param.save)) {
					// The below code is to save the data directly without modfying
					currTargetEle = $('[data-component="jrnlRefCsl_edit"] .validatedCSLRefMismatchHTM p');
					var refParaId = $(currTargetEle).attr("data-id");
					var refType = $(currTargetEle).attr("data-refType");
					var dataValidateOnline = $(currTargetEle).attr("data-validated-online");
					if(refType != undefined) {
						$('.back .jrnlRefText[data-id="' + refParaId + '"]').attr("data-refType", refType);
					}
					if(dataValidateOnline != undefined) {
						$('.back .jrnlRefText[data-id="' + refParaId + '"]').attr("data-validated-online", true);
					}
					$('.back .jrnlRefText[data-id="' + refParaId + '"]').removeAttr("data-untag");
					//Remove empty node from reference added by vijayakumar on 28-10-2020
					$(currTargetEle).find('*[class^="Ref"]').each(function() {
						var refHTML = $(this).html();
						refHTML = refHTML.replace(/^[\s\u00A0\u2003\u2002\u2004\u2005\u2007\u2008\u2009\u200A\u200B\u200C\u200D]+$/g, "");
						if(refHTML == "") {
							$(this).remove();
						}
					});
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").find("> .jrnlQueryRef").each(function() {
						var id = $(this).attr("id");
						if($(currTargetEle).find(".jrnlQueryRef#" + id).length == 0) {
							$(currTargetEle).prepend($(this)).html();
						}
					});
					$(currTargetEle).removeAttr("xmlns");
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").html($(currTargetEle).html());
					var reftyppe = $(currTargetEle).attr("data-reftype");
					if(reftyppe != undefined) {
						$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").attr("data-reftype", reftyppe);
					}
					if($("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").length > 0) {
						currTargetEle = $("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]");
					}
					if(currTargetEle) {
						var refId = $(currTargetEle).attr("data-id");
						$('.back .jrnlRefText[data-id="' + refId + '"]').html(currTargetEle.html());
						if(currTargetEle.attr("data-reftype")) {
							$('.back .jrnlRefText[data-id="' + refId + '"]').attr("data-reftype", currTargetEle.attr("data-reftype"));
						}
					}
					kriyaEditor.settings.undoStack.push($("#contentDivNode .jrnlRefGroup").find("[id=" + refParaId + "]"));
					kriyaEditor.init.addUndoLevel("style-change");
					kriya.popUp.closePopUps($("[data-component='jrnlRefCsl_edit']").closest("[data-component]"));
					kriyaEditor.init.save();
				} else {
					// The below code is to validate the refrence data
					var popper = $(targetNode).closest("[data-component]");
					$(popper).find(".previewContentCloned").html($(popper).find(".previewContent").html());
					// Check if PMID or DOI is selected in drop down
					var isPMIDSelected, isDOISelected;
					for (var i = 0; i < $("[data-component]").find('.refData select').length; i++) {
						var value = $($("[data-component]").find('.refData select')[i]).val();
						if (value) {
							if (value == "PMID") {
								isPMIDSelected = true;
							} else if (value == "DOI") {
								isDOISelected = true;
							}
						}
					}
					var isAuthorEmpty = true;
					if($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']").length>0 || $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']").length>0){
						var givenames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']");
						for(var i=0;i<givenames.length;i++){
							if($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']")[i]).closest('.dataLine').find('.text-line:empty').length!=1){
								isAuthorEmpty=false
							}
						}
						var surnames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']");
						for(var i=0;i<surnames.length;i++){
							if($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']")[i]).closest('.dataLine').find('.text-line:empty').length!=1){
								isAuthorEmpty=false
							}
						}
					}
					
					//Validate event date ranges
					if($(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartYear']").length > 0) {
						var startDay = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartDay']").text(),
						startMonth = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartMonth']").text(),
						startYear = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartYear']").text(),
						endDay = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndDay']").text(),
						endMonth = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndMonth']").text(),
						endYear = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndYear']").text()

						if(startMonth && isNaN(startMonth)) {
							startMonth = monthMapping[startMonth.slice(0, 3).toLowerCase()];
						} else if(startMonth && !isNaN(startMonth) && startMonth.length == 1) {
                            startMonth = '0' + startMonth;
						}
						if(endMonth && isNaN(endMonth)) {
							endMonth = monthMapping[endMonth.slice(0, 3).toLowerCase()];
						} else if(endMonth && !isNaN(endMonth) && endMonth.length == 1) {
                            endMonth = '0' + endMonth;
						}

						if(startDay && startDay.length == 1) {
							startDay = '0' + startDay
						}
						if(endDay && endDay.length == 1) {
							endDay = '0' + endDay
						}

						var error = '';
						if (!startDay && !endDay && !startMonth && !endMonth && !startYear && !endYear) {
							error = "Empty values present in one of the fields. Please remove the field or add the value.";
						} else if(startMonth && !isNaN(startMonth) && (Number(startMonth) > 12 || Number(startMonth) < 1) || endMonth && !isNaN(endMonth)  && (Number(endMonth) > 12 || Number(endMonth) < 1)) {
							error = "Please enter a valid Month.";
						} else if((startDay && !isNaN(startDay) && (Number(startDay) > 31 || Number(startDay) < 1)) || (endDay && !isNaN(endDay)  && (Number(endDay) > 31 || Number(endDay) < 1))) {
							error = "Please enter a valid Day.";
						}
						//Check if end date is present without start date
						else if(!startDay && !startMonth && !startYear && (endDay || endMonth || endYear)) {
							error = 'Please provide a Start date before entering the End date for the Event date.'
						} else if((startDay && !startMonth) || (endDay && !endMonth)) {
							error = 'Please enter the Month for the Event date.'
						} else if ((startMonth && !startYear) || (endMonth && !endYear)) {
							error = 'Please enter the Year for the Event date.'
						} else if (startDay==endDay && startMonth==endMonth && startYear==endYear) {
							error = 'Start and end dates cannot be the same. Please correct the dates.'
						} else if (isNaN(startDay) || isNaN(endDay) || isNaN(startYear) || isNaN(endYear)){
							error = 'Day and Year should be a number.'
						} else if ((startYear && (startYear.length < 4 || startYear.length > 4 )) || (endYear && (endYear.length < 4 || endYear.length > 4))){
							error = 'Please enter a valid Year.'
						}

						if(error) {
							kriya.removeAllNotify({'type':'reference'});
							kriya.notification({
								title: "ERROR",
								type: "error",
								timeout: 5000,
								content: error,
								"notifType":"reference",
								icon: "icon-warning2",
							});
							return false;
						}
					} 

					if ($(popper).closest("[data-component]").find(".selectionRequired:visible").length > 0 || $(popper).closest("[data-component]").find(".untagged").length > 0 && !(isPMIDSelected || isDOISelected)) {
						kriya.removeAllNotify({ 'type': 'reference' });
						kriya.notification({
							title: "ERROR",
							timeout: 5000,
							type: "error",
							content: "Please tag all the fields in edit reference box in order to proceed.",
							"notifType": "reference",
							icon: "icon-warning2",
						});
						return false;
					} else if (!isAuthorEmpty || ($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-texthint-holder!='Month'][data-texthint-holder!='Day'][data-class!='jrnlGivenName'][data-class!='jrnlSurName'][data-class!='RefCollabAbbrev'][data-class!='RefAccesedMonth'][data-class!='RefAccesedDay'][data-class!='RefStartDay'][data-class!='RefStartMonth'][data-class!='RefEndDay'][data-class!='RefEndMonth'][data-class!='RefEndYear'][data-class!='jrnlSuffixField'][data-class!='jrnlDelimiter'][data-class!='jrnlAuthorSuffixName']").length > 0)) {
						kriya.removeAllNotify({ 'notifType': 'reference' });
						kriya.notification({
							title: "ERROR",
							type: "error",
							timeout: 5000,
							content: "Empty values present in one of the fields. Please remove the field or add the value.",
							notifType: "reference",
							icon: "icon-warning2",
						});
						$(targetNode).find('option[selected="selected"]').prop('selected', true)
						return false;
					}

					var popper = $(targetNode).closest("[data-component]");
						if($('[data-component="jrnlRefCsl_edit"]').find("[data-error]").length > 0) {
							return false;
						}
						// calling biblio api
						/*var refType = $(currTargetEle).attr('data-reftype');
						                                                                          var cslStyle = $('[data-component="jrnlRefCsl_edit"]').attr('data-csl-style');
						                                          	
						                                                                          var parameters = {
						                                                                              "style": cslStyle,
						                                                                              "locale": "locales-en-US.xml",
						                                                                              "type": "html",
						                                                                              "refJournalType": refType,
						                                                                              "data": $(currTargetEle)[0].outerHTML,
						                                                                              "processType": 'cslEditReference'
						                                                                          }*/
						var refID = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContentCloned")).find(".jrnlRefText").attr("data-id");
						var cslStyle = false;
						if(citeJS && citeJS.config && citeJS.config.R && citeJS.config.R.cslStyle) {
							cslStyle = citeJS.config.R.cslStyle;
							if(refID && /BR/.test(refID)){
								cslStyle = citeJS.floats.getConfig("BR")["cslStyle"];
							}
						} else if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["cslStyle"]) {
							cslStyle = citeJS.floats.getConfig("R")["cslStyle"];
							if(refID && /BR/.test(refID)){
								cslStyle = citeJS.floats.getConfig("BR")["cslStyle"];
							}
						} else {
							if(objEle == undefined) {
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Configuration of citeJS is missing",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return false;
							}
						}
						// To skip Validation of reference totally (eg: Cabi)
						if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["skipReferenceValidation"]) {
							param.skipValidation = citeJS.floats.getConfig("R")["skipReferenceValidation"];
						}
						var validationSources = "";
						if(citeJS && citeJS.config && citeJS.config.R && citeJS.config.R.validationsource) {
							validationSources = citeJS.config.R.validationsource;
						} else if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["validationsource"]) {
							validationSources =citeJS.floats.getConfig("R")["validationsource"];
						} else {
							if(objEle == undefined) {
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Configuration of citeJS is missing",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return false;
							}
						}
						var cslRefType = $(popper).find('[data-class="CslRefType"]').val();
						var journalTypeInfo = {
							Journal: "article-journal",
							Book: "book",
							Book_Editor: "book",
							Thesis: "thesis",
							Data: "dataset",
							Conference: "paper-conference",
							Software: "entry",
							Patent: "patent",
							Website: "webpage",
							Report: "report",
						};
						if(journalTypeInfo[cslRefType]) {
							$(currTargetEle).attr("type", journalTypeInfo[cslRefType]);
						} else if(cslRefType) {
							$(currTargetEle).attr("type", cslRefType.toLowerCase());
						}
						var dataToSend = $($($('[data-component="jrnlRefCsl_edit"]').find(".previewContentCloned")).clone());
						if(param && param.goto == "screen3" && !param.runCSLAutomatically) {
							dataToSend = $(".validatedCSLRefMismatchHTM");
						}
						if(param && param.goto && param.goto=="screen2") {
							dataToSend.find("p").attr("retain-authors",true);
						}
						dataToSend.find("[data-validated-online]").attr("data-retain-color",true);
						dataToSend.find("[data-validated-online]").removeAttr("data-validated-online");
						var httpContent = '<validate projectname="' + kriya.config.content.customer + '">' + dataToSend.html() + "</validate>";
						var parameters = {
							processType: "cslEditReference",
							customer: kriya.config.content.customer,
							project: kriya.config.content.project,
							data: httpContent,
							cslType: "validate",
							cslStyle: cslStyle,
							type: "HTML",
							outputformat: "HTML",
						};
						var newSlNo = false;
						if($('#contentDivNode [data-id="' + refID + '"]').find(".RefSlNo").length > 0) {
							var slNo = $('#contentDivNode [data-id="' + refID + '"]').find(".RefSlNo").clone(true);
						} else if(refID) {
							//added by kirankumar:-issue:when insert new reference it showing refid as 1
							var slNo = refID.replace(/[A-Z]+/g, "");
							newSlNo = slNo;
						}
						if(newSlNo) {
							parameters["slNo"] = newSlNo;
						} else {
							slNo = refID.replace(/[A-Z]+/g, "");
							parameters["slNo"] = slNo;
						}
						if(param && param.skipValidation) {
							parameters["skipValidation"] = true;
						}
						if(param && param.goto && param.goto=="screen2") {
							dataToSend.find("p").attr("retain-authors",true);
						}
						if($('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').length > 0) {
							$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').remove();
						}
						if(validationSources){
							parameters["validationSources"] = validationSources;
						}
						$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').html("");
						popper = $(targetNode).closest("[data-component]");
						var nodeXpth = popper.attr("data-node-xpath");
						editElement = $(kriya.xpath(nodeXpth)[0]);
						currTargetEle = $(editElement).clone();
						if(currTargetEle.length > 0) {
							$(popper).find(".refContent [data-node-xpath]").each(function() {
								var xpath = $(this).attr("data-node-xpath");
								var dataElement = kriya.xpath(xpath, currTargetEle);
								if(dataElement.length > 0) {
									$(dataElement[0]).text($(this).text());
								}
							});
						}
						var originalRef = parameters["data"];
						if(param && param.skipValidation && !param.runCSLAutomatically) {
							popper.progress("Applying customer style sheet specifications on reference.");
						} else if(param && !param.runCSLAutomatically) {
							popper.progress("Trying to Validate the data and fetch new fields");
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").removeClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").show();
							$('[data-component="jrnlRefCsl_edit"]').find(".accept").addClass("disabled");
						}
						parameters["newSCStageEditor"] = true;
						var paramData = $(parameters.data);
						var unwrapps = $(paramData.find("p").find(".punctuation"));
						for(var k = 0; k < unwrapps.length; k++) {
							unwrapps[k].replaceWith(", ");
						}
						$(paramData).find(".del").remove();
						$(paramData).find(".ins").contents().unwrap();
						$(paramData).find('p').removeAttr("data-validated-online");
						//If no DOI is present in the reference then remove data-doi attribute
						let isDoiPresent = $(paramData).find('p').has('.RefDOI').length > 0
						if(!isDoiPresent){
							$(paramData).find('p').removeAttr('data-doi');
						}
						let existingRefType = $(paramData).find('p').attr('data-existing-reftype');
						let currentRefType = $(paramData).find('p').attr('data-reftype');
						if(existingRefType && currentRefType && existingRefType != "" && currentRefType != "" && existingRefType != currentRefType){
							$(paramData).find('p').removeAttr("data-existing-reftype data-doi data-pmid");
						}
						parameters.data = paramData.html();
						// To skip Reference Validation [Req: Cabi]
						if(citeJS && citeJS.cofig && citeJS.cofig.R && citeJS.cofig.R.skipReferenceValidation) {
							parameters['skipValidation'] = true;
						}
						// Overriding csl if language is configured [eg: SUP]
						const container = document.getElementById("contentContainer");
						if (container) {
							const articleLanguage = container.getAttribute("data-articleLanguage-csl");
							if (articleLanguage) {
							  parameters.cslStyle = articleLanguage
							} else {
							  parameters.cslStyle = cslStyle
							}
						  }
						// API call to validate the data to be made here
						kriya.general.sendAPIRequest("process_reference", parameters, function(res) {
							if(param && !param.runCSLAutomatically) {
								// when user modiffied some info in the fiend then show this message
								popper.progress("Rendering results");
							} else {
								$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").addClass("hidden");
								$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").hide();
								$('[data-component="jrnlRefCsl_edit"]').find(".accept").removeClass("disabled");
							}
							if(res && res != "" && res != "error") {
								$(".validationResult table").closest(".validationResult").removeClass("hidden");
								if(res.body) {
									var response = $("<div>" + res.body + "</div>");
									var responseQuery = $("<div>" + res.MismatchedHTML + "</div>");
									if(res.slNo) {
										// If serial number alone present in the reference, throw warning
										if(response.find("p") && response.find("p").length > 0 && response.find("p")[0].attributes && response.find("p")[0].attributes['data-reference-empty'] && response.find("p")[0].attributes['data-reference-empty'].value) {
											$(".validationResult table").closest(".validationResult").addClass("hidden");
											popper.progress("", true);
											kriya.removeAllNotify({'type':'reference'});
							                kriya.notification({
								               title: "ERROR",
								               type: "error",
								               timeout: 5000,
								               content: "Insufficient information provided, Please add more information",
								               "notifType":"reference",
								               icon: "icon-warning2",
							                });
							               return;
										}
										 var skipattrcheck = false;
										 if(param && param.goto && param.goto=='screen3'){
										 	skipattrcheck = true;
										 }
										if($(res.body).attr('data-validated-online') && !skipattrcheck ){
											popper.attr('validated','true');
										}
										response.find("p").attr("class", "jrnlRefText");
										//response.find('p').attr('id', res.slNo);
										responseQuery.find("p").attr("class", "jrnlRefText querydata");
										//responseQuery.find('p').attr('id', res.slNo);
										responseQuery.find("p").removeAttr("xmlns");
									}
									var refID = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).find(".jrnlRefText").attr("data-id");
									var reftype = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).find(".jrnlRefText").attr("data-reftype");
									var validatedReference = $(res.body);
									validatedReference.attr("data-id", refID);
									validatedReference.attr("data-reftype", reftype);
									validatedReference.removeAttr("xmlns");
									$(popper).find(".ref-type").addClass("hidden");
									var displayNewFields = [];
									popper.progress("", true);
									$(popper).find("[data-wrapper], .refBtns, .refContent").addClass("hidden");
									$(popper).find(".validateCancel").removeClass("hidden");
									$(".validatedCSLRefHTML").addClass("hidden").html("<div>" + $(response).find(".jrnlRefText")[0].outerHTML + "</div>");
									$(".validatedCSLRefHTML").find(".jrnlRefText").attr("class", "jrnlRefText");
									$(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-id", refID);
									$(responseQuery).find(".jrnlRefText").attr("data-id", refID);
									if($(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-doi") != undefined) {
										$(".validatedCSLRefHTML").prepend('<p>DOI: <a target="_blank" href="http://dx.doi.org/' + $(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-doi") + '">' + $(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-doi") + "</a></p>");
									}
									if($(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-pmid") != undefined) {
										$(".validatedCSLRefHTML").prepend('<p>PubMed: <a target="_blank" href="https://pubmed.ncbi.nlm.nih.gov/' + $(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-pmid") + '">' + $(".validatedCSLRefHTML").find(".jrnlRefText").attr("data-pmid") + "</a></p>");
									}
									if(param && param.validateInsertNewRef != undefined) {
										$(".validatedCSLRefHTML").append("<p style=\"text-align: center;\"><span class=\"btn btn-medium\" data-message=\"{'click':{'funcToCall': 'insertReferenceData','channel':'components','topic':'reference','param':{'save': 'saveToRef','insertref':true}}}\"> Insert reference </span></p>");
									} else {
										$(".validatedCSLRefHTML").append("<p style=\"text-align: center;\"><span class=\"btn btn-medium\" data-message=\"{'click':{'funcToCall': 'validateRefThroughCsl','channel':'components','topic':'reference','param':{'save': 'saveToRef'}}}\"> Accept the reference </span></p>");
									}
									$(".validatedCSLRefMismatchHTML").addClass("hidden");
									$(".validatedCSLRefMismatchHTML").html("");
									$(".truecaseHTML").html("");
									if(res.MismatchedHTML) {
										// display the info in edit ref popup
										var source = "";
										if(res.difference && res.difference["mismatch"]) {
											var mismatch = res.difference["mismatch"];
											if(Object.keys(mismatch)[0] && mismatch[Object.keys(mismatch)[0]] && mismatch[Object.keys(mismatch)[0]].source) source = mismatch[Object.keys(mismatch)[0]].source;
										}
										$(".validatedCSLRefMismatchHTML").append('<p style="text-align: left;" class="querydata">While validating this bibliographic reference against ' + source + ", we found some information which was different.</p>");
										$(".validatedCSLRefMismatchHTML").append(htmldiff($(response).find(".jrnlRefText")[0].outerHTML, $(responseQuery).find(".jrnlRefText")[0].outerHTML));
										$(".validatedCSLRefMismatchHTML").append('<div class="cslChangedReferenceOutput hidden">' + $(responseQuery).find(".jrnlRefText")[0].outerHTML + "</div>");
										if(param && param.validateInsertNewRef != undefined) {
											$(".validatedCSLRefMismatchHTML").append('<p style="text-align: left;" class="querydata">If you would like to insert this reference, please click "Insert the changed reference".</p>');
											$(".validatedCSLRefMismatchHTML").append("<p style=\"text-align: center;\"><span class=\"btn btn-medium\" data-message=\"{'click':{'funcToCall': 'insertReferenceData','channel':'components','topic':'reference','param':{'insertMismatchRef': true}}}\">Insert the changed reference</span></p>");
										} else {
											$(".validatedCSLRefMismatchHTML").append('<p style="text-align: left;" class="querydata">If you would like to use this reference, please click "Accept the change". If you would like to raise it as query, please click on "Raise as a query"</p>');
											$(".validatedCSLRefMismatchHTML").append("<p style=\"text-align: center;\"><span class=\"btn btn-medium\" data-message=\"{'click':{'funcToCall': 'validateRefThroughCsl','channel':'components','topic':'reference','param':{'save': 'saveChangedToRef'}}}\">Accept the change</span><span class=\"btn btn-medium orange\" data-message=\"{'click':{'funcToCall': 'raiseQuery','channel':'components','topic':'reference'}}\">Raise as a query</span></p>");
										}
										if(res.truecaserChanges && res.truecaserChanges[0]) {
											$(".truecaseHTML").append('<p style="text-align: left;" class="querydata">The below are some of the casing changes suggested in the reference</p>');
											if(res.truecaserChanges[0].titleMessage) $(".truecaseHTML").append('<p style="text-align: left;" class="querydataTitle">' + res.truecaserChanges[0].titleMessage + '<span class="title hidden">' + res.truecaserChanges[0].title + "</span></p>");
											if(res.truecaserChanges[0]["container-titleMessage"]) $(".truecaseHTML").append('<p style="text-align: left;" class="querydataContTit">' + res.truecaserChanges[0]["container-titleMessage"] + '<span class="containerTitle hidden">' + res.truecaserChanges[0]["container-title"] + "</span></p>");
											$(".truecaseHTML").append('<p style="text-align: left;" class="querydata">If you would like to apply the change, please click "Accept changes". If you would like to edit the reference, please click on "Edit reference"</p>');
										}
										$(".validatedCSLRefMismatchHTM").html($(".validatedCSLRefMismatchHTML .cslChangedReferenceOutput .jrnlRefText")[0]);
									} else {
										$(".validatedCSLRefMismatchHTM").html($($(".validatedCSLRefHTML  .jrnlRefText")[0]).clone());
									}
									$(".validatedCSLRefHTM").html($($('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find(".jrnlRefText")[0]).clone());
									// Below lin of code is to remove the highlighting done by css for activeElement.
									$(".validatedCSLRefHTM").find(".jrnlRefText ").removeClass("activeElement");
									$(".validatedCSLRefMismatchHTML").append("<p style=\"text-align: center;\"><span class=\"btn btn-medium\" data-message=\"{'click':{'funcToCall': 'validateBack','channel':'components','topic':'reference'}}\">Back</span></p>");
									//to save the reference after validating when inserting a new reference, instead displaying to the user which will be done at the time of validating an existing reference - jai 19-06-2017
									popper.progress("", true);
									if(!popper.find('.popupHead[data-type="new"]').hasClass("hidden")) {
										//eventHandler.components.reference.saveReference(param, targetNode);
									}
									$(".editResult").addClass("hidden");
									let isRetainArticleTitle = false;
									if($('[data-component="jrnlRefCsl_edit"]') && $('[data-component="jrnlRefCsl_edit"]').find(".previewContent") && $('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find('.jrnlRefText') && $('[data-component="jrnlRefCsl_edit"]').find(".previewContent").find('.jrnlRefText').attr('data-retain-title')) {
										isRetainArticleTitle = true;
									}
									if(res.changes != undefined && res.changes.length != undefined && res.changes.length > 0) {
										// The below code is to display all changes in a table
										$(".validationResult").removeClass("hidden");
										var newElement = $('<tr> <th class="tablerefrow" style="border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important; font-weight: normal">Type</th> <th class="tablerefrow" style="border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important; font-weight: normal">Original Reference Data</th> <th style="border: none !important; font-weight: normal;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" class="tablerefrow">Final Reference Data</th> <th style="border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important; font-weight: normal" class="tablerefrow">Source</th> <th style="border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important;font-weight: normal" class="tablerefrow" >Accept</th> <th style="border: none !important; border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important; font-weight: normal !important" class="tablerefrow" > Reject</th></tr>');
										$(".reftable").html("<tr>" + newElement.html() + "</tr>");
										for(var i = 0; i < res.changes.length; i++) {
											if(res.changes[i] && res.changes[i].changeType && res.changes[i].changeType == "modified") {
												// The below code is to display all changes in a table if a field is modified
												if(res.changes[i]["changed"] && $(res.changes[i]["changed"]).attr("class")) {
													displayNewFields.push($(res.changes[i]["changed"]).attr("class"));
												}
												//For article title, if retain edit is enabled and if there are any changes fetched from pubmed/crossref, the reject option should be enabled
												if($(res.changes[i]["changed"]) && $(res.changes[i]["changed"]).attr("class") && $(res.changes[i]["changed"]).attr("class").toLowerCase() === 'refarticletitle' && isRetainArticleTitle) {
													let originalArticleTitle = res.changes[i]["actual"] && $(res.changes[i]["actual"]).html() ? $(res.changes[i]["actual"]).html() : "";
													$(".validatedCSLRefMismatchHTM").find('.RefArticleTitle').text(originalArticleTitle);
													$(".validatedCSLRefMismatchHTM").find('.RefArticleTitle').attr('ignoreDataselect','true');
													newElement = $(' <tr > <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["csltype"] + '</th><th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["actual"] + '</th> <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["changed"] + '</th> <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">Different field found in ' + res.changes[i]["source"] + '</th> <th style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" class="tablerefrow" data-tooltip="Accept the change" refSelect="select" currentselection="true"> <i class="material-icons md1" style="font-size: 25px">check_circle_outline</i> </th> <th class="tablerefrow" style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" data-tooltip="Reject the change" refSelect="reject"> <i class="material-icons md1" style="font-size: 25px">cancel</i> </th> </tr>');
												}
												else {
												newElement = $(' <tr > <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["csltype"] + '</th><th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["actual"] + '</th> <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["changed"] + '</th> <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">Different field found in ' + res.changes[i]["source"] + '</th> <th style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" class="tablerefrow" data-tooltip="Accept the change" refSelect="select"> <i class="material-icons md1" style="font-size: 25px"> check_circle </i> </th> <th class="tablerefrow" style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" data-tooltip="Reject the change" refSelect="reject"> <i class="material-icons md1" style="font-size: 25px" currentselected="true"> close </i> </th> </tr>');
												}
												$(".reftable").append("<tr>" + newElement.html() + "</tr>");
											} else if(res.changes[i] && res.changes[i].changeType && res.changes[i].changeType == "inserted") {
												// The below code is to display all changes in a table if a field is inserted
												if(res.changes[i]["inserted"] && $(res.changes[i]["inserted"]).attr("class")) {
													displayNewFields.push($(res.changes[i]["inserted"]).attr("class"));
												}
												newElement = $(' <tr ><th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["csltype"] + '</th> <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">  </th> <th class="tablerefrow"  style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["inserted"] + '</th>  <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important"> Inserted by ' + res.changes[i]["contentSource"] + '</th> <th style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" class="tablerefrow " data-tooltip="Accept the change" refSelect="select"> <i class="material-icons md1 insertedData" style="font-size: 25px"> check_circle </i> </th> <th class="tablerefrow " style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" data-tooltip="Reject the change" refSelect="reject"> <i class="material-icons md1 insertedData" style="font-size: 25px" currentselected="true"> close </i> </th> </tr>');
												$(".reftable").append("<tr>" + newElement.html() + "</tr>");
											} else if(res.changes[i] && res.changes[i].changeType && res.changes[i].changeType == "deleted") {
												// The below code is to display all changes in a table if a field is deleted
												if(res.changes[i]["deleted"] && $(res.changes[i]["deleted"]).attr("class")) {
													displayNewFields.push($(res.changes[i]["deleted"]).attr("class"));
												}
												newElement = $(' <tr > <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["csltype"] + '</th> <th class="tablerefrow"  style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">' + res.changes[i]["deleted"] + '</th><th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">  </th>  <th class="tablerefrow" style="font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important">Deleted by Styling System</th> <th style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" class="tablerefrow " data-tooltip="Accept the change" refSelect="select"> <i class="material-icons md1 deletedData" style="font-size: 25px"> check_circle </i> </th> <th class="tablerefrow " style=" cursor: pointer;font-weight: normal !important;border: none !important;border: solid #D3D3D3 2px !important; padding-top: 10px !important;padding-bottom: 10px !important" data-tooltip="Reject the change" refSelect="reject"> <i class="material-icons md1 deletedData" style="font-size: 25px" currentselected="true"> close </i> </th> </tr>');
												$(".reftable").append("<tr>" + newElement.html() + "</tr>");
											}
										}
									}
									$("[data-tooltip]").tooltip({
										delay: 30,
										effect: "toggle",
									});
									$(".validatedCSLRefHTM").parent().removeClass("hidden");
									$(".validatedCSLRefHTM").removeClass("hidden");
									// Hiding or showing the necessary html tags.
									// When insert new reference is done, UI is different.
									if(param && param.validateInsertNewRef != undefined) {
										// The below code is to process info when insert new ref is done via editor
										var inputSuppliedData = $(".inputHTMLSearchRef").html();
										inputSuppliedData = inputSuppliedData.split("|");
										$(".askAuthor").addClass("hidden");
										$(".editCSLReference").html("Back");
										$(".insertReference").html("Insert Reference");
										$(".insertReference").attr("data-message", "{'click':{'funcToCall': 'insertReferenceData','channel':'components','topic':'reference','param':{'save': 'saveToRef','insertref':true}}}");
										if(inputSuppliedData != undefined && inputSuppliedData[0] != "by-pasteref") {
											var originalData = $(originalRef).find("p");
											if(originalData.length > 0) {
												$(".validatedCSLRefHTM").html("<p>" + $(originalData).html() + "</p>");
											}
										} else {
											$(".validatedCSLRefHTM").html("<p>" + inputSuppliedData[1] + "</p>");
										}
									} else {
										// $('.insertReference').attr('data-message', "{'click':{'funcToCall': 'validatenew','param' : {'save':'acceptRef'},'channel':'components','topic':'general'}}");
										// $('.insertReference').html('Accept Changes');
										$(".insertReference").addClass("Accept Changes");
										$(".askAuthor").removeClass("hidden");
										$(".editCSLReference").html("Edit Reference");
										$(".insertReference").attr("data-message", "{'click':{'funcToCall': 'validatenew','param' : {'save':'acceptRef'},'channel':'components','topic':'general'}}");
									}
									$(".validatedCSLRefHTML").addClass("hidden");
									$(".validatedCSLRefMismatchHTML").addClass("hidden");
									$(".validationResult").removeClass("hidden");
									$(".validatedCSLRefMismatchHTM").removeClass("hidden");
									if(res.changes != undefined && res.changes.length != undefined && res.changes.length > 0) {
										$(".validationResult table").closest(".validationResult").removeClass("hidden");
									} else {
										$(".validationResult table").closest(".validationResult").addClass("hidden");
									}
									var inputWrap = $('[data-component="jrnlRefText_edit"]').find('[data-input-editable="true"]');
									var type;
									var searchClass = inputWrap.find('input[name="refSearch"]:checked').attr("data-value");
									//if checkbox not check and click on next button in insertref popup at that time code is breaking
									if(searchClass) {
										type = searchClass.replace(/^Ref/, "");
									}
									if(param && param.validateInsertNewRef && type && type != "by-pasteref") {
										//hide the original  reference box and table in case of insert reference
										$($(".validationResult .validatedCSLRefHTM")[0]).addClass("hidden");
										$($(".validationResult thead tr th")[0]).addClass("hidden");
										$($(".validationResult")[2]).addClass("hidden");
									} else if(type && (type == "by-pasteref" || type == "PMID" || type == "DOI")) {
										$($(".validationResult .validatedCSLRefHTM")[0]).removeClass("hidden");
										$($(".validationResult thead tr th")[0]).removeClass("hidden");
										if(res.changes && res.changes != undefined) {
											$($(".validationResult")[2]).removeClass("hidden");
										}
									}
									$('[data-component="jrnlRefCsl_edit"]').removeClass("screen1");
									$('[data-component="jrnlRefCsl_edit"]').removeClass("screen2");
									$('[data-component="jrnlRefCsl_edit"]').removeClass("screen3");
									if(param && param.goto && param.goto == "screen3") {
										// The below code is to handle screen 3 UI changes
										$('[data-component="jrnlRefCsl_edit"]').addClass("screen1");
										$('[data-component="jrnlRefCsl_edit"]').find(".screen3hide").hide();
										$('[data-component="jrnlRefCsl_edit"]').find(".screen3show").show();
										if(param && param.runCSLAutomatically && $(".closepopupRefJrnl").length > 0) {} else {
											var targetNodeEdit = $(targetNode).clone();
											$(targetNodeEdit).find(".del").remove();
											$(targetNodeEdit).find(".ins").contents().unwrap();
											eventHandler.components.references.editCslRef(param, "", true, validatedReference);
										}
										if(param && param.saverefdata ){
											$(targetNode).closest("[data-component]").find('.referrorMessage5') .html(messages["message1"]);
										}else if($(targetNode).closest("[data-component]").attr('validated') && $(targetNode).closest("[data-component]").attr('validated')=='true'){
											$(targetNode).closest("[data-component]").find('.referrorMessage5') .html(messages["message2"]);
										}else{
											$(targetNode).closest("[data-component]").find('.referrorMessage5') .html(messages["message3"]);
										}
									} else if(param && param.goto && param.goto == "screen2") {
										// The below code is to handle screen 2 UI changes
										$('[data-component="jrnlRefCsl_edit"]').find(".accept").removeClass("disabled");
										$(targetNode).closest("[data-component]").find("#headercontent").hide();
										$('[data-component="jrnlRefCsl_edit"]').addClass("screen2");
										// $('[data-component="jrnlRefCsl_edit"]').find('.EditRefTitle').html('New Validated Reference Fields')
										// eventHandler.components.general.editCslRef({'goto':'screen2'},"",true,$('.validatedCSLRefMismatchHTM .jrnlRefText'), displayNewFields);
										$('[data-component="jrnlRefCsl_edit"]').find(".validatedCSLRefMismatchHTM").addClass("hidden");
										$('[data-component="jrnlRefCsl_edit"]').find(".validatedCSLRefMismatchHTM").hide();
										$(targetNodeEdit).closest("[data-component]").find("#headercontent").hide();
										$('[data-component="jrnlRefCsl_edit"]').addClass("screen2");
										// prevCloneNode = $($($('[data-component="jrnlRefCsl_edit"]').find('.previewContent')).clone());
										$('[data-component="jrnlRefCsl_edit"]').find(".screen2show").show();
										$('[data-component="jrnlRefCsl_edit"]').find(".screen2show").removeClass('hidden');
										$('[data-component="jrnlRefCsl_edit"]').find(".screen2hide").hide();
										$('[data-component="jrnlRefCsl_edit"]').find(".screen3Title").html("Edit Reference - Step 2 ( Please check all new Validated Fields and run styling on it. ) ");
										var paramdata = {
												goto: "screen3",
												skipValidation: "true",
											}
										if(param.saverefdata){
											paramdata["saverefdata"] = param.saverefdata;
										}
										
										if((res.changes == undefined || (res.changes && res.changes.length == 0))  ) {
											eventHandler.components.references.validatenew(paramdata, $(".runstyling"));
										}
									}
									// $('[data-component="jrnlRefCsl_edit"]').find('.screen3hide').hide()
									// $('[data-component="jrnlRefCsl_edit"]').find('.screen3Title').html('Final Styled Reference')
									//$('[data-component="jrnlRefCsl_edit"] .validatedCSLRefHTML').append(validatedReference)
									//eventHandler.components.PopUp.editCslRef('', '', validatedReference); // populate validated reference in popup
									// kriya.general.cslAddToHistory(validatedReference, $('[data-component="jrnlRefCsl_edit"]')); // to generate track changes history cards
								} else {
									$(popper).find(".validated").addClass("hidden");
									$(popper).find(".validationFailed").removeClass("hidden");
									popper.progress("", true);
                                    kriya.removeAllNotify({'type':'reference'});
									kriya.notification({
										title: "ERROR",
										type: "error",
										timeout: 5000,
										content: "Unable to convert CSL, validation failed",
										"notifType":"reference",
										icon: "icon-warning2",
									});
									return;
								}
							} else {
								popper.progress("", true);
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Validation failed, Please try again",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return;
							}
						}, function(err){
							popper.progress("", true);
							kriya.removeAllNotify({'type':'reference'});
							kriya.notification({
								title: "ERROR",
								type: "error",
								timeout: 5000,
								content: "Validation failed, Please try again",
								"notifType":"reference",
								icon: "icon-warning2",
							});
							return;
						});
					
				}
			}
		},
		// Function to validate multiple Author field inserted in the Author component and split them - Kamesh - 12-03-2020
		parseRefAuthor: function(param, comp, onSuccess) {
			
			if($(comp).find(".previewContentCloned").find('[class="RefAuthor"][data-author-parsed!="true"],[class="RefEditor"][data-author-parsed!="true"]').length > 0) {
				var firstNode = $(comp).find(".previewContentCloned").find('[class="RefAuthor"][data-author-parsed!="true"],[class="RefEditor"][data-author-parsed!="true"]').first();
				var skipProgressBar = false;
				if(param != undefined && param["skipProgressBar"]) {
					skipProgressBar = true;
				}
				if(!skipProgressBar) {
					comp.progress("Parsing Authors");
				} else {
					$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").show();
				}
				$(firstNode).find(".del").remove();
				$(firstNode).find(".ins").contents().unwrap();
				var data = firstNode.text();
				if(data) {
					data = data.replace(/(<([^>]+)>)/gi, "");
				}
				var params = {
					authorNodeString: "<p>" + data + "</p>",
					classPrefix: "jrnl",
					type: "parseAuthors",
				};
				kriya.general.sendAPIRequest("parsedata", params, function(res) {
					var newNode;
					if(res.body == "") {
						if(!skipProgressBar) {
							comp.progress("", true);
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").addClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").hide();
						}
                        kriya.removeAllNotify({'type':'reference'});
						kriya.notification({
							title: "ERROR",
							type: "error",
							timeout: 5000,
							content: "Unable to parse Author",
							"notifType":"reference",
							icon: "icon-warning2",
						});
					} else if($(res.body).find(".jrnlAuthor").length > 0) {
						if(!skipProgressBar) {
							comp.progress("", true);
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").addClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").hide();
						}
						newNode = $(res.body).find(".jrnlAuthor");
						if(newNode.length > 0) {
							$(newNode).each(function(s) {
								var clonedTemp = $(comp).parent().find('[data-template="true"][data-class="RefAuthor"]').clone();
								clonedTemp.removeAttr("data-template");
								clonedTemp.attr("data-added", "true");
								clonedTemp.attr("data-clone", "true");
								clonedTemp.attr("class", "RefAuthor");
								clonedTemp.attr("data-author-parsed", "true");
								clonedTemp.attr("id", firstNode.attr("id"));
								if(firstNode && $(firstNode).attr("class") && $(firstNode).attr("class") == "RefEditor") {
									clonedTemp.attr("class", "RefEditor");
								}
								$(newNode[s]).find(".jrnlSurName").addClass("RefSurName").removeClass("jrnlSurName");
								$(newNode[s]).find(".jrnlGivenName").addClass("RefGivenName").removeClass("jrnlGivenName");
								var newAuthorNode = $(newNode[s]).contents().unwrap();
								$(newAuthorNode).find(".RefGivenName").text($(this).find(".RefGivenName").text());
								$(newAuthorNode).find(".RefSurName").text($(this).find(".RefSurName").text());
								$(clonedTemp).append(newAuthorNode);
								$(clonedTemp).insertBefore(firstNode);
							});
							$(firstNode).remove();
							eventHandler.components.references.parseRefAuthor(param, comp, onSuccess);
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").removeClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").show();
							kriya.removeAllNotify({'type':'reference'});
                            kriya.notification({
								title: "ERROR",
								type: "error",
								timeout: 5000,
								content: "Unable to parse Author",
								"notifType":"reference",
								icon: "icon-warning2",
							});
						}
					} else {
						if(!skipProgressBar) {
							comp.progress("", true);
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").removeClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").show();
						}
                        kriya.removeAllNotify({'type':'reference'});
						kriya.notification({
							title: "ERROR",
							type: "error",
							timeout: 5000,
							content: "Parsing Authors Failed. Please check if you have a missing space between surName and givenName.",
							icon: "icon-warning2",
							"notifType":"reference",
						});
					}
				});
			} else {
				if(onSuccess && typeof onSuccess == "function") {
					onSuccess(true);
				}
			}
		},
		goBack: function(param, targetElement) {
			var popper = $(targetNode).closest("[data-component]");
            popper.attr('validated','false');
			$(popper).find("[data-wrapper], .refBtns, .refContent").removeClass("hidden");
			$(popper).find(".validationResult").addClass("hidden");
			$(popper).find(".editResult ").removeClass("hidden");
			$(popper).find(".ref-type ").removeClass("hidden");
			$(popper).find("#headercontent").show();
		},
		editRefscreenCSL: function(param, targetNode) {
			// $(targetNode).closest("[data-component]").find('#headercontent').hide()
			if(param && $('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
				param["screen3"] = true;
			}
			if(param && param["save"] == "saveToRef") {
				// trigger validation if selected this option

					var triggerValidation = false;
					var param = [];
					param["skipValidation"] = true;
					eventHandler.components.references.validatenew(param, targetNode);
					return false;
				
			}
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				var editElement = "";
				var popper = $(targetNode).closest("[data-component]");
				if(param == undefined || (param && param.save != "saveToRef")) {
					if($('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').length > 0) {
						$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').remove();
					}
				}
				if(popper.length > 0 && popper[0].hasAttribute("data-node-xpath")) {
					var nodeXpath = popper.attr("data-node-xpath");
					editElement = kriya.xpath(nodeXpath);
				}
				//if editElement is come as undefined at that time code will break
				if(editElement && editElement.length > 1) {
					for(var r = 1, rl = editElement.length; r < rl; r++) {
						currTargetEle = editElement[r];
					}
				} else if(editElement) {
					var currTargetEle = $(editElement).clone();
				}
				//getting ref type
				var cslRefType = $('[data-component="jrnlRefCsl_edit"]').find(":selected").val();
				// constructing as html
				var objEle = mappingObj["kriyaClassToUi"][cslRefType];
				if(objEle == undefined) {
                    kriya.removeAllNotify({'type':'reference'});
					kriya.notification({
						title: "ERROR",
						type: "error",
						timeout: 5000,
						content: "For <b>" + cslRefType + "</b> type reference, config not present",
						"notifType":"reference",
						icon: "icon-warning2",
					});
					return false;
				}
				if($('[data-component="jrnlRefCsl_edit"]').attr('insert')) {
					$('[data-component="jrnlRefCsl_edit"]').find(".inserthide").addClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".insertshow").removeClass("hidden");
					$('[data-component="jrnlRefCsl_edit"]').find(".insertshow").show();
				}
				var objEleKeys = Object.keys(objEle);
				if(param && /saveToRef|saveChangedToRef|acceptRef/.test(param.save)) {
					// Just update the data if save is clicked
					currTargetEle = $('[data-component="jrnlRefCsl_edit"] .validatedCSLRefMismatchHTM p');
					var refParaId = $(currTargetEle).attr("data-id");
					var refType = $(currTargetEle).attr("data-refType");
					var dataValidateOnline = $(currTargetEle).attr("data-validated-online");
					if(refType != undefined) {
						$('.back .jrnlRefText[data-id="' + refParaId + '"]').attr("data-refType", refType);
					}
					if(dataValidateOnline != undefined) {
						$('.back .jrnlRefText[data-id="' + refParaId + '"]').attr("data-validated-online", true);
					}
					$('.back .jrnlRefText[data-id="' + refParaId + '"]').removeAttr("data-untag");
					//Remove empty node from reference added by vijayakumar on 28-10-2020
					$(currTargetEle).find('*[class^="Ref"]').each(function() {
						var refHTML = $(this).html();
						refHTML = refHTML.replace(/^[\s\u00A0\u2003\u2002\u2004\u2005\u2007\u2008\u2009\u200A\u200B\u200C\u200D]+$/g, "");
						if(refHTML == "") {
							$(this).remove();
						}
					});
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").find("> .jrnlQueryRef").each(function() {
						var id = $(this).attr("id");
						if($(currTargetEle).find(".jrnlQueryRef#" + id).length == 0) {
							$(currTargetEle).prepend($(this)).html();
						}
					});
					$(currTargetEle).removeAttr("xmlns");
					$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").html($(currTargetEle).html());
					var reftyppe = $(currTargetEle).attr("data-reftype");
					if(reftyppe != undefined) {
						$("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").attr("data-reftype", reftyppe);
					}
					if($("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]").length > 0) {
						currTargetEle = $("#contentDivNode .jrnlRefGroup").find("[data-id=" + refParaId + "]");
					}
					if(currTargetEle) {
						var refId = $(currTargetEle).attr("data-id");
						$('.back .jrnlRefText[data-id="' + refId + '"]').html(currTargetEle.html());
						if(currTargetEle.attr("data-reftype")) {
							$('.back .jrnlRefText[data-id="' + refId + '"]').attr("data-reftype", currTargetEle.attr("data-reftype"));
						}
					}
					kriyaEditor.settings.undoStack.push($("#contentDivNode .jrnlRefGroup").find("[id=" + refParaId + "]"));
					kriyaEditor.init.addUndoLevel("style-change");
					kriya.popUp.closePopUps($("[data-component='jrnlRefCsl_edit']").closest("[data-component]"));
					kriyaEditor.init.save();
				} else {
					var popper = $(targetNode).closest("[data-component]");
					var addField= false;
					if(param && param.addField){
						addField = true;
					}
					var isAuthorEmpty = true;
					if ($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']").length > 0 || $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']").length > 0) {
						var givenames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']");
						for (var i = 0; i < givenames.length; i++) {
							if ($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlGivenName']")[i]).closest('.dataLine').find('.text-line:empty').length != 1) {
								isAuthorEmpty = false
							}
						}
						var surnames = $(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']");
						for (var i = 0; i < surnames.length; i++) {
							if ($($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-class='jrnlSurName']")[i]).closest('.dataLine').find('.text-line:empty').length != 1) {
								isAuthorEmpty = false
							}
						}
					}
					//Validate event date ranges
					if($(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartYear']").length > 0) {
						var startDay = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartDay']").text(),
						startMonth = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartMonth']").text(),
						startYear = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefStartYear']").text(),
						endDay = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndDay']").text(),
						endMonth = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndMonth']").text(),
						endYear = $(targetNode).closest("[data-component]").find(".refContent .text-line[data-class='RefEndYear']").text()
						
						if(startMonth && isNaN(startMonth)) {
							startMonth = monthMapping[startMonth.slice(0, 3).toLowerCase()];
						} else if(startMonth && !isNaN(startMonth) && startMonth.length == 1) {
                            startMonth = '0' + startMonth;
						}
						if(endMonth && isNaN(endMonth)) {
							endMonth = monthMapping[endMonth.slice(0, 3).toLowerCase()];
						} else if(endMonth && !isNaN(endMonth) && endMonth.length == 1) {
                            endMonth = '0' + endMonth;
						}

						if(startDay && startDay.length == 1) {
							startDay = '0' + startDay
						}
						if(endDay && endDay.length == 1) {
							endDay = '0' + endDay
						}

						var error = '';
						if (!startDay && !endDay && !startMonth && !endMonth && !startYear && !endYear) {
							error = "Empty values present in one of the fields. Please remove the field or add the value.";
						} else if(startMonth && !isNaN(startMonth) && (Number(startMonth) > 12 || Number(startMonth) < 1) || endMonth && !isNaN(endMonth)  && (Number(endMonth) > 12 || Number(endMonth) < 1)) {
							error = "Please enter a valid Month.";
						} else if((startDay && !isNaN(startDay) && (Number(startDay) > 31 || Number(startDay) < 1)) || (endDay && !isNaN(endDay)  && (Number(endDay) > 31 || Number(endDay) < 1))) {
							error = "Please enter a valid Day.";
						}
						//Check if end date is present without start date
						else if(!startDay && !startMonth && !startYear && (endDay || endMonth || endYear)) {
							error = 'Please provide a Start date before entering the End date for the Event date.'
						} else if((startDay && !startMonth) || (endDay && !endMonth)) {
							error = 'Please enter the Month for the Event date.'
						} else if ((startMonth && !startYear) || (endMonth && !endYear)) {
							error = 'Please enter the Year for the Event date.'
						} else if (startDay==endDay && startMonth==endMonth && startYear==endYear) {
							error = 'Start and end dates cannot be the same. Please correct the dates.'
						} else if (isNaN(startDay) || isNaN(endDay) || isNaN(startYear) || isNaN(endYear)){
							error = 'Day and Year should be a number.'
						} else if ((startYear && (startYear.length < 4 || startYear.length > 4 )) || (endYear && (endYear.length < 4 || endYear.length > 4))){
							error = 'Please enter a valid Year.'
						}

						if(error) {
							kriya.removeAllNotify({'type':'reference'});
							kriya.notification({
								title: "ERROR",
								type: "error",
								timeout: 5000,
								content: error,
								"notifType":"reference",
								icon: "icon-warning2",
							});
							return false;
						}
					} 
					
					if(($(popper).closest("[data-component]").find(".selectionRequired:visible").length > 0 || $(popper).closest("[data-component]").find(".untagged").length > 0) && !addField) {
						kriya.removeAllNotify({'type':'reference'});
                        kriya.notification({
							title: "ERROR",
							type: "error",
							timeout: 5000,
							content: "Please tag all the fields in edit reference box in order to proceed.",
							"notifType":"reference",
							icon: "icon-warning2",
						});
						return false;
					} else if ((!isAuthorEmpty || ($(targetNode).closest("[data-component]").find(".refContent .text-line:empty[data-texthint-holder!='Month'][data-texthint-holder!='Day'][data-class!='jrnlGivenName'][data-class!='jrnlSurName'][data-class!='RefCollabAbbrev'][data-class!='RefAccesedMonth'][data-class!='RefAccesedDay'][data-class!='RefStartDay'][data-class!='RefStartMonth'][data-class!='RefEndDay'][data-class!='RefEndMonth'][data-class!='RefEndYear'][data-class!='jrnlSuffixField'][data-class!='jrnlDelimiter'][data-class!='jrnlAuthorSuffixName']").length > 0)) && !addField) {
						kriya.removeAllNotify({'notifType':'reference'});
						kriya.notification({
							title: "ERROR",
							type: "error",
							timeout: 5000,
							content: "Empty values present in one of the fields. Please remove the field or add the value.",
							notifType:"reference",
							icon: "icon-warning2",
						});
						$(targetNode).find('option[selected="selected"]').prop('selected',true)
						return false;
					}
					// In case of direct rendering, we need to skip the csl rendering.
					param["skipProgressBar"] = true;
					
						var popper = $(targetNode).closest("[data-component]");
						if($('[data-component="jrnlRefCsl_edit"]').find("[data-error]").length > 0) {
							return false;
						}
						var refID = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContentCloned")).find(".jrnlRefText").attr("data-id");
						var cslStyle = false;
						if(citeJS && citeJS.cofig && citeJS.cofig.R && citeJS.cofig.R.cslStyle) {
							cslStyle = citeJS.cofig.R.cslStyle;
							if(refID && /BR/.test(refID)){
								cslStyle = citeJS.floats.getConfig("BR")["cslStyle"];
							}
						} else if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["cslStyle"]) {
							cslStyle = citeJS.floats.getConfig("R")["cslStyle"];
							if(refID && /BR/.test(refID)){
								cslStyle = citeJS.floats.getConfig("BR")["cslStyle"];
							}
						} else if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["cslStyle"]) {
							cslStyle = citeJS.floats.getConfig("R")["cslStyle"];
						} else {
							if(objEle == undefined) {
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Configuration of citeJS is missing",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return false;
							}
						}
						var validationSources = "";
						if(citeJS && citeJS.cofig && citeJS.cofig.R && citeJS.cofig.R.validationsource) {
							validationSources = citeJS.cofig.R.validationsource;
						} else if(citeJS && citeJS.floats && citeJS.floats.getConfig != undefined && citeJS.floats.getConfig("R") && citeJS.floats.getConfig("R")["validationsource"]) {
							validationSources =citeJS.floats.getConfig("R")["validationsource"];
						} else {
							if(objEle == undefined) {
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Configuration of citeJS is missing",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return false;
							}
						}
						var cslRefType = $(popper).find('[data-class="CslRefType"]').val();
						var journalTypeInfo = {
							Journal: "article-journal",
							Book: "book",
							Book_Editor: "book",
							Thesis: "thesis",
							Data: "dataset",
							Conference: "paper-conference",
							Software: "entry",
							Patent: "patent",
							Website: "webpage",
							Report: "report",
						};
						if(journalTypeInfo[cslRefType]) {
							$(currTargetEle).attr("type", journalTypeInfo[cslRefType]);
						} else if(cslRefType) {
							$(currTargetEle).attr("type", cslRefType.toLowerCase());
						}
						var dataToSend = $($($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).clone());
						if(param && param.goto == "screen3" && !param.runCSLAutomatically) {
							dataToSend = $(".validatedCSLRefMismatchHTM");
						}
						dataToSend.find("[data-validated-online]").attr("data-retain-color",true);
						dataToSend.find("[data-validated-online]").removeAttr("data-validated-online");
						var httpContent = '<validate projectname="' + kriya.config.content.customer + '">' + dataToSend.html() + "</validate>";
						var parameters = {
							processType: "cslEditReference",
							customer: kriya.config.content.customer,
							project: kriya.config.content.project,
							data: httpContent,
							cslType: "validate",
							cslStyle: cslStyle,
							type: "HTML",
							outputformat: "HTML",
						};
						var newSlNo = false;
						if($('#contentDivNode [data-id="' + refID + '"]').find(".RefSlNo").length > 0) {
							var slNo = $('#contentDivNode [data-id="' + refID + '"]').find(".RefSlNo").clone(true);
						} else if(refID) {
							//added by kirankumar:-issue:when insert new reference it showing refid as 1
							var slNo = refID.replace(/[A-Z]+/g, "");
							newSlNo = slNo;
						}
						if(newSlNo) {
							parameters["slNo"] = newSlNo;
						} else {
							slNo = refID.replace(/[A-Z]+/g, "");
							parameters["slNo"] = slNo;
						}
						if(param && param.skipValidation) {
							parameters["skipValidation"] = true;
						}
						if($('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').length > 0) {
							$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').remove();
						}
						$('[data-component="jrnlRefCsl_edit"] .cslReferenceOutput').html("");
						popper = $(targetNode).closest("[data-component]");
						var nodeXpth = popper.attr("data-node-xpath");
						editElement = $(kriya.xpath(nodeXpth)[0]);
						currTargetEle = $(editElement).clone();
						if(currTargetEle.length > 0) {
							$(popper).find(".refContent [data-node-xpath]").each(function() {
								var xpath = $(this).attr("data-node-xpath");
								var dataElement = kriya.xpath(xpath, currTargetEle);
								if(dataElement.length > 0) {
									$(dataElement[0]).text($(this).text());
								}
							});
						}
						if(validationSources){
							parameters["validationSources"] = validationSources;
						}
						var originalRef = parameters["data"];
						// show message in progress bar before validation
						if(param && param.skipValidation && !param.runCSLAutomatically) {
							popper.progress("Applying customer style sheet specifications on reference.");
						} else if(param && !param.runCSLAutomatically) {
							popper.progress("Trying to Validate the data and fetch new fields");
						} else {
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").removeClass("hidden");
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").show();
							$('[data-component="jrnlRefCsl_edit"]').find(".accept").addClass("disabled");
						}
						parameters["newSCStageEditor"] = true;
						var paramData = $(".previewContent");
						var unwrapps = $(paramData.find("p").find(".punctuation"));
						for(var k = 0; k < unwrapps.length; k++) {
							unwrapps[k].replaceWith(", ");
						}
						$(paramData).find(".del").remove();
						$(paramData).find(".ins").contents().unwrap();
						$(paramData).find('p').removeAttr("data-validated-online");
						parameters.data = paramData.html();
						// send the request for validation after turning off CSL
						if($(targetNode).closest(".saveRefField .text-line:empty").length==2 && $(targetNode).closest('.saveRefField [data-class="jrnlGivenName"]').length>0){
							$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").addClass("hidden");
							return;
						}
						// To skip Reference Validation [Req: Cabi]
						if(citeJS && citeJS.cofig && citeJS.cofig.R && citeJS.cofig.R.skipReferenceValidation) {
							parameters['skipValidation'] = true;
						}
						kriya.general.sendAPIRequest("process_reference", parameters, function(res) {
							if(param && !param.runCSLAutomatically) {
								popper.progress("Rendering results");
							} else {
								$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").addClass("hidden");
								$('[data-component="jrnlRefCsl_edit"]').find(".StylingProgress").hide();
								$('[data-component="jrnlRefCsl_edit"]').find(".accept").removeClass("disabled");
							}
							if(res && res != "" && res != "error") {
								if(res.body) {
									if(param && param.goto && param.goto=="screen3"){
										var refID = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).find(".jrnlRefText").attr("data-id");
										var reftype = $($('[data-component="jrnlRefCsl_edit"]').find(".previewContent")).find(".jrnlRefText").attr("data-reftype");
										var validatedReference = $(res.body);
										validatedReference.attr("data-id", refID);
										validatedReference.attr("data-reftype", reftype);
										validatedReference.removeAttr("xmlns");
										$(".previewContent").find("p").html($(res.body).html());
										eventHandler.components.references.saveReferenceData(param, $('[data-component="jrnlRefCsl_edit"]').find(".accept"));
									}else{
										$(".previewContent").find("p").html($(res.body).html());
									}
									
									$(popper).find(".validated").addClass("hidden");
									// 												$(popper).find('.validationFailed').removeClass('hidden');
									popper.progress("", true);
								} else {
									$(popper).find(".validated").addClass("hidden");
									// 												$(popper).find('.validationFailed').removeClass('hidden');
									popper.progress("", true);
                                    kriya.removeAllNotify({'type':'reference'});
									kriya.notification({
										title: "ERROR",
										type: "error",
										timeout: 5000,
										content: "Unable to convert CSL, validation failed",
										"notifType":"reference",
										icon: "icon-warning2",
									});
									return;
								}
							} else {
								popper.progress("", true);
                                kriya.removeAllNotify({'type':'reference'});
								kriya.notification({
									title: "ERROR",
									type: "error",
									timeout: 5000,
									content: "Validation failed, Please try again",
									"notifType":"reference",
									icon: "icon-warning2",
								});
								return;
							}
						});
					
				}
			}
		},
		addCslField: function(param, targetNode) {
			// when a plus button is clicked,  a new field is added in ref editor using below code.
			const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				var refType = $('[data-component="jrnlRefCsl_edit"]').find(":selected").val();
				if(mappingObj["kriyaRefTypeToCslType"][refType] != undefined) {
					refType = mappingObj["kriyaRefTypeToCslType"][refType];
				}
				var fieldFormat = mappingObj["cslFieldsMapping"][refType];
				// Adding modified access day and month for brill
				if(isModifyEditorPresent && fieldFormat && fieldFormat.accessed && fieldFormat.accessed.name) {
					fieldFormat.accessed.name = 'Accessed Year';
				}

				if(!isModifyEditorPresent && fieldFormat && fieldFormat['accessed-day']) {
					delete fieldFormat['accessed-day']
				}

				if(!isModifyEditorPresent && fieldFormat && fieldFormat['accessed-month']) {
					delete fieldFormat['accessed-month']
				}

				var objEle = mappingObj["kriyaClassToUi"][refType];
				// constructing options according to csl mapping
				var optionNode = $(".cslRefTemples").find('[data-temp="option"]').clone().removeAttr("data-temp");
				objEle = eventHandler.components.references.sortJSON(objEle);
				if(optionNode) {
					currNode = document.createElement("option");
					$(currNode).attr("value", "").text("Select field");
					$(optionNode).find('[data-class="refDataType"]').append(currNode);
					$(targetNode).closest("[data-component]").find(".refContent").attr("data-ref-edited", "true");
				}
				var sortedObj = [];
				for(var key in objEle) {
					if(fieldFormat && objEle && objEle[key] && fieldFormat[objEle[key]] && fieldFormat[objEle[key]].name) {
						sortedObj[fieldFormat[objEle[key]].name] = objEle[key];
					}
				}
				var sortedField = {};
				Object.keys(sortedObj).sort().forEach(function(key) {
					sortedField[key] = sortedObj[key];
				});
				for(var key in sortedField) {
					var currNode = document.createElement("option");
					$(currNode).attr("value", sortedObj[key]).text(key);
					$(optionNode).find('[data-class="refDataType"]').append(currNode);
				}
				var newFieldID = uuid.v4();
				// constructing empty field
				var newField = $(".cslRefTemples").find('[data-temp="dataLine"]').clone().removeAttr("data-temp");
				$(newField).append($(optionNode));
				var newDataField = $(".cslRefTemples").find('[data-temp="dataField"]').clone().removeAttr("data-temp");
				$(newDataField).append($(".cslRefTemples").find('[data-temp="addAndDeleteButton"]').clone().removeAttr("data-temp"));
				$(newField).append($(newDataField));
				var currentnodexpath = $(targetNode).closest(".dataLine").find('[class="text-line"]').attr("data-node-xpath");
				$(newField).find('[class="text-line"]').attr("data-prev-node-xpath", currentnodexpath);
				$(newField).find('[class="text-line"]').attr("id-data", newFieldID);
				$(newField).addClass("saveRefField");
				$(newField).attr("new-field-added", true);
				$(newField).attr("firstfieldvalue", "");
				$(newField).find('[class="text-line"]').attr("data-node-xpath", "//*[@id='" + newFieldID + "']");
				
				$(newField).insertAfter($(targetNode).closest(".dataLine"));
				
               
				//trigger selection of that field.
				var currentNodeID = $(targetNode).closest(".dataLine").find(".text-line").attr("id-data");
				var newElement = '<span class="RefComments" id="' + newFieldID + '"></span>';
				$(newElement).attr("id", newFieldID);
				var newElementPunctuation = '<span class="' + newFieldID + ' punctuation">, </span>';
				if(isModifyEditorPresent) {
					if($(targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("#" + currentNodeID))[0].nextSibling) {
					  $(newElement).insertAfter($(targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("#" + currentNodeID))[0].nextSibling);
					}
					else {
						$(newElement).insertAfter($(targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("#" + currentNodeID))[0]);
					}
				}
				else {
                $(newElementPunctuation).insertAfter(targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("#" + currentNodeID));
				$(newElement).insertAfter(targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("." + newFieldID));
				$(newElementPunctuation).insertAfter(targetNode.closest('[data-type="popUp"]').find(".previewContentTemp").find("#" + currentNodeID));
				$(newElement).insertAfter(targetNode.closest('[data-type="popUp"]').find(".previewContentTemp ").find("." + newFieldID));
				}
				
				var diffdata = htmldiff($(".previewContentTemp").html(), $(".previewContent").html());
				if(diffdata && $(".previewContent").html() && diffdata == $(".previewContent").html()) {
					$(this).closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
				} else {
					$(this).closest('[data-type="popUp"]').find(".referrorMessage2").removeClass("hidden");
				}
				if ($('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
					$(".saveRefData").attr("data-message", "{'click':{'funcToCall': 'saveCSLReferenceData','channel':'components','topic':'references'}}");
					$(".saveRefDataScreen3").attr("data-message", "{'click':{'funcToCall': 'saveCSLReferenceData','channel':'components','topic':'references'}}");
				}
				eventHandler.components.references.changeCslField("", $($(newField).find("select")));
				
			} else {
                kriya.removeAllNotify({'type':'reference'});
				kriya.notification({
					title: "Failed",
					type: "error",
					content: "Mapping Object file missing - Kriya config error",
					"notifType":"reference",
					timeout: 8000,
					icon: "icon-info",
				});
			}
		}, 
		setDOMTreeData: function(field){
			var target = field;
				kriya.config.refDOMContentModified = false;
				if($(".saveRefData").hasClass("closepopupRefJrnl")) {
					return;
				}
				if(event.inputType == 'insertFromPaste'){
					var tempRef = target.text();
					tempRef = cleanupOnPaste(tempRef);
					target.text(tempRef)
				}
				eventHandler.components.references.validateRefThroughCsl({
					save: "saveToRef",
				}, target);
				var diffdata = htmldiff($(".previewContentTemp").html(), $(".previewContent").html());
				if(diffdata && $(".previewContent").html() && diffdata == $(".previewContent").html()) {
					target.closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
				} else {
					target.closest('[data-type="popUp"]').find(".referrorMessage2").removeClass("hidden");
				}
		},
		refTypeFieldMapping: function(param, targetNode) {
			// check if any thing is untagged and stop the user if there is any.
			const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
			if($(targetNode).closest("[data-component]").find(".selectionRequired:visible").length > 0 ) {
				kriya.removeAllNotify({'type':'reference'});
                kriya.notification({
					title: "ERROR",
					type: "error",
					timeout: 5000,
					content: "Please tag all the fields in edit reference box in order to proceed.",
					"notifType":"reference",
					icon: "icon-warning2",
				});
				$(targetNode).find('option[selected="selected"]').prop('selected',true)
				return false;
			}
			var mappingObj = kriya.config.cslMappingObj;
			if(mappingObj) {
				var refType = $(targetNode).find(":selected").val();
				if(mappingObj["kriyaRefTypeToCslType"][refType] != undefined) {
					refType = mappingObj["kriyaRefTypeToCslType"][refType];
				}
				// $(targetNode).closest("[data-component]").find(".previewContent .jrnlRefText").attr("data-reftype", refType);
				// $(targetNode).closest("[data-component]").find(".previewContentTemp .jrnlRefText").attr("data-reftype", refType);
				var fieldFormat = mappingObj["cslFieldsMapping"][refType];

				// Showing access month and day as a stanalone field for brill customer
                if(isModifyEditorPresent && fieldFormat && fieldFormat.accessed && fieldFormat.accessed.name) {
					fieldFormat.accessed.name = 'Accessed Year';
				}

				if(!isModifyEditorPresent && fieldFormat && fieldFormat['accessed-day']) {
					delete fieldFormat['accessed-day']
				}

				if(!isModifyEditorPresent && fieldFormat && fieldFormat['accessed-month']) {
					delete fieldFormat['accessed-month']
				}

				var objEle = mappingObj["kriyaClassToUi"][refType];
				// contructing options according to csl mapping
				var optionNode = $(".cslRefTemples").find('[data-temp="option"]').clone().removeAttr("data-temp");
				objEle = eventHandler.components.references.sortJSON(objEle);
				if(optionNode) {
					currNode = document.createElement("option");
					$(currNode).attr("value", "").text("Select field");
					$(optionNode).find('[data-class="refDataType"]').append(currNode);
				}
				var sortedObj = [];
				for(var key in objEle) {
					if(fieldFormat && objEle && objEle[key] && fieldFormat[objEle[key]] && fieldFormat[objEle[key]].name) {
						sortedObj[fieldFormat[objEle[key]].name] = objEle[key];
					}
				}
				var sortedField = {};
				Object.keys(sortedObj).sort().forEach(function(key) {
					sortedField[key] = sortedObj[key];
				});
				for(var key in sortedField) {
					var currNode = document.createElement("option");
					$(currNode).attr("value", sortedObj[key]).text(key);
					$(optionNode).find('[data-class="refDataType"]').append(currNode);
				}
				if(fieldFormat) {
					var requiredFields = {};
					if(fieldFormat) {
						for(var key in fieldFormat) {
							if(fieldFormat && fieldFormat[key] && fieldFormat[key].validationrequired) {
								requiredFields[key] = true;
							}
						}
					}
					$('[data-component="jrnlRefCsl_edit"]').find(".refContent").find(".dataLine").each(function() {
						var currField = $(this).find("[selected]").val();
						if(currField) {
							if(requiredFields) {
								delete requiredFields[currField];
							}
						}
						$(this).find(".refData").remove();
						$(this).prepend($(optionNode).clone());
						if($(this).find("[value=" + currField + "]").length > 0) {
							$(this).find("[value=" + currField + "]").attr("selected", "selected");
						} else {
							$(this).find(".refData").addClass("selectionRequired");
						}

						if(mappingObj){
							var refType = $(targetNode).find(":selected").val();
							var KriyaTag = "RefComments";
							if(refType && currField && mappingObj["cslTagToKriyaTag"][refType] && mappingObj["cslTagToKriyaTag"][refType][currField]) {
								KriyaTag = mappingObj["cslTagToKriyaTag"][refType][currField];
							}
							var idtoChange = $(this).closest(".dataLine").find(".text-line").attr("id-data");
							$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).attr("class", KriyaTag);
							$(targetNode).closest("[data-component]").find(".previewContentTemp").find("#" + idtoChange).attr("class", KriyaTag);
						}
						// For - showing green color checks
						// if(fieldFormat && fieldFormat[currField] && fieldFormat[currField].required && $(this).find("[value=" + currField + "]").length > 0) {
						// 	$(this).find(".refData").addClass("cslRequiredField");
						// 	$(this).find(".text-line").attr("data-validate", "true");
						// } else {
						// 	$(this).find(".text-line").removeAttr("data-validate");
						// }
						// if(currField && fieldFormat[currField] && fieldFormat[currField].validationrequired) {
						// 	$(this).find(".refData").first().append('<span class="hidden" style="display:inline-block;margin-bottom:0px;vertical-align: bottom;color:green;margin-bottom:6px"><i class="material-icons" style="font-size:20px;object-fit: cover;vertical-align: bottom;">check_circle</i></span>');
						// }
					});
					// $(targetNode).closest("[data-component]").find(".previewContentCloned .jrnlRefText").attr("data-reftype", refType);
					if(Object.keys(requiredFields).length > 0) {
						var data = "Please tag ";
						data += "<span id='requiredFieldsvalidationTags' style='font-weight: bold; color: red; font-size: 10px; margin-top: 3px;''>" + Object.keys(requiredFields).join(", ") + "</span>";
						data += " for better validation results.";
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").html(data);
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").show();
					} else {
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").html("");
						$('[data-component="jrnlRefCsl_edit"]').find(".vrTagsMissing").css("display", "none");
					}
				}
			}
			eventHandler.components.references.validateRefThroughCsl({
				save: "saveToRef",
			}, $(targetNode));
			
			if($('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
				eventHandler.components.references.editRefscreenCSL({
					goto: "screen3",
					skipValidation: "true",
					runCSLAutomatically: true,
				}, $(targetNode));
			}
		},
		refAdderSelection: function(param, targetNode) {
			var refType = $(targetNode).find(":selected").val();
			if(refType =="") {
				$(targetNode).attr('data-validate-error','true')
			}else{
				$(targetNode).removeAttr('data-validate-error')
			}
		},
		changeCslField: function(param, targetNode) {
			const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
			const isAuthorWuthSuffix = $('[data-component="jrnlRefCsl_edit"]').attr('author_suffix_enabled') ? true : false;
			$(targetNode).removeClass("untagged");
			if($(targetNode).find('[value="UnTagged"]').length > 0) {
				$(targetNode).find('[value="UnTagged"]').remove();
				var data = $(targetNode).closest(".dataLine").find(".text-line").html();
				if(data) {
					data = data.trim().replace(/&nbsp\;/g, "");
					var startPunct = /^[\.\,\;\:\s\[\]\(\) ]+/g;
					var endPunctuation = /[\.\,\;\:\s\[\]\(\) ]+$/g;
					data = data.replace(startPunct, "");
					data = data.replace(endPunctuation, "");
					$(targetNode).closest(".dataLine").find(".text-line").html(data);
				}
			}
			$(targetNode).closest(".dataLine").attr("style", "");
			if(!$(targetNode).val()) {
				$(targetNode).closest(".refData").addClass("selectionRequired");
				return false;
			}
			var mappingObj = kriya.config.cslMappingObj;
			var fieldType = $(targetNode).val();
			var multipleAllowed = mappingObj['multipleFieldsAllowed'];
			var existingOptions = [];
			$(targetNode).closest('[data-component]').find('[data-class="refDataType"]').each(function () {
				existingOptions.push($(this).attr('prev-data'));
			});
			
			if(mappingObj) {
				var fieldType = $(targetNode).val();
				var popper = $(targetNode).closest("[data-component]");
				var refType = $(popper).find('[data-class="CslRefType"]').val();
				var currDataLine = $(targetNode).closest(".dataLine");
				$(currDataLine).find(".text-line").removeAttr("data-validate");
				$(currDataLine).find(".refData").removeClass("cslRequiredField");
				var idtoChange = $(targetNode).closest(".dataLine").find(".text-line").attr("id-data");
				var KriyaTag = "RefComments";
				if(refType && fieldType && mappingObj["cslTagToKriyaTag"][refType] && mappingObj["cslTagToKriyaTag"][refType][fieldType]) {
					KriyaTag = mappingObj["cslTagToKriyaTag"][refType][fieldType];
				}
				$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).attr("class", KriyaTag);
				$(targetNode).closest("[data-component]").find(".previewContentTemp").find("#" + idtoChange).attr("class", KriyaTag);
				if(KriyaTag && KriyaTag =='RefLAD' && !isModifyEditorPresent){
					["RefAccesedMonth","RefAccesedDay", "RefLAD"].forEach(function (queryObj, currentKey) {
						if($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).length==0){
							let newFieldID = uuid.v4();
							let newElement = '<span class="'+queryObj+'" id="' + newFieldID + '"></span>';
							$(targetNode).attr('id-data',newFieldID);
							$(targetNode).attr("data-node-xpath", '//*[@id="' + newFieldID + '"]');
							$(targetNode).closest("[data-component]").find(".previewContent").find('p').append(newElement)
						}
					});
				}
				if(KriyaTag && KriyaTag =='RefEventDateRange'){
					["RefStartDay", "RefStartMonth","RefStartYear", "RefEndDay", "RefEndMonth","RefEndYear"].forEach(function (queryObj, currentKey) {
						if($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).length==0){
							let newFieldID = uuid.v4();
							let newElement = '<span class="'+queryObj+'" id="' + newFieldID + '"></span>';
							$(targetNode).attr('id-data',newFieldID);
							$(targetNode).attr("data-node-xpath", '//*[@id="' + newFieldID + '"]');
							$(targetNode).closest("[data-component]").find(".previewContent").find('p').append(newElement)
						}
					});
				}
				if($(targetNode).val()) {
					$(targetNode).closest(".refData").removeClass("selectionRequired");
				}
				if(mappingObj["cslFieldsMapping"][refType] && mappingObj["cslFieldsMapping"][refType][fieldType] && mappingObj["cslFieldsMapping"][refType][fieldType].validationrequired) {
					$(targetNode).closest(".refData").first().append('<span class="hidden" style="display:inline-block;margin-bottom:0px;vertical-align: bottom;color:green;margin-bottom:6px"><i class="material-icons" style="font-size:20px;object-fit: cover;vertical-align: bottom;">check_circle</i></span>');
				}
				var diffdata = htmldiff($(".previewContentTemp").html(), $(".previewContent").html());
				if(diffdata && $(".previewContent").html() && diffdata == $(".previewContent").html()) {
					$(this).closest('[data-type="popUp"]').find(".referrorMessage2").addClass("hidden");
				} else {
					$(this).closest('[data-type="popUp"]').find(".referrorMessage2").removeClass("hidden");
				}
                var authorStyle = ['author', 'editor','translator'];
				if (fieldType && authorStyle.indexOf(fieldType) >= 0) {
					$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).find(".RefSurName").contents().unwrap()
					$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).find(".RefGivenName").contents().unwrap()
                    $(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).html('<span class="RefSurName">'+$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).html()+'</span><span class="RefGivenName"></span>')
                    var prevData = $(targetNode).closest('.refData').next().find('.text-line');
                    var iddata  = (targetNode).closest('.refData').next().find('.text-line').attr('id-data')
                    var xnodepath  = (targetNode).closest('.refData').next().find('.text-line').attr('data-node-xpath')
                    $(targetNode).closest('.refData').next().remove();
                    var authorField;
					// Adding the customized author with delimiter field
					if(isModifyEditorPresent) {
                      authorField = $('.cslRefTemples').find('[data-temp="authorFieldWithDelimiter"]').clone().removeAttr('data-temp');
					}
					else if(isAuthorWuthSuffix) {
                      authorField = $('.cslRefTemples').find('[data-temp="authorFieldWithSuffix"]').clone().removeAttr('data-temp');
					}
					else {
                      authorField = $('.cslRefTemples').find('[data-temp="authorField"]').clone().removeAttr('data-temp');
					}
					$(authorField).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
					$(authorField).addClass("saveRefField");
                    $(authorField).append($('.cslRefTemples').find('[data-temp="addAndDeleteButton"]').clone().removeAttr('data-temp'));
                    $($(authorField).find('.text-line')[0]).html($(prevData[0]).html())
                    $($(authorField).find('.text-line')[1]).html($(prevData[1]).html())
                     $(authorField).find('.text-line').attr('id-data',iddata)
                    $(authorField).find('.text-line').attr('data-node-xpath',xnodepath)
                    $(targetNode).closest('.dataLine').append($(authorField));
					//set the selected attr for selected option
					if(fieldType){
						$(targetNode).find('option').removeAttr("selected")
						$(targetNode).find('option:selected').attr("selected", "selected")
					}
                }else if(fieldType) {
                	$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).find(".RefSurName").contents().unwrap()
					$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).find(".RefGivenName").contents().unwrap()
                	$(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).html($(targetNode).closest("[data-component]").find(".previewContent").find("#" + idtoChange).find(".RefSurName").remove().find(".RefGiveName").remove().html())
                    var prevData = $(targetNode).closest('.refData').next().find('.text-line');
                    var iddata  = (targetNode).closest('.refData').next().find('.text-line').attr('id-data')
                    var xnodepath  = (targetNode).closest('.refData').next().find('.text-line').attr('data-node-xpath')
                    
                    $(targetNode).closest('.refData').next().remove();
                    var authorField = $('.cslRefTemples').find('[data-temp="dataField"]').clone().removeAttr('data-temp');
					
					if(fieldType=='title'){
										currFieldNode = $(".cslRefTemples").find('[data-temp="titleField"]').clone().removeAttr("data-temp");
										$(currFieldNode).find('.retaincase').attr('style','padding-top:10px !important')
										$(currFieldNode).find('input[type=checkbox]').change(function() {
										  if ($(this).is(':checked')) {
										    $('.previewContent p').attr('data-retain-title','true')
										  } else {
										    $('.previewContent p').removeAttr('data-retain-title')
										  }
										});
					}else if(fieldType=='container-author' && $(".cslRefTemples").find('[data-temp="container-author"]').length >0){
						authorField = $(".cslRefTemples").find('[data-temp="container-author"]').clone().removeAttr("data-temp");
					}
					else if(/accessed/.test(fieldType) && !isModifyEditorPresent){
                            authorField = $(".cslRefTemples").find('[data-temp="accessedField"]').clone().removeAttr("data-temp");
						$(authorField).find('[id="RefAccesedMonth"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefAccesedMonth').attr('id'));
						$(authorField).find('[id="RefAccesedDay"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefAccesedDay').attr('id'));
					}
					else if(/event-date-range/.test(fieldType)){
						authorField = $(".cslRefTemples").find('[data-temp="dateRangeField"]').clone().removeAttr("data-temp");
						$(authorField).find('[id="RefStartMonth"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefStartMonth').attr('id'));
						$(authorField).find('[id="RefStartDay"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefStartDay').attr('id'));
						$(authorField).find('[id="RefEndMonth"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefEndMonth').attr('id'));
						$(authorField).find('[id="RefEndDay"] .text-line').attr('id-data',$(targetNode).closest("[data-component]").find(".previewContent").find('p .RefEndDay').attr('id'));
					}
					$(authorField).append($(".cslRefTemples").find('[data-temp="suffixField"]').clone().removeAttr("data-temp"));
                    $(authorField).find('[data-class="jrnlSuffixField"]').addClass("saveRefField");
					$(authorField).append($('.cslRefTemples').find('[data-temp="addAndDeleteButton"]').clone().removeAttr('data-temp'));
					if(/accessed/.test(fieldType) && !isModifyEditorPresent){
						["RefAccesedMonth","RefAccesedDay","RefLAD"].forEach(function (queryObj, currentKey) {
								if($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).length!=0){
									let id = ($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).attr('id'))
									if(queryObj == 'RefAccesedMonth'){
										$(authorField).find('[id="RefAccesedMonth"] .text-line').attr('id-data',id);
										$(authorField).find('[id="RefAccesedMonth"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
									}else if(queryObj == 'RefAccesedDay'){
										$(authorField).find('[id="RefAccesedDay"] .text-line').attr('id-data',id);
										$(authorField).find('[id="RefAccesedDay"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
									}else{
										$(authorField).find('[id="RefLAD"] .text-line').attr('id-data',id);
										$(authorField).find('[id="RefLAD"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
									}
								}
						});
					}
					else if(/event-date-range/.test(fieldType)){
						["RefStartDay", "RefStartMonth","RefStartYear", "RefEndDay", "RefEndMonth","RefEndYear"].forEach(function (queryObj, currentKey) {
							if($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).length!=0){
								let id = ($(targetNode).closest("[data-component]").find(".previewContent").find('p').find('.'+queryObj).attr('id'))
								if(queryObj == 'RefStartMonth'){
									$(authorField).find('[id="RefStartMonth"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefStartMonth"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}else if(queryObj == 'RefStartDay'){
									$(authorField).find('[id="RefStartDay"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefStartDay"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}else if(queryObj == 'RefStartYear'){
									$(authorField).find('[id="RefStartYear"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefStartYear"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}else if(queryObj == 'RefEndMonth'){
									$(authorField).find('[id="RefEndMonth"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefEndMonth"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}else if(queryObj == 'RefEndDay'){
									$(authorField).find('[id="RefEndDay"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefEndDay"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}else{
									$(authorField).find('[id="RefEndYear"] .text-line').attr('id-data',id);
									$(authorField).find('[id="RefEndYear"] .text-line').attr("data-node-xpath", '//*[@id="' + id + '"]');
								}
							}
					});
					}
					else{
						$($(authorField).find('.text-line')[0]).html($(prevData[0]).html())
						$($(authorField).find('.text-line')[0]).attr('id-data',iddata)
						$($(authorField).find('.text-line')[0]).attr('data-node-xpath',xnodepath)
					}
                    $(targetNode).closest('.dataLine').append($(authorField));
                    
					//set the selected attr for selected option
					if(fieldType){
						$(targetNode).find('option').removeAttr("selected")
						$(targetNode).find('option:selected').attr("selected", "selected")
					}

					var requiredFieldstag = $(targetNode).closest("[data-component]").find("#requiredFieldsvalidationTags");
					if(requiredFieldstag.length > 0 && $(targetNode).closest("[data-component]").find("#requiredFieldsvalidationTags").html()) {
						var requiredFields = $(targetNode).closest("[data-component]").find("#requiredFieldsvalidationTags").html().split(", ");
						var newRequiredFields = [];
						for(var key in requiredFields) {
							if(requiredFields[key] != fieldType) {
								newRequiredFields.push(requiredFields[key]);
							}
						}
						$(targetNode).closest("[data-component]").find("#requiredFieldsvalidationTags").html(newRequiredFields.join(", "));
						if(newRequiredFields.length < 1) {
							$(targetNode).closest("[data-component]").find(".vrTagsMissing").hide();
						}
					}
				}
				eventHandler.components.references.setDOMTreeData($(targetNode).closest(".dataLine").find("[data-node-xpath],[data-class='RefCollabAbbrev'],[data-class='jrnlSuffixField']"));
				var target = $(targetNode).closest(".dataLine").find("[data-node-xpath],[data-class='RefCollabAbbrev'],[data-class='jrnlSuffixField']");
					for (var i = 0; i < target.length; i++) {
					
						target[i].addEventListener("input", function (event) {
							eventHandler.components.references.setDOMTreeData($(this));
							event.preventDefault();
						}, false);
					}
			}
		},
		sortJSON: function(obj) {
			//convert JSON obj into array after sorting it
			var sortedArray = [];
			for(var i in obj) {
				// Push each JSON Object entry in array by [value, key]
				sortedArray.push([obj[i], i]);
			}
			obj = {};
			var objEleSorted = sortedArray.sort(function(a, b) {
				return a.toString().toLowerCase().localeCompare(b.toString().toLowerCase());
			});
			for(var k = 0; k < objEleSorted.length; k++) {
				var ele = objEleSorted[k];
				obj[ele[1]] = ele[0];
			}
			return obj;
		},
		sortJSONRefType: function (obj) {
			//convert JSON obj into array after sorting it
			var sortedArray = [];
			for (var i in obj) {
				// Push each JSON Object entry in array by [value, key]
				sortedArray.push([obj[i], obj[i].name, i]);
			}

			obj = {};
			var objEleSorted = sortedArray.sort(function (a, b) {
				return a.toString().toLowerCase().localeCompare(b.toString().toLowerCase());
			});

			return objEleSorted;
		},
		deleteCslField: function(param, targetNode) {
			const isModifyEditorPresent = $('[data-component="jrnlRefCsl_edit"]').attr('modify_ref_editor') ? true : false;
			targetNode.closest(".dataLine").find(".text-line").each(function(){
				IdToDelete = $(this).attr("id-data");
				if(IdToDelete) {
					targetNode.closest('[data-type="popUp"]').attr("ref-save-field-deleted", true);
					$("<extraspace> </extraspace>").insertAfter(targetNode.closest('[data-type="popUp"]').find(".previewContent").find("#" + IdToDelete))
					$("<extraspace> </extraspace>").insertBefore(targetNode.closest('[data-type="popUp"]').find(".previewContent").find("#" + IdToDelete))
					targetNode.closest('[data-type="popUp"]').find(".previewContent").find("extraspace").contents().unwrap()
					// Removing suffix non-text node by looping
					if(isModifyEditorPresent) {
						let  nonTextNode=$(targetNode.closest('[data-type="popUp"]')).find(".previewContent ").find("#" + IdToDelete)[0] && $(targetNode.closest('[data-type="popUp"]')).find(".previewContent ").find("#" + IdToDelete)[0].nextSibling;
						let  nonTextNodeClassName= nonTextNode && $(targetNode.closest('[data-type="popUp"]')).find(".previewContent ").find("#" + IdToDelete)[0].nextSibling.className;
						while(nonTextNode && !nonTextNodeClassName) {
							nonTextNode.remove()
							nonTextNode= $(targetNode.closest('[data-type="popUp"]')).find(".previewContent ").find("#" + IdToDelete)[0].nextSibling;
							if(nonTextNode) {
							nonTextNodeClassName= $(targetNode.closest('[data-type="popUp"]')).find(".previewContent ").find("#" + IdToDelete)[0].nextSibling.className;
							}
						}
					}
					targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("#" + IdToDelete).remove();
					targetNode.closest('[data-type="popUp"]').find(".previewContent ").find("." + IdToDelete).remove();
					targetNode.closest('[data-type="popUp"]').find(".previewContentTemp ").find("#" + IdToDelete).remove();
					targetNode.closest('[data-type="popUp"]').find(".previewContentTemp ").find("." + IdToDelete).remove();
					
				}
			})
			if ($('[data-component="jrnlRefCsl_edit"]').hasClass("screen3")) {
				$(".saveRefData").attr("data-message", "{'click':{'funcToCall': 'saveCSLReferenceData','channel':'components','topic':'references'}}");
				$(".saveRefDataScreen3").attr("data-message", "{'click':{'funcToCall': 'saveCSLReferenceData','channel':'components','topic':'references'}}");
			}
			if(targetNode.closest(".refContent").length > 0) {
				targetNode.closest(".refContent").attr("data-ref-edited", "true");
			}
			$(targetNode).closest(".dataLine").remove();
			eventHandler.components.references.setDOMTreeData(targetNode);
		}
	};
	return eventHandler;
})(eventHandler || {});

function ERROR(msg) {
	if(!msg) {
		return false;
	}
    kriya.removeAllNotify({'type':'reference'});
	kriya.notification({
		title: "ERROR",
		type: "error",
		content: msg,
		timeout: 5000,
		icon: "icon-warning2",
		"notifType":"reference",
	});
	console.log(msg);
}