
const retryConfig = {
    DEFAULT_RETRY_COUNT : 3,
    DEFAULT_RETRY_INTERVAL : 1000, // Base interval in ms
    DEFAULT_BACKOFF_FACTOR: 2,     // Multiplier for exponential backoff
    MAX_RETRY_INTERVAL: 30000,     // Maximum interval in ms
    JITTER_PERCENTAGE: 0.2         // Random jitter percentage
}

/**
 * ajaxWithRetry - Wrapper for $.ajax with retry capability + exponential backoff
 * @param {Object} options - Same options you pass to $.ajax
 * @param {Number} retryCount - Number of times to retry (default: 3)
 * @param {Number} baseDelay - Base delay in ms before first retry (default: 3000)
 * @returns {Promise} - Resolves with AJAX response or rejects after retries
 */
function ajaxWithRetry(options, onRetry=null, retryCount = retryConfig.DEFAULT_RETRY_COUNT, baseInterval = retryConfig.DEFAULT_RETRY_INTERVAL) {
    let attempts = 0;

    function attempt() {
        return $.ajax(options).catch((jqXHR) => {
            const shouldRetry = retryChecks(jqXHR);
            if (shouldRetry && attempts < retryCount) {
                attempts++;

                // Calculate exponential backoff with jitter
                const backoff = Math.pow(retryConfig.DEFAULT_BACKOFF_FACTOR, attempts - 1) * baseInterval;
                const jitter = backoff * retryConfig.JITTER_PERCENTAGE;
                const waitTime = Math.min(
                    retryConfig.MAX_RETRY_INTERVAL,
                    Math.floor(backoff + (Math.random() * jitter * 2 - jitter))
                );
                
                // 🔹 Call the retry notification callback
                if (typeof onRetry === "function") {
                    onRetry({
                        attempt: attempts,
                        maxAttempts: retryCount,
                        waitTime,
                        status: jqXHR.status
                    });
                }

                return new Promise((resolve) => setTimeout(resolve, waitTime)).then(attempt);
            }
            // Reject if retries exhausted or not retryable
            return $.Deferred().reject(jqXHR).promise();
        });
    }

    return attempt();
}

function retryChecks(error) {
    let shouldRetry = false;
    const retryStatusCodes = [502, 503, 504];
    const retryErrorCodes = ['ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED'];

    const statusCode = error?.status;
    const errorCode = error?.responseJSON?.error?.error?.code;

    if ((statusCode && retryStatusCodes.includes(statusCode)) || (errorCode && retryErrorCodes.includes(errorCode))) {
        shouldRetry = true;
    }
    return shouldRetry;
}
