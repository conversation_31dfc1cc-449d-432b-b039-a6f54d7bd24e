var kriyaEditor = function() {
	//default settings
	var settings = {};
	//add or override the initial setting
	var initialize = function(params) {
		function extend(obj, ext) {
			if (obj === undefined){
				return ext;
			}
			var i, l, name, args = arguments,
				value;
			for (i = 1, l = args.length; i < l; i++) {
				ext = args[i];
				for (name in ext) {
					if (ext.hasOwnProperty(name)) {
						value = ext[name];
						if (value !== undefined) {
							obj[name] = value;
						}
					}
				}
			}
			return obj;
		}
		settings = settings = extend(settings, params);
		return this;
	};

	var getSettings = function(){
		var newSettings = {};
		for (var prop in settings) {
			newSettings[prop] = settings[prop];
		}
		return newSettings;
	};

	return {
		init: initialize,
		getSettings: getSettings,
		settings: settings
	};
}();

/**
*	Extend eventHandler function with event handling
*/
(function (kriyaEditor) {
	settings = kriyaEditor.settings;
	var timer;
	kriyaEditor.user = {};
	kriyaEditor.user.name = $('#headerContainer .userProfile .username').text();
	if(kriya.config.content.role=='publisher'){
		kriyaEditor.user.name = kriyaEditor.user.name+' ('+kriya.config.content.customer.toUpperCase()+')';
	}else if (kriya.config.content.role){
		kriyaEditor.user.name = kriyaEditor.user.name+' ('+kriya.config.content.role.toUpperCase()+')';
	}else{
		kriyaEditor.user.name = kriyaEditor.user.name;
	}
	kriyaEditor.user.id   = $('#headerContainer .userProfile .username').attr('data-user-id');
	var checkDelStatus = true;
	kriyaEditor.init = {
		editor: function(dataNode) {
			setTimeout(function () {
				kriyaEditor.init.saveBackup();
			}, 15000);
						
			//kriyaEditor.init.trackArticle();
			return new wysihtml.Editor(dataNode, {
				//toolbar: "toolbar",
				parserRules: wysihtmlParserRules,
				useLineBreaks: false,
				autoLink: false
			});
		},
		saveBackup: function(){
			if ($('#contentContainer').attr('data-state') == 'read-only'){
				return true;
			}

			//Create snap shot when page load - jagan
			var content = $('div.WordSection1')[0].outerHTML
			content += $('#navContainer #queryDivNode')[0].outerHTML
			var parameters = {
				'customer': kriya.config.content.customer,
				'project': kriya.config.content.project,
				'doi': kriya.config.content.doi,
				'content': encodeURIComponent(content),
				'fileName' : 'page_load'
			};
			kriya.general.sendAPIRequest('createsnapshots',parameters,function(res){
				//console.log('Data Saved');
			});

			setInterval(function(){
				if(keeplive && keeplive.getUserStatus() != 'in-active'){
					var content = $('div.WordSection1')[0].outerHTML
					content += $('#navContainer #queryDivNode')[0].outerHTML
					var parameters = {
						'customer': kriya.config.content.customer,
						'project': kriya.config.content.project,
						'doi': kriya.config.content.doi,
						'content': encodeURIComponent(content)
					};
					kriya.general.sendAPIRequest('createsnapshots',parameters,function(res){
						//console.log('Data Saved');
					});
				}
			},600000);
		},
		trackArticle: function(){
			if ($('#contentContainer').attr('data-state') == 'read-only'){
				return true;
			}
			//setInterval(function(){
			if ($('#welcomeContainer:visible').length == 0){
				if ((typeof(timeTicker) != "undefined" && timeTicker.isRunning()) || typeof(timeTicker) == "undefined"){
					var d = new Date();
					var currDate = d.getUTCFullYear() +'-'+  ("0" + (d.getUTCMonth()+1)).slice(-2) +'-'+ ("0" + d.getUTCDate()).slice(-2);
					var currTime = ("0" + d.getUTCHours()).slice(-2) +':'+ ("0" + d.getUTCMinutes()).slice(-2) +':'+ ("0" + d.getUTCSeconds()).slice(-2);
					var parameters = {
						'customer':kriya.config.content.customer,
						'project':kriya.config.content.project,
						'doi':kriya.config.content.doi,
						'type':'mergeData',
						'skipElasticUpdate': 'true',
						'data': {
							'process': 'update',
							'xpath': '//workflow/stage[name[.="' + $('#contentContainer').attr('data-stage-name') + '"] and status[.="in-progress"]]',
							'content': '<stage><job-logs><log><end-date>' + currDate + '</end-date><end-time>' + currTime + '</end-time></log></job-logs></stage>'
						}
					};
					kriya.general.sendAPIRequest('updatedata', parameters, function(res){});
					setTimeout(function(){
						kriyaEditor.init.trackArticle();
					}, 120000);
				}
			}
			//},300000);
		},
		tracker: function(dataNode){
			if ($('#contentContainer').attr('data-state') == 'read-only'){
				return false;
			}
			kriyaEditor.init.trackeChanges();
			kriyaEditor.changeID = Math.floor(Date.now() / 1000);
			return new ice.InlineChangeEditor({
				element: dataNode,					// element to track - ice will make it contenteditable
				trackFormats: true,					// element to track - ice will make it contenteditable
				handleEvents: true,											// tell ice to setup/handle events on the `element`
				currentUser: {
					id: kriya.config.content.role + ' ' + kriyaEditor.user.id,
					name: kriyaEditor.user.name
				}, // set a user object to associate with each change
				plugins: [													// optional plugins
					'IceAddTitlePlugin',									// Add title attributes to changes for hover info
					//'powerpaste',
					{
						name: 'IceCopyPastePlugin',							// Track content that is cut and pasted
						settings: {
							preserve: 'p, span[id|class|data-citation-string|data-href], em, strong,ul,ol,li, i, u, sup, sub, h1[class], h2[class], h3[class], h4[class], h5[class], h6[class]',	// List of tags and attributes to preserve when cleaning a paste | Included  i, u, sup, sub, td by Raj
							preserveTrack: 'ul,ol,table', // List of tags and attributes to preserve adding track when paste
							beforePasteClean: function(html, targetElement){
								html = html.replace(/\u200B/g, ''); //Remove zero width space entity																
								html = html.replace(/\u000A/g, ' '); 
								if(html.match(/((?![\u0020-\uFFFF]+).)/g)){
									kriya.notification({
										title: 'Info',
										type : 'warning',
										content: 'Invalid characters found in pasted content.',
										icon: 'icon-warning2'
									});									
								}
								html = html.replace(/((?![\u0020-\uFFFF]+).)/g, '<span class="kriya-invalid-entity">?</span>');

								//validate content text length when paste - jagan
								var validatedVal = kriyaEditor.init.validateCharLength(targetElement, html);
								if(validatedVal == false){
									html = "";
								}

								return html;
							},
							afterPaste: function(targetElement){ //Callback function for paste was added by jagan to save the pasted data
								//If pasted element is multiple paragraph then using loop and save the content -Rajesh
								if($('#contentDivNode').find('[data-paste=true]').length > 0){
									$('#contentDivNode').find('[data-paste=true]').each(function(){
										$(this).removeAttr('data-paste');
										kriyaEditor.settings.undoStack.push($(this)[0]);
									});									
								}else{
									//If pasted element is list then add new id and save as newly inserted
									//else save the target element
									if($(targetElement).closest('ul:not([id]),ol:not([id])').length > 0){
										var listNode = $(targetElement).closest('ul:not([id]),ol:not([id])');
										listNode.attr('id', uuid.v4()).attr('data-inserted', 'true');
										listNode.find('*').each(function(){
											if(!$(this).is(kriya.config.preventAddId)){
												$(this).attr('id', uuid.v4());
											}											
										});
										kriyaEditor.settings.undoStack.push(listNode);

										if(kriyaEditor.settings.summaryData){
											$(kriyaEditor.settings.summaryData.added).each(function(){
												var addedBlockNode = $(this).closest(tracker.blockEls.join());
												if(!$(addedBlockNode).is(kriya.config.preventAddId)){
													$(addedBlockNode).attr('id', uuid.v4());
													$(addedBlockNode).attr('data-inserted', 'true');
													kriyaEditor.settings.undoStack.push(addedBlockNode);
												}												
											});
											$(kriyaEditor.settings.summaryData.characterDataChanged).each(function(){
												var existBlockNode = $(this).closest(tracker.blockEls.join());
												kriyaEditor.settings.undoStack.push(existBlockNode);
											});
											$(kriyaEditor.settings.summaryData.removed).each(function(){
												$(this).attr('data-removed', 'true');
												kriyaEditor.settings.undoStack.push($(this));
											});
										}
									}else{
										kriyaEditor.settings.undoStack.push(targetElement);
									}
								}
								kriyaEditor.init.addUndoLevel('paste (ctrl+v)');

								//Update figure meta length when typing - jagan
								kriyaEditor.init.updateTextLength(targetElement);
							},
							afterCut : function(param,targetElement){
								//Push to undoStack only
								//1) If the nodes are in DOM
								//2) If the same node is not already there in stack and 
								//3) If the node doesn't have it's parent node - Tharun - 25/03/2025
								if (kriyaEditor.settings.undoStack.length > 0) {
							        $(param).each(function() {
							            // Check if element is still in the DOM
							            if (!$.contains(document, this)) {
							                return;
							            }
							            let isChild = false, isDuplicate = false;
							            for (let i = 0; i < kriyaEditor.settings.undoStack.length; i++) {
							                let stackElement = kriyaEditor.settings.undoStack[i];
							                if (stackElement === this) {
							                    isDuplicate = true; // Element is already in stack
							                    break;
							                }
							                if (stackElement.contains(this)) {
							                    isChild = true; // Element is a child of an existing stack element
							                    break;
							                }
							            }
							            // Push only if it's neither a duplicate nor a child
							            if (!isChild && !isDuplicate) {
							                kriyaEditor.settings.undoStack.push(this);
							            }
							        });
							    } else {
							        $(param).each(function() {
							            // Check if element is still in the DOM
							            if ($.contains(document, this)) {
							                kriyaEditor.settings.undoStack.push(this);
							            }
							        });
							    }
								kriya.general.deleteSelectedCitationElement(param);
								kriya.general.deleteSelectedAQuery(param);
								kriyaEditor.init.addUndoLevel('ice-cut');
							}
						}
					}
				],
				menu: {
					edit: {
						title: 'Edit',
						items: 'undo redo | cut copy paste | selectall'
					}
				}
			});
		},
		validateCharLength: function(targetElement, text, preventNotice){
			if (targetElement && $(targetElement).length > 0 && $(targetElement).closest('p.jrnlSecPara').length > 0 && $(targetElement).closest('p.jrnlSecPara').attr('data-ignore-charLength') === 'true') {
				return true;
			}
			var returnVal = true;
			//validate content text length when paste - jagan
			var textLen = $('<div>' + text + '</div>').text().length;
			textLen = (textLen > 0)?textLen-1:0;
			if(kriya && kriya.config && kriya.config.checkCharlength){
				for(var cc=0;cc<kriya.config.checkCharlength.length > 0;cc++){
					var checkObj = kriya.config.checkCharlength[cc];
					if(targetElement && $(targetElement).closest(checkObj['selector']).length > 0){
						var clonedFigMeta = $(targetElement).closest(checkObj['selector']).clone(true);
						clonedFigMeta.find('.jrnlFigMetaHead').remove();
						var figMetaText = clonedFigMeta.cleanTrackChanges().text();// remove trackchanges
						if((figMetaText.length + textLen) >= checkObj['charLength']){
							if(!preventNotice){
								kriya.notification({
									title: 'ERROR',
									type : 'error',
									content: checkObj['errorMesg'],
									icon: 'icon-warning2'
								});
							}							
							returnVal = false;
							break;							
						}
					}
				}
			}
			return returnVal;
		},
		cleanupHTML: function(){
			//Clean up content ids in navigation panel - Jagan
			$('#navContainer #infoDivContent #refDivNode').find('.card-content [id]').removeAttr('id');
			$('#navContainer #infoDivContent #tableDivNode').find('.card-content [id]').removeAttr('id');
			$('#navContainer #infoDivContent #figureDivNode').find('.card-content [id]').removeAttr('id');
			$('#navContainer #infoDivContent #dataDivNode').find('.card-content [id]').removeAttr('id');
		},
		/**
		 * Update alternate text length in character length label
		 */
		updateTextLength: function(element){
			if($(element).length > 0){
				var figMeta = $(element).closest('.jrnlFigMeta[data-content-type="Alt Text"]');
				if($(figMeta).length > 0){
					//loop and get count of all the para inside jrnlFigMeta
					var totalLength = 0;
					$(figMeta).find('p').each(function() {
						var clonedText = $(this).clone(true);
						totalLength += clonedText.cleanTrackChanges().text().length; // Accumulate length
					});
					$(figMeta).find('.charLengthLabel .charLength').text(totalLength);
					if(totalLength > 0){
						$(figMeta).find('.charLengthLabel').addClass('green-text');
					}else{
						$(figMeta).find('.charLengthLabel').removeClass('green-text');
					}
				}
			}
		},
		trackeChanges: function (){
			//$('#changesDivNode .change-div').remove();
			//Initialize the tracked elements in the changes tab
			/*var trackNodes = document.querySelectorAll('.ins, .del, .sty, .styrm, [data-track="del"], [data-track="ins"]');
			for (var t = 0;t<trackNodes.length;t++) {
				var trackNode = trackNodes[t];
				eventHandler.menu.observer.addTrackChanges(trackNode, 'added');
			}*/
		},
		loadImage: function (){
			return true;
			// stopped loading pushing-pixels to avoid saving 'pushing-pixels' instead of original images
			setTimeout(function(){
				var imageNodes = document.querySelectorAll('img');
				for (var t = 0; t < imageNodes.length; t++) {
					var imageNode = imageNodes[t];
					var orginalImage = imageNode.getAttribute('data-alt-src');
					if(orginalImage){
						//imageNode.setAttribute('src', orginalImage);
						loadImage(imageNode, orginalImage)
					}
				}
				function loadImage(imageNode, orginalImage){
					imageNode.setAttribute('src', orginalImage);
					imageNode.onerror = function() {
						imageNode.setAttribute('src', '../images/pushing-pixels.gif');
					}
				}
				createCloneContent();
			},3000);
		},
		checkCitations: function(){
			$('.jrnlPossBibRef:empty').remove();
			$('.jrnlBibRef:empty').remove();
			$(kriya.config.containerElm).find('.jrnlPossBibRef').attr('class', 'jrnlPossRef').attr('data-type', 'jrnlBibRef');
			$('#navContainer label[for]').addClass('hidden');
			$('#untaggedDivNode').removeClass('active').addClass('hidden');
			$('.navDivContent').find('> .nav-action-icon.active').attr('data-last-active','true');
			$('.navDivContent').find('> .nav-action-icon').addClass('disabled');
			eventHandler.menu.edit.toggleNavPane('checkCiteDivNode',$('#navContainer #checkCiteDivNode'));
			$('#checkCiteDivNode').css('height', $(window).height() - $('#checkCiteDivNode').offset().top - $('#footerContainer').height());
			kriyaEditor.init.nextCitation();
		},
		nextCitation: function(action){
			var contentNode = $(kriya.config.containerElm);
			var citationClass = {
				'jrnlFigCaption':'jrnlFigRef',
				'jrnlTblCaption':'jrnlTblRef',
				'jrnlRefText':'jrnlBibRef',
				'jrnlSupplCaption':'jrnlSupplRef',
				'jrnlMapCaption':'jrnlMapRef',
			}
			
			if (action == 'notProvided'){
				$(contentNode).find('.jrnlPossRef.active').attr('class', 'jrnlUncitedRef');
			}
			else if (action == 'markCited'){
				var className = $('.selectedRef').attr('class').split(' ')[0];
				className = citationClass[className];
					if ($(contentNode).find('.jrnlPossRef.active').html() != $('#possCiteValue').html()){
						if(className == 'jrnlBibRef'){
						var e = $('#possCiteValue').text();
						e = e.replace(/([\[\/\.\(\)\'\u2019\"])/g, '\\$1');
						var r = new RegExp('^(.*?)'+ e + '(.*?)$');
						var prefixString = $(contentNode).find('.jrnlPossRef.active').text().replace(r, '$1');
						if (prefixString != ""){
							$(contentNode).find('.jrnlPossRef.active').before(prefixString);
						}
						var prefixString = $(contentNode).find('.jrnlPossRef.active').text().replace(r, '$2');
						if (prefixString != ""){
							$(contentNode).find('.jrnlPossRef.active').after(prefixString);
						}

					}
					$('#possCiteValue').prepend($(contentNode).find('.jrnlPossRef.active .jrnlQueryRef').clone())
						$(contentNode).find('.jrnlPossRef.active').html($('#possCiteValue').html())
				}
				var rid = [];
				$('.selectedRef').each(function(){
					rid.push($(this).attr('id'));
				})
				rid = rid.join(' ');
				$(contentNode).find('.jrnlPossRef.active').attr('class',className).attr('data-citation-string', ' ' + rid + ' ').attr('data-mark-cited', rid);
			}
			else if (action == 'notCitation'){
				$(contentNode).find('.jrnlPossRef.active').contents().unwrap();
			}

			if ($(contentNode).find('.jrnlPossRef.active').length == 0){
				$(contentNode).find('.jrnlPossRef:first').addClass('active');
				// kriyaEditor.settings.undoStack.push($(contentNode).find('.jrnlPossRef.active'));
			}else{
				var prevCite = $(contentNode).find('.jrnlPossRef.active');
				$(prevCite).removeClass('active');
				if ($(prevCite).nextInDOM('.jrnlPossRef').length > 0){
					$(prevCite).nextInDOM('.jrnlPossRef').addClass('active');
				}
			}
			if($(contentNode).find('.jrnlPossRef.active').length > 0){
				var savenode = $(contentNode).find('.jrnlPossRef.active').closest('p,h1,h2,h3,h4,h5.h6')
			}
			$('#refListDiv').html('');
			$('#markCited').addClass('disabled');
			$('#notCited,#skipCitation').removeClass('disabled');
			$('.toggle-refs').addClass('hidden');
			if ($(contentNode).find('.jrnlPossRef.active').length > 0){
				$(contentNode).find('.jrnlPossRef.active')[0].scrollIntoView();
				$('#possCiteValue').html($(contentNode).find('.jrnlPossRef.active').html());
				if ($('.jrnlPossRef').attr('data-type') == 'jrnlBibRef'){
					if ($('.jrnlPossRef.active').attr('data-tag-type') == 'AUTHORYEARREF'){
						$('.toggle-refs').removeClass('hidden');
					}
					var year = $('.jrnlPossRef.active').text();
					author = year.replace(/^([^\s]+).*$/ig, '$1');
					year = year.replace(/^[a-z\s\,\.\;]+/ig, '');
					year = year.replace(/[a-z\s\,\.\;]+/ig, '');
					$('.resource_reference').find('.jrnlRefText').each(function(){
						var thisRef = $(this).clone();
						var id = $(this).closest('*[data-panel-id]').attr('data-panel-id')
						$(thisRef).attr('data-id',id).attr('id',id)
						if ($('.jrnlPossRef.active').attr('data-tag-type') == 'AUTHORYEARREF'){
							thisRef.addClass('hidden');
							var ry = new RegExp(year.replace(/(?=[() ])/g, '\\'))
							var ra = new RegExp(author.replace(/(?=[() ])/g, '\\'))
							if (ry.test($(this).find('.RefYear').text()) || ra.test($(this).find('.RefAuthor:first').text())){
								thisRef.attr('related-refs', 'true').removeClass('hidden');
							}
						}
						$('#refListDiv').append(thisRef);
					});
				}
				if ($('.jrnlPossRef').attr('data-type') == 'jrnlTblRef'){
					$(contentNode).find('.jrnlTblCaption').each(function(){
						$('#refListDiv').append($(this).clone());
					});
				}
				if ($('.jrnlPossRef').attr('data-type') == 'jrnlFigRef'){
					$(contentNode).find('.jrnlFigCaption').each(function(){
						$('#refListDiv').append($(this).clone());
					});
				}
			}else{
				$('#checkCiteDivNode').addClass('hidden').removeClass('active');
				// kriyaEditor.init.save();
			}
			if(savenode){
				kriyaEditor.settings.undoStack.push(savenode);
				kriyaEditor.init.addUndoLevel('citation');
			}
		},
		toggleReferences: function(){
			$('.toggle-refs').toggleClass('btn-default')
			$('.toggle-refs').toggleClass('btn-warning');
			if ($('#refListDiv .jrnlRefText.hidden').length > 0){
				$('#refListDiv .jrnlRefText').removeClass('hidden');
			}else{
				$('#refListDiv .jrnlRefText').addClass('hidden');
				$('#refListDiv .jrnlRefText[related-refs]').removeClass('hidden');
			}
		},
		undoRedo: function(dataNode){
			var x = document.querySelector.bind(document);
			kriyaEditor.settings.undoStack = [];
			kriyaEditor.settings.contentNode = dataNode;
			kriyaEditor.settings.startValue = kriyaEditor.settings.contentNode.innerHTML;
			
			var sel = rangy.getSelection();
			var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
			kriyaEditor.settings.startCaretID = blockNode.attr('id');
			kriyaEditor.settings.startCaretPos = getCaretCharacterOffsetWithin(blockNode[0]);

			kriyaEditor.settings.changeNode = document.getElementById('navContainer');
			kriyaEditor.settings.contextStartValue = kriyaEditor.settings.changeNode.innerHTML;
			kriyaEditor.settings.forkedNode = false;

			var contentUndoObject = {
				container: kriyaEditor.settings.contentNode,
				oldValue : kriyaEditor.settings.contentNode.innerHTML,
				newValue : ''
			}

			kriyaEditor.settings.undo = x('[data-tooltip="Undo"]');
			kriyaEditor.settings.redo = x('[data-tooltip="Redo"]');
			kriyaEditor.settings.save = x('[data-tooltip="Save"]');

			stack = new Undo.Stack();
			stack.isDirty = false;
			var newValue = "";

			/**
			 * hook key-up events to track the changes made to content
			 */
			//var keysPressed = [];
			kriyaEditor.settings.contentNode.addEventListener('keyup', function(event){

				//#468 - Auto save content in editor on focus out using keystrokes - Prabakaran.A(<EMAIL>)
				if(stack.isDirty && kriyaEditor.lastSelection){
					var lastSelectionTargetElement = kriyaEditor.lastSelection.anchorNode;
				}
				//End of #468
				//keys[event.keyCode] = false;
				//var index = keysPressed.indexOf(event.key.toLocaleLowerCase());
				//keysPressed.splice(index, 1);

				var range = rangy.getSelection();
				if (range.anchorNode == null && range.rangeCount == 0){//clicked some where outside the content - JAI 19-02-2018
					return false;
				}
				//#469 - All Customers | Object Citations | Insert citations - Helen.J (<EMAIL>)
				kriya.selection = rangy.getSelection().getRangeAt(0);
				kriya.selectedNodes = kriya.selection.getNodes();
				//End of #469

				//When space,break,backSpace,delete will type at that time showNonPrintChar()function will trigger for showing non-printable characters.
				if(range.anchorNode && $(range.anchorNode.parentNode).length > 0 && (event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 32 || (event.shiftKey && event.keyCode == 13 ))){
					//The below line was commented because it consuming more time when typing - jagan
					//kriya.general.showNonPrintChar(range.anchorNode.parentNode);
				}

				var targetElement = range.anchorNode;
				kriyaEditor.lastSelection = range;

				if (targetElement && targetElement.nodeType == 3){
					targetElement = targetElement.parentNode;
				}

				//Clear the highlight when start typing not when cursor move
				if(!(event.keyCode >= 33 && event.keyCode <= 40) && $('#clonedContent .highlight.findOverlay').length > 0){
					// eventHandler.menu.findReplace.searchText('clear');
					$('.highlight.findOverlay').remove();					
				}
				
				//Clear spelling highligh when start type  - jagan
				/*if(!(event.ctrlKey && event.keyCode == 67) && event.keyCode != 91 && !(event.keyCode >= 33 && event.keyCode <= 40) && $(".icon-spell-check").parent().hasClass('active') && $('#clonedContent .highlight.spelling').length > 0){
					eventHandler.menu.spellCheck.checkDocument('',$(".icon-spell-check").parent()[0]); //to remove spell check highlights
				}*/

				//#10954 Add data-constructed-edited as true if default is false - Jagan
				if($(targetElement).closest('[data-constructed-edited="false"]').length > 0){
					$(targetElement).closest('[data-constructed-edited="false"]').attr('data-constructed-edited', 'true');
				}

				//return false if the target is non editable
				if((targetElement && targetElement.hasAttribute('data-editable') && targetElement.getAttribute('data-editable')=='false') || $(targetElement.closest('*[data-editable="false"]')).length >0 || kriya.config.preventTyping && ($(targetElement).closest(kriya.config.preventTyping).length > 0)){
					//Added condition to skip return when event is 13(enter) and the class is jrnlDeleted inorder to handle the duplicate id for new node on content selected and pressed enter
					if(!(event.keyCode == 13 && $(targetElement).closest(kriya.config.preventTyping)[0].hasAttribute('class') && $(targetElement).closest(kriya.config.preventTyping)[0].getAttribute('class') == 'jrnlDeleted')){
						//#468 - Auto save content in editor on focus out using keystrokes - Prabakaran.A(<EMAIL>)
						// trigger save, if undostack length is greater than 0 on key events - JAI - 09-07-2019
						if ((stack.isDirty || kriyaEditor.settings.undoStack.length > 0) && ((event.keyCode >= 33 && event.keyCode <= 40) || (event.keyCode == 46 || event.keyCode == 8))){
							kriyaEditor.init.addUndoLevel('non-editable-keystrokes', lastSelectionTargetElement?lastSelectionTargetElement:targetElement);
						}
						//End of #468
						return false;
					}
				}

				//update citation if page type text to citation - jagan
				var clonedTarget = $(targetElement).clone(true);
				clonedTarget.find('.jrnlBibRef').remove();
				if(range.anchorNode.nodeType == 3 && $(targetElement).find('.jrnlBibRef:not([data-track="del"])').length > 0 && $(targetElement).hasClass('ins') && !((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) && $(clonedTarget).text().match(/[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/)){
					$(targetElement).html($(targetElement).html()); //to join all text nodes
					if($(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling && $(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling.nodeType == 3){
						targetElement = $(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling;
					}
				}
				var keycode = event.keyCode;
				if (
					(keycode > 47 && keycode < 58)   || // number keys
					keycode == 32 || keycode == 13   || // spacebar & return key(s)
					(keycode > 64 && keycode < 91 && keycode != 67 && keycode != 90)   || // letter keys
					(keycode > 95 && keycode < 112)  || // numpad keys
					(keycode > 185 && keycode < 193) || // ;=,-./` (in order)
					(keycode > 218 && keycode < 223)   // [\]' (in order)
					){
						if($('#contentContainer').attr('data-state') && $('#contentContainer').attr('data-state') != 'read-only' && (kriya.config.content.role == "typesetter" || kriya.config.content.role == "publisher" || kriya.config.content.role == "preeditor")){
							if($(targetElement).parents('table') && $(targetElement).parents('table').length>0){
								var tableID = '';
								if($(targetElement).parents('.jrnlTblBlock').length >0 && $(targetElement).parents('.jrnlTblBlock').find('.floatLabel').length>0){
									tableID = $(targetElement).parents('.jrnlTblBlock').find('.floatLabel').text()
								}
								else if($(targetElement).parents('.jrnlInlineTable[data-id]:first,[data-id]:first').length >0){
									tableID = "Table " + $(targetElement).parents('.jrnlInlineTable[data-id]:first,[data-id]:first').attr('data-id')
								}
								if(!$(targetElement).parents('table:first')[0].hasAttribute('data-modified-table')){
									$(targetElement).parents('table:first').attr('data-modified-table','true')
								}
							}
						}
					}
				var prevNode = $(targetElement)[0].previousSibling;
				while(prevNode && prevNode.nodeType != 3 && $(prevNode).closest('.del, [data-track="del"]').length > 0){
					prevNode = $(prevNode)[0].previousSibling;
				}
				if(!((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) && prevNode && $(prevNode).is('.jrnlBibRef:not([data-track="del"])') && citeJS.settings && citeJS.settings.R && citeJS.settings.R.citationType == "1"){
					var trackText = $(targetElement).text();
					var pageNumMatches = trackText.match(/^[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/);
					if(pageNumMatches && pageNumMatches[1]){
						trackText = trackText.replace(/^[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/g, '');

						if($(targetElement)[0].nodeType == 3){
							$(targetElement)[0].nodeValue = trackText;
						}else{
							$(targetElement).text(trackText);
						}

						var pageNum = pageNumMatches[1];
						citeJS.general.updatePageNumInCitation(prevNode, pageNum);
						kriyaEditor.init.addUndoLevel('save-page-no', prevNode.closest('p'));
					}
				}
				switch (event.keyCode) {
					case 46: // delete
					case 8: // backspace
						//Remove the text field when it becomes empty - jagan
						if($(targetElement).closest('[data-text-field]:empty').length > 0){
							$(targetElement).closest('[data-text-field]:empty').attr('data-removed', 'true');
						}

						//Update figure meta length when typing - jagan
						kriyaEditor.init.updateTextLength(targetElement);
						
						//Add data to save for the content remove when backspace and delete press
						if(kriyaEditor.settings.summaryData && kriyaEditor.settings.summaryData.removed.length > 0){
							$(kriyaEditor.settings.summaryData.removed).each(function(){
								if(this.nodeType != 3 && $(this).attr('id')){
									var removedId = $(this).attr('id');
									var removeNode = this;
									if($(kriya.config.containerElm+ ' #'+removedId).length < 1){
										var check = false;
										// Don't include if the node is child of any removed node
										$(kriyaEditor.settings.summaryData.removed).each(function(){
											if($(this).find('#'+removedId).length > 0){
												check = true;
											}
										});
										if(!check){
											$(removeNode).attr('data-removed', 'true');
											kriyaEditor.settings.undoStack.push(removeNode);
										}
									}
								}
							});

							if(kriyaEditor.settings.summaryData.reparented.length > 0){
								var reparentSaveNode = $(kriyaEditor.settings.summaryData.reparented).parents('[id]:first');
								if(reparentSaveNode.length > 0){
									if($(reparentSaveNode).closest('ul[id],ol[id]').length > 0){
										reparentSaveNode = $(reparentSaveNode).closest('ul[id],ol[id]');
									}
									kriyaEditor.settings.undoStack.push(reparentSaveNode);
								}
							}
              //Removed from editor_save.js and added here to avoid undo stack failure by Anuraja on 10-May-2021
							//remove empty para and head level while saving added by vijayakumar on 19-04-2021
							if($(targetElement)[0].nodeName && /^(h[1-6]|P$)/i.test($(targetElement)[0].nodeName) && $(targetElement).html() == ""){
								if($(targetElement).closest('body').length > 0 && $(targetElement).closest('.body').children().length == 1 && $(targetElement).closest('.body').children()[0] == $(targetElement)[0]){
									//don't delete parent if body have only one child - By anuraja on 07-May-2021
								}else{
									$(targetElement).attr('data-removed','true');
									$(targetElement).remove();
								}
							}

							var mergedNode = $(targetElement).closest('p[id],div[id],table[id],h1[id],h2[id],h3[id],h4[id],h5[id],h6[id],li[id],ol[id]');
							kriyaEditor.settings.undoStack.push(mergedNode);
							kriyaEditor.init.addUndoLevel('merge-node');
							kriyaEditor.settings.summaryData = false;

						}else{
							kriyaEditor.init.addUndoLevel('delete-key', targetElement);
						}
						break;
					case 32: // space
						//console.log('add undo level called, space keys pressed');
						//The below line was commented because it consuming more time when typing - jagan
						//kriyaEditor.init.addUndoLevel('space-key', targetElement);
							
						//Update figure meta length when typing - jagan
						kriyaEditor.init.updateTextLength(targetElement);

						kriyaEditor.init.addUndoLevel('character-key', targetElement);
						break;
					case 33: // page up
					case 34: // page down
					case 35: // end
					case 36: // home
					case 37: // arrow key: left
					case 38: // arrow key: up
					case 39: // arrow key: right
					case 40: // arrow key: down
						if (stack.isDirty){
							//console.log('add undo level called, navigation keys pressed');
							//#468 - Auto save content in editor on focus out using keystrokes - Prabakaran.A(<EMAIL>)
							kriyaEditor.init.addUndoLevel('editable-keystrokes', lastSelectionTargetElement?lastSelectionTargetElement:targetElement);
							//End of #468
						}
						break;
					default:
						var keycode = event.keyCode;
						if (
							(keycode > 47 && keycode < 58)   || // number keys
							keycode == 32 || keycode == 13   || // spacebar & return key(s) (if you want to allow carriage returns)
							(keycode > 64 && keycode < 91)   || // letter keys
							(keycode > 95 && keycode < 112)  || // numpad keys
							(keycode > 185 && keycode < 193) || // ;=,-./` (in order)
							(keycode > 218 && keycode < 223)   // [\]' (in order)
							){

								//Add new id if text field doesn't have id
								if($(targetElement).closest('[data-text-field]:not([id])').length > 0){
									$(targetElement).closest('[data-text-field]:not([id])').attr('data-inserted', 'true');
									$(targetElement).closest('[data-text-field]:not([id])').attr('id', uuid.v4());
								}

								if (! event.ctrlKey && !event.metaKey){
									//console.log('add undo level called, character keys pressed');

									//Update figure meta length when typing - jagan
									kriyaEditor.init.updateTextLength(targetElement);

									//#355 - Id attribute missing for Paragraphs in Firefox - Lakshminarayanan.S (<EMAIL>)
									// When a paragraph is split, the chrome takes the first paragraph as forked node but firefox takes the second node. Hence workaround for firefox
									//#533 - Save event is not triggered for on click - kriyaEditor.settings.forkedNode is undefined - prabakaran.a(<EMAIL>)
									if (navigator.userAgent.search("Firefox") > 0 && kriyaEditor.settings.forkedNode){
										//End of #533
										//forkedNode not conected with dom because of that next sibling and previous sibling comming as null-kirankumar
										if(!kriyaEditor.settings.forkedNode.previousElementSibling){
											var targetForForkNode = targetElement;
											while (targetForForkNode && kriya.config.blockElements.indexOf(targetForForkNode.nodeName.toLocaleLowerCase()) == -1){
												targetForForkNode = targetForForkNode.parentNode;
											}
											kriyaEditor.settings.forkedNode = targetForForkNode;
										}
										kriyaEditor.settings.forkedNode = kriyaEditor.settings.forkedNode.previousElementSibling;
										kriyaEditor.settings.summaryData = kriyaEditor.settings.forkedNode.nextElementSibling;
										kriyaEditor.settings.summaryData.added =  kriyaEditor.settings.forkedNode.nextElementSibling;
									   }
									//old contition - if (keycode == 13 && kriyaEditor.settings.forkedNode && kriyaEditor.settings.forkedNode.hasAttribute('id')){
									if (keycode == 13 && kriyaEditor.settings.forkedNode){

										if (targetElement.nodeType == 3){
											targetElement = targetElement.parentNode;
										}
										while (kriya.config.blockElements.indexOf(targetElement.nodeName.toLocaleLowerCase()) == -1){
											targetElement = targetElement.parentNode;
										}
										//when we click enter from h1-h6 then creating new element as div for that i replacing newly created tag name to forkednode tag name -#2618
										var forkeNode = kriyaEditor.settings.forkedNode;
										//When cliked enter from deletedNode in jrnlFigMeta rename the jrnlDeleted to old-class
										let forkedClass = forkeNode.getAttribute('class');
										let newNodeClass = targetElement.getAttribute('class');
										if(!checkDelStatus && keycode == 13 && (forkedClass == "jrnlDeleted" && newNodeClass == "jrnlDeleted")){
											var oldClass = targetElement.getAttribute('old-class');
											$(targetElement).attr('class',oldClass);
											$(targetElement).removeAttr('data-editable');
											checkDelStatus = true;
										}
										if(targetElement.nodeName != forkeNode.nodeName){
											targetElement = $(targetElement).renameElement(forkeNode.nodeName)[0];
											$(targetElement).attr('class',$(forkeNode).attr('class'));
											var range = document.createRange();
											range.setStart(targetElement, 0);
											selection.removeAllRanges();
											selection.addRange(range);
											//anchorNode not created in range for that i given this line.
											range = rangy.getSelection();
											if(kriyaEditor.settings.summaryData.added.length > 0){
												kriyaEditor.settings.summaryData.added[0] = targetElement;
											}
										}
										//old contition -if(kriyaEditor.settings.summaryData && (kriyaEditor.settings.summaryData.added.length > 0 || kriyaEditor.settings.summaryData)){
										if(kriyaEditor.settings.summaryData){
											$(kriyaEditor.settings.summaryData.added).each(function(){
												if(this.nodeType != 3){
													if(!$(this).is(kriya.config.preventAddId)){
														$(this).attr('id', uuid.v4());
													}
													if($(kriyaEditor.settings.undoStack).filter($(this).parents()).length == 0){
														$(this).attr('data-inserted', 'true');
														//Rename the class to jrnlSecPara if new node is a jrnlEqnPara
														if($(this).hasClass('jrnlEqnPara')){
															$(this).removeClass('jrnlEqnPara');
															$(this).addClass('jrnlSecPara');
														}
														//data-id should not be duplicate after break the one element should remove data-id attr added by vijayakumar on 11-12-2018
														$(this).removeAttr('data-id');
														kriyaEditor.settings.undoStack.push(this);
														noIdCheck = $(this).next();
														while(noIdCheck.length > 0 && !noIdCheck[0].hasAttribute('id'))
														{
															noIdCheck.attr('id',uuid.v4());
															noIdCheck.attr('data-inserted', 'true');
															kriyaEditor.settings.undoStack.push(noIdCheck[0]);
															noIdCheck = noIdCheck.next();
														}
														noIdCheck = $(this).prev();
														while(noIdCheck.length > 0 && !noIdCheck[0].hasAttribute('id'))
														{
															noIdCheck.attr('id',uuid.v4());
															noIdCheck.attr('data-inserted', 'true');
															kriyaEditor.settings.undoStack.push(noIdCheck[0]);
															noIdCheck = noIdCheck.prev();
														}
													}
												}

											});
										}
										//End of #355

										if ($(kriyaEditor.settings.forkedNode).hasClass('activeElement')){
											$(kriyaEditor.settings.forkedNode).removeClass('activeElement');
										}
										kriyaEditor.settings.undoStack.push(kriyaEditor.settings.forkedNode);
										kriyaEditor.settings.undoStack.push(targetElement);
										//#535 -While entering two times,cursor is comes out of list but one extra list is added in last.
										kriyaEditor.settings.undoStack.push($(targetElement).prev());
										//End of #535
										kriyaEditor.init.addUndoLevel('keyup-event');
										kriyaEditor.settings.forkedNode = false;
									}else if(!kriyaEditor.settings.shortcut){										
										kriyaEditor.init.addUndoLevel('character-key', targetElement);
									}else{
										kriyaEditor.settings.shortcut = false; //Reset shortcut value										
									}
								}else if((event.ctrlKey || event.metaKey) && keycode == 86){
									//paste was handled in ice paste callback afterPaste function - jagan
								}else if((event.ctrlKey || event.metaKey) && keycode == 88){
									kriyaEditor.settings.undoStack.push(targetElement);
									kriyaEditor.init.addUndoLevel('cut (ctrl+x)');
								}
							}
				}

				//add/remove track changes to the side panel
				if (range.anchorNode.nodeType == 3){
					var parentNode = range.anchorNode.parentNode;
				}else if(kriya.selection && kriya.selection.startOffset != kriya.selection.endOffset){
				//Get the tracked elements when select the text and press delete or backspace key
					var parentNode = kriya.selection.startContainer.nextElementSibling;
					// if the current deleted node is the only child for its parent (inside table)
					if (! parentNode && $(kriya.selection.startContainer.parentNode).hasClass('del')){
						parentNode = kriya.selection.startContainer.parentNode;
					}
				}else{
					var parentNode = range.anchorNode;
				}
				if ($(parentNode).hasClass('del') || $(parentNode).hasClass('ins')){
					eventHandler.menu.observer.addTrackChanges(parentNode, 'added');
					//addUndoLevel('change', 'changesDivNode');
				}

				if ($(parentNode).siblings('.del:first,.ins:first').length > 0){
					//Add track changes for the inserted and deleted element when select the text and start typing
					eventHandler.menu.observer.addTrackChanges($(parentNode).siblings('.del:first,.ins:first')[0], 'added');
				}

				if ($(range.anchorNode).siblings('.del:first,.ins:first').length > 0){
					//Add track changes for the inserted and deleted element when select the text and start typing
					eventHandler.menu.observer.addTrackChanges($(range.anchorNode).siblings('.del:first,.ins:first')[0], 'added');
				}

				var underlayNode = $('#clonedContent #' + $(parentNode).closest('*[clone-id]').attr('clone-id'));
				if (underlayNode.length > 0 && $(underlayNode).find('.highlight').length > 0){
					eventHandler.menu.findReplace.reSearch(underlayNode);
				}

				if($(targetElement).closest('.jrnlArtTitle').length > 0){
					kriyaEditor.settings.undoStack.push($(targetElement).closest('.jrnlArtTitle'));
					callUpdateArticleCitation($(targetElement).closest('.jrnlArtTitle'), '.jrnlCitation');
				}


			});
			//#509 - IE related - restricts resizing of border in IE - Rajasekar T (<EMAIL>)
			kriyaEditor.settings.contentNode.addEventListener( 'mscontrolselect', function( evt ) {
				evt.returnValue = false;
				return false;
			});
			//End of #509

			kriyaEditor.settings.contentNode.addEventListener('keydown', function(event){
				//console.log(rangy.getSelection().anchorNode.parentNode);
				//if menu item is active - jai
				/*if ($('.kriyaMenuControl.active').length > 0) {
					keycode = event.keyCode;
					if (keycode == 27){
						$('.kriyaMenuControl.active').removeClass('active');
					}
					event.preventDefault();
					event.stopPropagation();
				}else{*/
					var range = rangy.getSelection();
					var targetElement = range.anchorNode;
					if (targetElement && targetElement.nodeType == 3){
						targetElement = targetElement.parentNode;
					}
					kriyaEditor.settings.shortcut = false; //Reset shortcut value					
					//update citation if page type text to citation and type navigation key - jagan
					var clonedTarget = $(targetElement).clone(true);
					clonedTarget.find('.jrnlBibRef').remove();
					//if range.anchorNode is undefined code will break
					if(range.anchorNode && range.anchorNode.nodeType == 3 && $(targetElement).find('.jrnlBibRef:not([data-track="del"])').length > 0 && $(targetElement).hasClass('ins') && (event.keyCode >= 33 && event.keyCode <= 40) && $(clonedTarget).text().match(/[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/)){
						$(targetElement).html($(targetElement).html()); //to join all text nodes
						if($(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling && $(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling.nodeType == 3){
							targetElement = $(targetElement).find('.jrnlBibRef:not([data-track="del"])')[0].nextSibling;
						}
					}
					//Added by Joy to delete the parent node, if the general footnote is empty, only when backspace and delete was pressed
					if((event.keyCode == 8 || event.keyCode == 46) && $(targetElement).closest('.jrnlTblFoot').length > 0){
						var dataCloned = $(targetElement).closest('.jrnlTblFoot').clone(true);
						$(dataCloned).find('.removeNode').remove()
						$(dataCloned).find('.markAs').remove()
						$(dataCloned).cleanTrackChanges().text()
						//if cloned node is empty, remove the parent node
						if(dataCloned.text() == ''){
							$(targetElement).closest('.jrnlTblFoot').kriyaTracker('del');
						}
					}
					if(targetElement){
						var prevNode = $(targetElement)[0].previousSibling;
					}
					while(prevNode && prevNode.nodeType != 3 && $(prevNode).closest('.del, [data-track="del"]').length > 0){
						prevNode = $(prevNode)[0].previousSibling;
					}
					// Added by Anuraja #11603 - Click right or left arrow without click mouse, Error will occurred due to selection not get defined. because we initialized the selection on click event of contentdiv
					// Now define selection event on arrow keys
					if(event.keyCode >= 37 && event.keyCode <= 40){
						selection = rangy.getSelection();
					}					
					if((event.keyCode >= 33 && event.keyCode <= 40) && prevNode && $(prevNode).is('.jrnlBibRef:not([data-track="del"])') && citeJS.settings && citeJS.settings.R && citeJS.settings.R.citationType == "1"){
						var trackText = $(targetElement).text();
						var pageNumMatches = trackText.match(/^[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/);
						if(pageNumMatches && pageNumMatches[1]){
							trackText = trackText.replace(/^[\,\.\s]+pp?[\.\s]+(([\d]+)([\-\.][\d]+)?)/g, '');
							if($(targetElement)[0].nodeType == 3){
								$(targetElement)[0].nodeValue = trackText;
							}else{
								$(targetElement).text(trackText);
							}

							var pageNum = pageNumMatches[1];
							citeJS.general.updatePageNumInCitation(prevNode, pageNum);
							kriyaEditor.init.addUndoLevel('save-page-no', prevNode.closest('p'));
						}
					}

					keycode = event.keyCode || event.which;
					if (
						(keycode > 47 && keycode < 58) || // number keys
						keycode == 32 || keycode == 13 || // spacebar & return key(s) (if you want to allow carriage returns)
						keycode == 46 || keycode == 8 || // spacebar & return key(s) (if you want to allow carriage returns)
						(keycode > 64 && keycode < 91) || // letter keys
						(keycode > 95 && keycode < 112) || // numpad keys
						(keycode > 185 && keycode < 193) || // ;=,-./` (in order)
						(keycode > 218 && keycode < 223) // [\]' (in order)
						){
						if($(targetElement).closest('td p.jrnlDeleted').length > 0 && $(targetElement).closest('td p.jrnlDeleted').attr('old-class')){
							var oldClass = $(targetElement).closest('td p.jrnlDeleted').attr('old-class');
							$(targetElement).closest('td p.jrnlDeleted').attr('class', oldClass);
						}
						if (citeJS.settings && citeJS.settings.multiArtTitle && kriya && kriya.config && kriya.config.preventMerging) {
							kriya.config.preventMerging = kriya.config.preventMerging.replace(/\,?\.jrnlArtTitle/, '')
						}
						
						// Allow the Enter key event to function when the cursor is positioned at the end of fully deleted jrnlSecPara only in jrnlFigMeta.
						//So setting the checkDelStatus to continue the event without terminating in next condition - PRD-973 -Tharun
						var range = rangy.getSelection().getRangeAt(0);
						var isAtEnd = range.startContainer.length === range.startOffset;
						var isAtStart = range.startOffset === 0;
						if (isAtEnd && !isAtStart) {
							if(keycode == 13 && $(targetElement).closest('.jrnlDeleted').length > 0 && $(targetElement).closest('.jrnlFigMeta').length > 0){
								checkDelStatus = false;
							}
						}

						if(targetElement &&  targetElement != null && checkDelStatus && (($('#contentContainer').attr('data-state') == 'read-only') || (targetElement.hasAttribute('data-editable') && targetElement.getAttribute('data-editable') == 'false') || (targetElement.hasAttribute('class') && /Block$/.test(targetElement.getAttribute('class'))) || $(targetElement.closest('*[data-editable="false"]')).length > 0 || $(targetElement.closest('*[data-track="del"]')).length > 0 || $(targetElement.closest('*[data-editable="false"]')).length > 0 || (kriya.config.preventTyping && $(targetElement).closest(kriya.config.preventTyping).length > 0))){
							if (((event.ctrlKey || event.metaKey) && (!/^(X|V)$/i.test(event.key))) || event.altKey) {} else {
								if ($('.nonEditableMessage').length == 0){
									//GIT ID #10233 - To set old class for para if, data inserted into deleted para by Tamil Selvan on 04-Aug-2020
									if($(targetElement).closest('td p.jrnlDeleted').length > 0 && $(targetElement).closest('td p.jrnlDeleted').attr('old-class')){
										var oldClass = $(targetElement).closest('td p.jrnlDeleted').attr('old-class');
										$(targetElement).closest('td p.jrnlDeleted').attr('class', oldClass);
									}
									if((keycode == 8 || keycode == 46) && $(targetElement).closest('.jrnlDeleted').length > 0){
										//Do nothing - when delte key press and target is jrnlDeleted then don't show msg - jagan
									}else{
										kriyaEditor.settings.summaryData.added = [];
										var errMsg = "Editing is not allowed";
										if(kriya.config.preventTypingErMsg && typeof(kriya.config.preventTypingErMsg) == "object"){
											var classNames = Object.keys(kriya.config.preventTypingErMsg);
											for(var c=0;c<classNames.length;c++){
												var objKey = classNames[c];
												if($(targetElement).closest('.' + objKey).length > 0){
													errMsg = kriya.config.preventTypingErMsg[objKey];
													errMsg = errMsg.replace(/\{\$role\}/, kriya.config.content.role);
													break;
												}
											}
										}
										var notificationID = kriya.notification({
											title: 'Info',
											type: 'error',
											timeout: 5000,
											content: errMsg,
											icon: 'icon-warning2'
										});
										$('#'+notificationID).addClass('nonEditableMessage');
									}
								}
								event.preventDefault();
								event.stopPropagation();
							}
						}
						
						//add del node if closest non breaking space;
						if (!(event.ctrlKey || event.metaKey) && ($(kriya.selection.getNodes()).closest('.nonBreakingSpace').length > 0 || $(targetElement).closest('.nonBreakingSpace').length > 0)) {
							if ($(targetElement).closest('.nonBreakingSpace').length > 0 ){
								var removeNode = $(targetElement).closest('.nonBreakingSpace').kriyaTracker('del');
								$(targetElement).closest('.nonBreakingSpace').removeAttr('class');
							}else{
								var removeNode = $(kriya.selection.getNodes()).closest('.nonBreakingSpace').kriyaTracker('del');
								$(kriya.selection.getNodes()).closest('.nonBreakingSpace').removeAttr('class');
							}
							kriyaEditor.settings.undoStack.push(removeNode);
							if (kriyaEditor.settings.undoStack.length > 0) {
								kriyaEditor.init.addUndoLevel('nonBreakingSpace');
							}
						}

						//Clear spelling highligh when start type only printable characters, but ignore (ctrl + c) when user copies text - Jai
						if ($(".icon-spell-check").parent().hasClass('active') && $('#clonedContent .highlight.spelling').length > 0){
							if (!((event.ctrlKey || event.metaKey) && event.keyCode == 67)){
									eventHandler.menu.spellCheck.checkDocument('',$(".icon-spell-check").parent()[0]); //to remove spell check highlights		
							}else if ((event.ctrlKey || event.metaKey) && (/^(X|V)$/i.test(event.key))) {
							//check if user cuts/paste, close the spell check modal
								eventHandler.menu.spellCheck.checkDocument('',$(".icon-spell-check").parent()[0]); //to remove spell check highlights		
							}
						}

						if(kriya && kriya.config && kriya.config.checkCharlength && !event.ctrlKey && !event.metaKey && keycode != 8 && keycode != 46){
							var preventNotice = (keycode == 32)?true:false;
							var validatedVal  = kriyaEditor.init.validateCharLength(targetElement, "", preventNotice);
							if(validatedVal == false){
								event.preventDefault();
								event.stopPropagation();
							}
						}
					}

					//Prevent mergeing figmeta para from non editable head - jagan
					if((event.keyCode == 8 || event.keyCode == 46) && $(targetElement).closest('.jrnlFigMeta p:empty').length > 0 && $(targetElement).closest('.jrnlFigMeta p:empty').prev('.jrnlFigMetaHead').length > 0){
						event.preventDefault();
						event.stopPropagation();
					}

					switch (event.keyCode || event.which) {
						case 46: // delete
						case 13:
							if (citeJS.settings && citeJS.settings.multiArtTitle) {
								var selector = "span[class$='Ref'], span[class$='Ref activeElement'],.jrnlTblFoot,.jrnlSubDisplay"
							} else {
								var selector = "span[class$='Ref'], span[class$='Ref activeElement'],.jrnlTblFoot,.jrnlArtTitle,.jrnlSubDisplay"
							}
							if ($(targetElement).closest(selector).length > 0) {
								event.preventDefault();
								event.stopPropagation();
							}

							//handling enter
							while (targetElement && kriya.config.blockElements.indexOf(targetElement.nodeName.toLocaleLowerCase()) == -1){
								targetElement = targetElement.parentNode;
							}
							kriyaEditor.settings.forkedNode = targetElement;
							break;
						//case 9:
							//handling list when tab was clicked - JAGAN
							// this is now handled as wysihtml command, which already has a tab event in it - JAI 29-04-2017
						case 16:// shift+tab   //when we un list the items at that time it is not saving properlly and it is duplicating nodes-kirankumar
						case 9:// shift+tab
							//when we are click shift tab on paragraph then editor getting saved
							//if(event.shiftKey && event.keyCode == 9){
							//When evere shift+tab happend and if any node not deleted in editor at that time kriyaEditor.settings.summaryData.removed will come as undefined at that time here code code will break
							//if((event.shiftKey && event.keyCode == 9) && kriyaEditor.settings.summaryData.removed.length > 0){
							if((event.shiftKey && event.keyCode == 9) && (kriyaEditor.settings.summaryData.removed && kriyaEditor.settings.summaryData.removed.length > 0)){								
								//When we unlist the list items at that time old list is not removing in xml it will remove old list-kirankumar
								if(kriyaEditor.settings.summaryData && kriyaEditor.settings.summaryData.removed.length > 0){
									$(kriyaEditor.settings.summaryData.removed).each(function(){
										if(this.nodeType != 3 && $(this).attr('id')){
											var removedId = $(this).attr('id');
											var removeNode = this;
											if($(kriya.config.containerElm+ ' #'+removedId).length < 1){
												var check = false;
												// Don't include if the node is child of any removed node
												$(kriyaEditor.settings.summaryData.removed).each(function(){
													if($(this).find('#'+removedId).length > 0){
														check = true;
													}
												});
												if(!check){
													$(removeNode).attr('data-removed', 'true');
													kriyaEditor.settings.undoStack.push(removeNode);
												}
											}
										}
									});

									if(kriyaEditor.settings.summaryData.reparented.length > 0){										
										//Sort the reparented nodes
										var reparentedIndex = {};
										$(kriyaEditor.settings.summaryData.reparented).each(function(){
											var node = $(this);
											if(!$(this).is('.body > *, .front > *,.back > *')){
												node = $(this).parents('.body > *, .front > *,.back > *').first();
											}											
											var idAttr = $(node).attr('id');
											if(!idAttr){
												idAttr = uuid.v4();
												$(node).attr('id', idAttr);
											}
											reparentedIndex[idAttr] = $(node).index();
										});
										var sortedIDs = Object.keys(reparentedIndex).sort(function(a,b){return reparentedIndex[a]-reparentedIndex[b]});										
										
										//If reparentted first node is a inserted node then save that next to first node because first inserted node in body will not have sibbling id. (#8287) - jagan
										if(sortedIDs.length > 1 && reparentedIndex[sortedIDs[0]] == 0 && $(kriya.config.containerElm + ' #' + sortedIDs[0]).attr('data-inserted') == "true"){
											var firstNode = sortedIDs[0];
											sortedIDs[0] = sortedIDs[1];
											sortedIDs[1] = firstNode;
										}

										if(sortedIDs.length > 0 && $(kriya.config.containerElm).find('#' + sortedIDs[0]).prev('[id]').length == 0 && $(kriya.config.containerElm).find('#' + sortedIDs[(sortedIDs.length - 1)]).next('[id]').length > 0){
											sortedIDs = sortedIDs.reverse();
										}

										//Push the node to stack
										for(var s=0;s<sortedIDs.length;s++){
											var nodeID = sortedIDs[s];
											var contentNode = $(kriya.config.containerElm + ' #' + nodeID);
											kriyaEditor.settings.undoStack.push(contentNode);
										}
									}

									var mergedNode = $(targetElement).closest('p[id],div[id],table[id],h1[id],h2[id],h3[id],h4[id],h5[id],h6[id],li[id],ol[id]');
									kriyaEditor.settings.undoStack.push(mergedNode);
									//kriyaEditor.init.addUndoLevel('merge-node');
									kriyaEditor.settings.summaryData = false;

								}else{
									kriyaEditor.settings.undoStack.push(targetElement);
								}
								
								
								var startNode = $(rangy.getSelection().getRangeAt(0).startContainer.parentNode).closest('p');
								var endNode   = $(rangy.getSelection().getRangeAt(0).endContainer.parentNode).closest('p');
								var endNodeId = $(endNode).attr('id');
								var startNodeId = $(startNode).attr('id');
								var selectedNodes = [];
								if(endNodeId != startNodeId){
									selectedNodes.push(startNode);
									var collectSelectedNodes = true;
									while(collectSelectedNodes){
										if($(startNode).next().attr('id') == endNodeId){
											selectedNodes.push(startNode.next());
											collectSelectedNodes = false;
										}else{
											selectedNodes.push(startNode.next());
											startNode = $(startNode).next();
										}
									}
								}else{
									selectedNodes.push(startNode);
								}
								
								for(var thisNode in selectedNodes){
									var currElement = selectedNodes[thisNode];
									if(($(currElement).closest('li').length == 0)&&($(currElement).hasClass('jrnlListPara'))){
										$(currElement).attr('id',uuid.v4())
										$(currElement).attr('class','jrnlSecPara').attr('data-inserted','true');

										if ($(currElement).prev().length > 0 && ($(currElement).prev()[0].nodeName == 'UL' || $(currElement).prev()[0].nodeName == 'OL')){
											var prevList = $(currElement).prev();
											//var prevListType = $(prevList).attr('type');
											//var prevListStyle = $(prevList).attr('style');
											kriyaEditor.settings.undoStack.push(prevList);
										}
										if($(currElement).next().length > 0 && ($(currElement).next()[0].nodeName == 'UL' || $(currElement).next()[0].nodeName == 'OL')){
											var nextList = $(currElement).next().attr('data-inserted','true');
											$(nextList).attr('type',$(nextList).css('list-style-type'));
											//$(nextList).attr('style',prevListStyle);
											kriyaEditor.settings.undoStack.push(nextList);
										}
										kriyaEditor.settings.undoStack.push(currElement);
									}
								}

								if(kriyaEditor.settings.undoStack.length > 0){
									kriyaEditor.init.addUndoLevel('shift+tab');
								}
							}
							break;
						case 8: // backspace
							//console.log('add undo level called, delete/backspace keys pressed');
							//Update figure meta length when deleting alt text in jrnlFigMeta
							kriyaEditor.init.updateTextLength(targetElement);
							break;
					}
				//}

				// short cut key controls
				var sk = "";
				if (event.ctrlKey) sk += "ctrl+";
				if (event.metaKey) sk += "ctrl+";
				if (event.altKey) sk += "alt+";
				if (event.shiftKey) sk += "shift+";
				if (! /(control|alt|shift)/i.test(event.key)){
					sk += event.key;
				}
				if (sk.match(/\"/)) {
					sk = "";
				}
				//keysPressed[event.keyCode] = event.key.toLocaleLowerCase();
				//keysPressed.push(event.key.toLocaleLowerCase());
				//sk = keysPressed.join('+');
				//sk = sk.replace('control', 'ctrl');
				if ($('[data-shortcut="'+sk+'"]').length > 0){
					if ($('[data-shortcut="'+sk+'"]').attr('data-action') != undefined){
						var action = $('[data-shortcut="'+sk+'"]').attr('data-action');
						if (action == "focus"){
							$('[data-shortcut="'+sk+'"]').focus();
						}else{
							$('[data-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
						}
					}else{
						$('[data-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
					}
					kriyaEditor.settings.shortcut = true;					
					event.preventDefault();
					event.stopPropagation();
				}
				//added by Anuraja on 11-05-2021 to handle mac short cut keys  https://stackoverflow.com/questions/10527983/best-way-to-detect-mac-os-x-or-windows-computers-with-javascript-or-jquery
				if(navigator&& navigator.platform && /^(Mac)/.test(navigator.platform)){
					if ($('[data-mac-shortcut="'+sk+'"]').length > 0){
						if ($('[data-mac-shortcut="'+sk+'"]').attr('data-action') != undefined){
							var action = $('[data-mac-shortcut="'+sk+'"]').attr('data-action');
							if (action == "focus"){
								$('[data-mac-shortcut="'+sk+'"]').focus();
							}else{
								$('[data-mac-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
							}
						}else{
							$('[data-mac-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
						}
						event.preventDefault();
						event.stopPropagation();
					}
				}
				if (event.key === "Backspace") {
					let sel = rangy.getSelection();
				
					if (sel.rangeCount > 0) {
						let range = sel.getRangeAt(0);
						let parentElement = range.commonAncestorContainer;
				
						if (parentElement.nodeType !== 1) {
							parentElement = parentElement.parentNode;
						}
				
						let prevElement = parentElement.previousSibling;
				
						// Check if the element doesnt have any text or images.
						if (parentElement.innerText.trim() === "" && parentElement.querySelectorAll('img').length == 0) {
						event.preventDefault();
							let nextFocusElement = parentElement.previousSibling || parentElement.parentNode;
							parentElement.remove();
				
							if (nextFocusElement) {
								let newRange = rangy.createRange();
								newRange.selectNodeContents(nextFocusElement);
								newRange.collapse(false); 
								sel.setSingleRange(newRange);
							}
							return false;
						}
					}
				}	
			});
			kriyaEditor.settings.contentNode.addEventListener('mouseleave', function(event){
				var range = rangy.getSelection();
				if (range.rangeCount > 0 && range.text() != ""){
					var r = range.getRangeAt(0).startContainer.parentElement
					if ($(r).closest('#contentDivNode').length > 0){
						//kriya.selection = range.getRangeAt(0);
						//kriya.selectedNodes = kriya.selection.getNodes();
						//findReplace.general.highlight(range, 'textSelection');
					}
				}
			});
			kriyaEditor.settings.contentNode.addEventListener('click', function(event){
				if(stack.isDirty && kriyaEditor.lastSelection){
					var targetElement = kriyaEditor.lastSelection.anchorNode;
					kriyaEditor.init.addUndoLevel('content-click', targetElement);
				}
				$('.highlight.textSelection,.highlight.queryText').remove();
				$('#clonedContent [data-table-id]').remove();

				//Remove the foot note if newly insrted is empty
				$('.jrnlTblFoot:empty').not($(event.target).closest('.jrnlTblFoot')).remove();
				var emptyEthic = $('.jrnlEthicsFN[data-save-node] p:empty');
				if(emptyEthic.not($(event.target).closest(emptyEthic)).length > 0){
					emptyEthic.parent().remove();
				}
				$('.dropdown-button').dropdown('close');

				var hrefLink = '';
				if($(event.target).closest('a').length > 0 && $(event.target).closest('a').attr('href')){
					hrefLink = $(event.target).closest('a').attr('href');
				}
				if($(event.target).closest('.jrnlSupplSrc') && $(event.target).closest('.jrnlSupplSrc').attr('href')){
					hrefLink = $(event.target).closest('.jrnlSupplSrc').attr('href');
				}
				if(hrefLink != ''){
					window.open(hrefLink);
				}
				let targetElementData = $(event.target)[0]; //Added by Anuraja to add space after editable label if caption is not added, so user can add new caption.
				if(/label/.test(targetElementData.className) && $(targetElementData).attr('data-edit-label') && (!targetElementData.nextSibling || targetElementData.nextSibling.nodeType == 3 && targetElementData.nextSibling.nodeValue.trim() === '' && targetElementData.nextSibling.nodeValue != '\u00A0')){
					targetElementData.after(document.createTextNode('\u00A0'));
				}
				if(eventHandler && eventHandler.query && eventHandler.query.action) {
                    eventHandler.query.action.removeSpellRefs();
					eventHandler.query.action.disableSpinnerForAuthorQuery();
				}
			});

			kriyaEditor.settings.contentNode.addEventListener('focusout', function(event){
				if(stack.isDirty && kriyaEditor.lastSelection){
					var targetElement = kriyaEditor.lastSelection.anchorNode;
					kriyaEditor.init.addUndoLevel('focusout-key', targetElement);
				}
			});

			kriyaEditor.settings.contentNode.addEventListener('scroll', function(event){
				$('#clonedContent').scrollTop($('#contentDivNode').scrollTop())
				$('.suggestions,.resolve-query').remove();
				if (!$(".kriya-tags-suggestions").hasClass( "hidden" )){
					$('.kriya-tags-suggestions').addClass('hidden');
				}
				setTimeout(function(){
					if ($('.query-div.hover-inline').find('.cancelReply').length > 0){
						//if we open authore popup and scroll at that time two js events triggering (scroll,focusout),in this situation same function alling two time for restricting
						//that i added temp-data-removed attr.this attr life time before compleate this scroll event.
						$('.query-div.hover-inline').find('.cancelReply').attr('temp-data-removed','true');
						$('.query-div.hover-inline').find('.cancelReply').trigger('click');
					}else if ($('.query-div.hover-inline').find('.com-close').length > 0){
						$('.query-div.hover-inline').find('.com-close').trigger('click');
					}else{
						$('.query-div.disabled').remove();
						$('.query-div.hover-inline').removeAttr('style').removeClass('hover-inline');
					}
					
					// if ($('.lqc-div.hover-inline').find('.com-close').length > 0 && ( $(targetNode)[0].className == 'btn-floating btn-small com-close' && $(targetNode)[0].closest(".lqc-div").hasAttribute('qid') && $('.lqc-div.hover-inline')[0].hasAttribute('qid') && $(targetNode)[0].closest(".lqc-div").getAttribute('qid') == $('.lqc-div.hover-inline')[0].getAttribute('qid') ) ){
					// 	$('.lqc-div.hover-inline').find('.com-close').trigger('click');
					// }
					if ($('.lqc-div.hover-inline').find('.com-close').length > 0 && $(targetNode)[0].className != 'btn-floating btn-small com-close'){
						$('.lqc-div.hover-inline').find('.com-close').trigger('click');
					}
					$('.highlight.queryText_test,style.queryCss,.image-hover').remove();
				},100);
				if ($('.getBack').length > 0){
					var floatXpath = $('.getBack').attr('data-float-xpath');
					var floatnode = kriya.xpath($('.getBack').attr('data-float-xpath'));
					if(floatnode.length > 0){
						if ($(floatnode).position().top + $(floatnode).height() < 0 || $(floatnode).position().top > $('#contentDivNode').height()){
							$('.getBack').remove();
						}
					}
				}
				//addundo level already handled in focus out and it's slowing the scroll event
				/*if(stack.isDirty && kriyaEditor.lastSelection){
					var targetElement = kriyaEditor.lastSelection.anchorNode;
					kriyaEditor.init.addUndoLevel('scroll-key', targetElement);
				}*/
				//Removed the checking of visible property as it delays scroll event #17370
				// var blockMenu = $('[data-component*="_menu"][data-component^="jrnl"]:visible');
				var blockMenu = $('[data-component*="_menu"][data-component^="jrnl"]');
				if(blockMenu.length > 0){
					$(blockMenu).css({
						'display' : '',
						'opacity' : '0',
						'left' : '',
						'top' : '',
					});
				}
			});

			/*document.getElementsByTagName('body')[0].addEventListener('keydown', function(event){
				if ($(event.target).closest('#contentDivNode').length > 0) return false;
				// short cut key controls
				var sk = "";
				if (event.ctrlKey) sk += "ctrl+";
				else if (event.altKey) sk += "alt+";
				else if (event.shiftKey) sk += "shift+";
				if (! /(control|alt|shift)/i.test(event.key)){
					sk += event.key;
				}
				if (sk.match(/\"/)){
					sk = "";
				}
				//commented by kirankumar issue:-ctrl+z not working in popup
				if ($('[data-shortcut="'+sk+'"][data-context="body"]').length > 0){
					if ($('[data-shortcut="'+sk+'"]').attr('data-action') != undefined){
						var action = $('[data-shortcut="'+sk+'"]').attr('data-action');
						if (action == "focus"){
							$('[data-shortcut="'+sk+'"]').focus();
						}else{
							$('[data-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
						}
					}else{
						$('[data-shortcut="'+sk+'"]:not(.disabled)').trigger('click');
					}
					event.preventDefault();
					event.stopPropagation();
				}
			});*/
			function initScrollTbl(){
				//Initialize the scroll to table for handling search highlight
				$(kriya.config.containerElm + ' .jrnlTblContainer').each(function(){
					this.addEventListener('scroll', function(event){
						var tableID = $(this).find('table').attr('id');
						$('#clonedContent div[data-table-id="' + tableID + '"]').scrollTop($(this).scrollTop());
						//Initialize the horizontal scroll to table for handling search highlight - aravind
						$('#clonedContent div[data-table-id="' + tableID + '"]').scrollLeft($(this).scrollLeft());
					});
				});
			}
			function stackUI() {
				if(stack.canRedo()){
					kriyaEditor.settings.redo.classList.remove("disabled")
				}else{
					kriyaEditor.settings.redo.classList.add("disabled")
				}

				if(stack.canUndo() && kriyaEditor.settings.save){
					kriyaEditor.settings.undo.classList.remove("disabled");
					kriyaEditor.settings.save.classList.remove("disabled");
					$('.pdfViewHeader .pdfViewMenu').removeClass('disabled');
					$('.pdfViewHeader #pdfNotice').removeClass('hidden');
				}else{
					//#348 All| Tables| Undo Redo cycle must not get affected after Accept/ Reject - Prabakatran.A(<EMAIL>)
					//#533 Undo is not working - ReferenceError: event is not defined - Prabakaran A(<EMAIL>)
					if(typeof event != 'undefined' && !$(event.currentTarget).is('[data-name="MenuBtnUndo"]')){
						kriyaEditor.settings.startValue = kriyaEditor.settings.contentNode.innerHTML;

						var sel = rangy.getSelection();
						var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
						kriyaEditor.settings.startCaretID = blockNode.attr('id');
						kriyaEditor.settings.startCaretPos = getCaretCharacterOffsetWithin(blockNode[0]);

						kriyaEditor.settings.contextStartValue = kriyaEditor.settings.changeNode.innerHTML;
					}
					//End of #348
					if (kriyaEditor.settings.save){
						kriyaEditor.settings.undo.classList.add("disabled");
						kriyaEditor.settings.save.classList.add("disabled");
					}
				}
			}

			kriyaEditor.settings.undo.addEventListener("click", function () {
				var range = rangy.getSelection();
				var targetElement = range.anchorNode;
				if (stack.isDirty){
					kriyaEditor.init.addUndoLevel('undo-key', targetElement);
				}
				//#178 -Tables: Copy & Paste (Undo&Redo changes does not take place in tables)- Prabakaran.A-Focusteam
				if($('[data-type="popUp"]:not([data-component="contextMenu"]):not([data-component="trackChange"]):visible').length == 0 || $('[data-name="tableMenu"]:visible').length > 0)
				//End of the code #178 -Tables: Copy & Paste (Undo&Redo changes does not take place in tables)- Prabakaran.A-Focusteam
				stack.undo();
			});

			kriyaEditor.settings.redo.addEventListener("click", function () {
				//#178 -Tables: Copy & Paste (Undo&Redo changes does not take place in tables)- Prabakaran.A-Focusteam
				if($('[data-type="popUp"]:visible').length == 0 ||  $('[data-name="tableMenu"]:visible').length > 0)
				//End of the code #178 -Tables: Copy & Paste (Undo&Redo changes does not take place in tables)- Prabakaran.A-Focusteam
				stack.redo();
			});

			initScrollTbl(); //Init table scroll when page load
			
			stack.changed = function () {
				stackUI();
				initScrollTbl(); //Init table scroll when stack change
			};
		},
		addUndoLevel: function(action, element){
			var stackObj = [];
			/*var newElement = null;
			var oldValue   = null;
			var newValue   = null;*/

			//Add content div node to undo object
			var newElement = kriyaEditor.settings.contentNode;
			var oldValue   = kriyaEditor.settings.startValue;
			//Commented because of high time consumption #4201
			//kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;

			var oldCaretPos = kriyaEditor.settings.startCaretPos;
			var oldCaretId  = kriyaEditor.settings.startCaretID;

			// added to stop adding undo levels when opened in read-only mode
			// this was marking the stack as dirty and later dummy save was called
			// which was adding unnecessary logs - Jai 17-May
			if ($('#contentContainer').attr('data-state') == 'read-only'){
				return;
			}

			//Commented because of high time consumption #4201
			//var newValue   = newElement.innerHTML;

			if ((action == 'delete-key') || (action == 'character-key')) {
				stack.isDirty = true;
				stack.oldValue = oldValue;
				//Commented because of high time consumption #4201
				//stack.newValue = newValue;
				stack.oldCaretPos = oldCaretPos;
				stack.oldCaretId = oldCaretId;

				if (kriyaEditor.settings.save){
					(stack.isDirty)?(
						kriyaEditor.settings.undo.classList.remove("disabled"),
						kriyaEditor.settings.save.classList.remove("disabled")
					):(
						kriyaEditor.settings.undo.classList.add("disabled"),
						kriyaEditor.settings.save.classList.add("disabled")
					);
				}
				return;
			}

			if (kriyaEditor.settings.undoStack.length > 0){
				element = kriyaEditor.settings.undoStack;
			}
			kriyaEditor.settings.undoStack = [];
			if(element && $(element).length > 0){
				//Collect the all ids of the elements that add in the stack
				//If element doesn't have ids then get closest one
				//#395-All| List| Unable to revert List to para( #364 Requirement changed added para) -<EMAIL>
				//In lists, when LI is converted to paragraph, <p> is removed and inserted as new paragraph. The edited list must be saved before the inserted tag.
				var saveNodeIdsTemp = {
					'removed': [],
					'edited': [],
					'inserted': [],
					'moved': []
				};
				var saveNodeIds = [];
				//End of #395-All| List| Unable to revert List to para( #364 Requirement changed added para) -<EMAIL>

				//Duplicate id handling 28-10-2024
				//Check to identify the presence of duplicate id's:
				//Take current node id and compare it with prev and next node id, if same then mark the current id as data-removed
				//Get all the duplicate nodes and generate new uuid and mark as data-inserted.
				$(element).each(function(){
					let saveNode = $(this);
					let prevEle = saveNode[0] ? saveNode[0].previousSibling : null;
    				let nextEle = saveNode[0] ? saveNode[0].nextSibling : null;
					let prevID = false;
					let nextID = false;
					if(saveNode && $(prevEle).length > 0 && prevEle.nodeType != 3 && prevEle.hasAttribute('id')){
						prevID = prevEle.getAttribute('id');
					}
					if(saveNode && $(nextEle).length > 0 && nextEle.nodeType != 3 && nextEle.hasAttribute('id')){
						nextID = nextEle.getAttribute('id');
					}
					if (saveNode.attr('id') == prevID || saveNode.attr('id') == nextID){
						let newElement = document.createElement(saveNode[0].nodeName);
						newElement.setAttribute('id', saveNode.attr('id'));
						newElement.setAttribute('data-removed', 'true');
						element.push(newElement);
	
						let duplicateElements = document.querySelector('#contentDivNode').querySelectorAll('[id="' + saveNode.attr('id') + '"]');
						duplicateElements.forEach(function(ele, index) {
							ele.setAttribute('id', uuid.v4());
							ele.setAttribute('data-inserted', 'true');
							if (!element.filter(function(node) { return $(node).is(ele); }).length) {
								element.push(ele); // Add the new duplicate element back to saveNodes if not present
							}
						});
					}
				});
				var elementsToSave = element;
				$(element).each(function(i, obj){
					if($(this).closest('#compDivContent').length > 0){
						return; //If closest node is a component content then continue the loop
					}
					if($(this).closest('[id]:not(' + kriya.config.invalidSaveNodes + ')').length > 0){
						var saveNode = $(this).closest('[id]:not(' + kriya.config.invalidSaveNodes + ')');
						//to avoid saving newly inserted span, sup, sub, em, italic, underlines, bolds - JAI
						//Check the length of closest save node - jagan
						// to get closest element which is not a span, em, i, bold, strong - JAI 03-07-2019
						if ($(saveNode).length > 0 &&  /^(S|E|I|U|B)/i.test($(saveNode)[0].nodeName) && $(saveNode).parent().closest('[id]:not(' + kriya.config.invalidSaveNodes + ',span,em,i,b,strong)').length > 0){
							saveNode = $(saveNode).parent().closest('[id]:not(' + kriya.config.invalidSaveNodes + ',span,em,i,b,strong)');
						}
						
						//If the save node is inside a float block then save the block element - Jagan 04-07-2019
						//When the action is move to float bloack do not perform the below code as it was duplicating the content - Added by Saravanan
						if(kriya.config.saveBlockNodes && $(saveNode).closest(kriya.config.saveBlockNodes).length > 0 && action !== 'move-to-float-block'){
							saveNode = $(saveNode).closest(kriya.config.saveBlockNodes);
						}

						//when ids duplicate inside the save node duplicated ids replaced with new ids-kirankumar
						var ids=[];
						$($(saveNode).find('[id]')).each(function(){
							var pid = $(this).attr('id');
							if(pid){
								if(ids.indexOf(pid) == -1){
									ids.push(pid);
								}else{
									$(saveNode).find('[id = '+pid+']').each(function(){
										$(this).attr('id',uuid.v4());
									});
								}
							}					
						});
						//Filter the invalid nodes to check pushToArray modify by jagan
						var pushToArray = true;
						$(elementsToSave).filter(function(){
							if(!$(this).is(kriya.config.invalidSaveNodes)){
								return this;
							}
						}).each(function(){
							if ($(this).attr('id') && $(this).find('#'+ saveNode.attr('id')).length > 0 && saveNode.attr('data-removed') != "true"){
								pushToArray = false;
							}
						});

						//check if the current node is a child of a node which is already in the saving list - jai
						$.each(saveNodeIds, function(s, j){
							if (saveNodeIds[s].inserted && $('#' + saveNodeIds[s].inserted).find('#'+ saveNode.attr('id')).length > 0){
								pushToArray = false;
							}
							if (saveNodeIds[s].moved && $('#' + saveNodeIds[s].moved).find('#'+ saveNode.attr('id')).length > 0){
								pushToArray = false;
							}
							if (saveNodeIds[s].edited && $('#' + saveNodeIds[s].edited).find('#'+ saveNode.attr('id')).length > 0){
								pushToArray = false;
							}
						});
						
						
						//var saveNode = $(this).siblings('[id]:not(' + kriya.config.invalidSaveNodes + ')').prevObject;
						// here we are collecting changes in an array as object with action as key and id as value
						// "saveNodeIdsTemp" variable is only used for validation purpose
						// validation: to avoid duplicate id getting collected in "saveNodeIds" collection
						var isIDAdded = false;
						if(saveNode[0].hasAttribute('data-removed') && pushToArray){
							if (saveNodeIdsTemp.removed.indexOf(saveNode.attr('id')) < 0){
								saveNodeIdsTemp.removed.push(saveNode.attr('id'));
								saveNodeIds.push({'removed':saveNode.attr('id')});
								isIDAdded = true;
							}
						}else if(saveNode[0].hasAttribute('data-inserted')){
							if (saveNodeIdsTemp.inserted.indexOf(saveNode.attr('id')) < 0 && pushToArray){
								saveNodeIdsTemp.inserted.push(saveNode.attr('id'));
								saveNodeIds.push({'inserted':saveNode.attr('id')});
								isIDAdded = true;
							}
						}else if(saveNode[0].hasAttribute('data-moved')){
							if (saveNodeIdsTemp.moved.indexOf(saveNode.attr('id')) < 0 && pushToArray){
								saveNodeIdsTemp.moved.push(saveNode.attr('id'));
								saveNodeIds.push({'moved':saveNode.attr('id')});
								isIDAdded = true;
							}
						}else{
							//#45 (5) - Bug fix - Saving all nodes, when formatting is applied to multiple nodes - Prabakaran. A(<EMAIL>)
							$(saveNode).each(function(){
								// For some empty span tags are removed when performing CTRL + X from the HTML, so check whether those ids are present in the HTML
								const saveID = $(this).attr('id');
                                if (saveNodeIdsTemp.edited.indexOf($(this).attr('id')) < 0 && pushToArray && saveID && $(`#${saveID}`).length > 0){
									saveNodeIdsTemp.edited.push($(this).attr('id'));
									saveNodeIds.push({'edited':$(this).attr('id')});
									isIDAdded = true;
                                }
                            })
                            //End of #45 (5)
						}

						if(isIDAdded == true){
							//Before saving the content add id to all elements which doesn't have id in save node.
							$(saveNode).find('*:not([id])').each(function(){
								//Don't add id for content inside the query and if node is not inside the content container then don't add id
								if($(this).closest('.query-div').length > 0 || $(this).closest(kriya.config.containerElm) == 0){
									return;
								}
								// to restrict adding id to child nodes - aravind
								if(!$(this).is(kriya.config.preventAddId)){
									$(this).attr('id',uuid.v4());
								}
							});
						}
					}else{
						console.log(this);
					}
				});
				
				// check if any removed node is sent along with its parent to save, if so remove the removed node from the stack as the parent node will be saved first and the removed node will not be available to save in the xml - jai - 28-06-2019
				var oldContent = $('<content>' + kriyaEditor.settings.startValue + '</content>');
				var intermediateArray = []
				$.each(saveNodeIds, function(s, j){
					if (saveNodeIds[s].removed){
						var removeNodeID = saveNodeIds[s].removed;
						var pushToArray = true;
						$.each(saveNodeIds, function(o, k){
							if (saveNodeIds[o].inserted && oldContent.find('#' + saveNodeIds[o].inserted).find('#'+ removeNodeID).length > 0){
								pushToArray = false;
							}
							else if (saveNodeIds[o].moved && oldContent.find('#' + saveNodeIds[o].moved).find('#'+ removeNodeID).length > 0){
								pushToArray = false;
							}
							else if (saveNodeIds[o].edited && oldContent.find('#' + saveNodeIds[o].edited).find('#'+ removeNodeID).length > 0){
								pushToArray = false;
							}
							else if (saveNodeIds[o].removed && oldContent.find('#' + saveNodeIds[o].removed).find('#'+ removeNodeID).length > 0){
								// to remove removed nodes for which its parent is also sent to remove in savefunction - JAI - 03-07-2019
								pushToArray = false;
							}
						})
						if (pushToArray){
							intermediateArray.push(saveNodeIds[s])
						}
					}else{
						intermediateArray.push(saveNodeIds[s])
					}
				})
				saveNodeIds = intermediateArray;
				
				stackObj.push({
					'saveNodes': saveNodeIds,
					'action' : action
				});
			}

			var sel = rangy.getSelection();
			var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
			var blockID = blockNode.attr('id');
			var caretPosinBlock = getCaretCharacterOffsetWithin(blockNode[0]);
			//if we change any node values in this function before this line that will not saving in undo stack,when we undo after redo that changed values not comming for that changed nodes.
			newValue   = newElement.innerHTML;
			kriyaEditor.htmlContent = newValue;
			stackObj.push({
				container: newElement,
				oldValue: oldValue,
				newValue: newValue,
				caretPos: caretPosinBlock,
				caretBlock: blockID,
				oldCaretPos: oldCaretPos,
				oldCaretId: oldCaretId
			});

			//Adding right panel in the stack
			stackObj.push({
				container: kriyaEditor.settings.changeNode,
				oldValue : kriyaEditor.settings.contextStartValue,
				newValue : kriyaEditor.settings.changeNode.innerHTML
			});

			//add clone element in the stack
			stackObj.push({
				container: kriyaEditor.settings.cloneNode,
				oldValue : kriyaEditor.settings.cloneValue,
				newValue : kriyaEditor.settings.cloneNode.innerHTML
			});

			var EditCommand = Undo.Command.extend({
				constructor: function (undoObj) {
					this.undoObject = undoObj;
				},
				execute: function () {
					this.changed('execute');
				},
				undo: function () {
					this.undoObject.forEach(function(item, index, array){
						if(!item.saveNodes){
							item.container.innerHTML = item.oldValue;
							if(item.oldCaretPos != undefined && item.oldCaretId){
								setCursorPosition($('#' + item.oldCaretId)[0], item.oldCaretPos);
							}
						}
					});
					this.changed('undo');
				},
				redo: function () {
					this.undoObject.forEach(function(item, index, array){
						if(!item.saveNodes){
							item.container.innerHTML = item.newValue;
							if(item.caretPos != undefined && item.caretBlock){
								setCursorPosition($('#' + item.caretBlock)[0], item.caretPos);
							}
						}
					});
					this.changed('redo');
				},
				changed: function (type) {
					for(var x=0; x<this.undoObject.length; x++){
						var item = this.undoObject[x];
						if(item.saveNodes){
							var saveNodes = [];
							//Collect the all save elemets and send it to the save function
							// collecting method is modified as now saveNodes is an array, which have collection objects with action as key and id as value
							var startValue = $('<content>' + kriyaEditor.settings.startValue + '</content>');
							var contextStartValue = $('<content>' + kriyaEditor.settings.contextStartValue + '</content>');
							for(var y=0; y<item.saveNodes.length; y++){
								var nodes;
								for (var key in item.saveNodes[y]) {
									//When undo the chnage inseted node should be removed and removed node should be insert
									var val = item.saveNodes[y][key];
									if(startValue.find('#' + val).length == 0){
										oldValue = contextStartValue
									}else{
										oldValue = startValue
									}
									var idNode =oldValue.find('#' + val)
							    	if(type == "undo" && key == "inserted"){
										idNode.removeAttr('data-inserted');
										idNode.removeAttr('data-moved');
										idNode.attr('data-removed', 'true');
										nodes = idNode[0];
							    	}else if(type == "undo" && key == "removed"){
							    		$('#' + val).attr('data-inserted', 'true');
							    		nodes = $('#' + val)[0];
							    	}else{
								    	if(key == 'removed'){
											//nodes = $('<span id="' + val + '" data-removed="true"></span>')[0];
											idNode.removeAttr('data-inserted');
											idNode.removeAttr('data-moved');
											idNode.attr('data-removed', 'true');
											nodes = idNode[0];
								    	}else if(key == 'moved'){
								    		nodes = $('#' + val).attr('data-moved', 'true');
								    	}else{
								    		nodes = $('#' + val)[0];
								    	}
							    	}
									if (nodes && kriya.config.saveBlockNodes && $(nodes).closest(kriya.config.saveBlockNodes).length > 0 && key !== 'move-to-float-block') {
										nodes = $(nodes).closest(kriya.config.saveBlockNodes);
									}
								};
								if(nodes){
									saveNodes = saveNodes.concat(nodes);
								}
							};
							// added this below rule bcoz while undo is triggered
							// content disappear, to handle this we have reversed save node
							if(type == "undo"){
								saveNodes = $(saveNodes).reverse();
							}
							kriyaEditor.init.save(saveNodes, item.action + '_' + type);
							// Here allow savenodes to process LQC, 
							// Handled cases - click and focusout
							// click - Insert a content and click on someother para 
							// focusout - insert any content, focus out to switch,  click on editor page
							/*if($('div[data-component="lqc_warning_edit"]').length > 0 && ( (action && (action == 'content-click' || action == 'focusout-key')) || (item.action && (item.action == 'content-click' || item.action == 'focusout-key'))) ){
								let cloneSaveNodes = saveNodes;
								let allowProcessLQC = true;
								var sel = rangy.getSelection();
								var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
								var blockID = blockNode.attr('id');
								let saveNodesLen = cloneSaveNodes.length;
								// condition to check current selection node should not same as savenode
								for(let saveNd = 0; saveNd < saveNodesLen; saveNd++){
									let saveNode = cloneSaveNodes[saveNd];
									let saveNodeId = saveNode.getAttribute('id');
									if(blockID == saveNodeId && action == 'content-click' ){ // check here if action is click
										allowProcessLQC = false;
									}
									if(allowProcessLQC){
										processLQC($(saveNode));
									}
								}
							} */
						}
					};
					if(type == "undo" || type == "redo"){
						$('#navContainer #queryDivNode .query-div.disabled').removeClass('disabled');
						citeJS.general.updateStatus();
					}
					//exclude if role is author
					if((kriya.config.content.role != 'author') && (kriya.config.content.role != 'publisher')){
						kriya.config.content.changes = true;
					}
					 //Added By Anuraja to iniate PDF viewer iframe onload event after undo and redo
					 if (location.pathname.match(/(proof_review)/)){
						$('#pdfViewer').on("load", function() {
							callPDFViewer();
						});
					 }
					//to handle undo in list
					//previously after undo of first change, startValue is not updated
					kriyaEditor.settings.startValue = kriyaEditor.settings.contentNode.innerHTML;

					var sel = rangy.getSelection();
					var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
					kriyaEditor.settings.startCaretID = blockNode.attr('id');
					kriyaEditor.settings.startCaretPos = getCaretCharacterOffsetWithin(blockNode[0]);

					kriyaEditor.settings.cloneValue = kriyaEditor.settings.cloneNode.innerHTML;
					kriyaEditor.settings.contextStartValue = kriyaEditor.settings.changeNode.innerHTML;
				}
			});

			stack.execute(new EditCommand(stackObj));
			stack.isDirty = false;

			return;
		},
		save: function(saveNodes, actionType){
			var msg = "Dummy save function is called";
			if (kriyaEditor.init.isEditorSaveEnabled != true){
				msg += ' save not enabled - ' + $('#contentContainer').attr('data-state');
			}else{
				msg += ' save enabled - ' + $('#contentContainer').attr('data-state');
			}
			var errorMsg = "Error: " + msg + " - " + document.readyState + "\nUser:" + kriyaEditor.user.name + "\n" + window.location.href;
			var parameters = {
				'log': errorMsg,
				'type': 'save_failure',
				'action': 'invalid-save' + ' (' + actionType + ')',
				'customer': kriya.config.content.customer,
				'doi': kriya.config.content.doi,
				'project': kriya.config.content.project
			};
			kriya.general.sendAPIRequest('logerrors',parameters,function(res){
				//console.log('Log Saved');
			});
		},
		saveQueueData: function(){
			var msg = "Dummy saveQueueData function is called";
			if (kriyaEditor.init.isEditorSaveEnabled != true){
				msg += ' save not enabled - ' + $('#contentContainer').attr('data-state');
			}else{
				msg += ' save enabled - ' + $('#contentContainer').attr('data-state');
			}
			var errorMsg = "Error: " + msg + " - " + document.readyState + "\nUser:" + kriyaEditor.user.name + "\n" + window.location.href;
			var parameters = {
				'log': errorMsg,
				'type': 'save_failure',
				'action': 'invalid-save',
				'customer': kriya.config.content.customer,
				'doi': kriya.config.content.doi,
				'project': kriya.config.content.project
			};
			kriya.general.sendAPIRequest('logerrors',parameters,function(res){
				//console.log('Log Saved');
			});
		}
	}
	return kriyaEditor;

})(kriyaEditor || {});

function processLQC(mainInputNodes){
	let inputNodeClone = mainInputNodes.clone(true);
	let inputNodes = inputNodeClone[0]
	let param = [];
	param.type = 'languageQualitycheck';
	let allowNodeForLQC = checkNodeAllowForLQC(inputNodes, param);
	if(allowNodeForLQC && inputNodes && inputNodes.textContent != '' && inputNodes.getAttribute("id") != null){
		let clonedXmlNodeData = cleanUpXMLNode(inputNodes, param);
		var nodes = inputNodes;
		var promiseArr = [];
		var leadingSpace = [];
		var callBackVar = [];
		var data ={};
		let orderKey = "";
		var dataStrLen = 0;
		var langQualitycheckConfig = '';
		if(param && param.type && param.type == 'languageQualitycheck'  && languagequalitycheckConfig && typeof(languagequalitycheckConfig) != undefined && languagequalitycheckConfig[kriya.config.content.customer] != undefined){
			langQualitycheckConfig = languagequalitycheckConfig[kriya.config.content.customer];
		}
		else if(param && param.type && param.type == 'languageQualitycheck'  && languagequalitycheckConfig && typeof(languagequalitycheckConfig) != undefined && languagequalitycheckConfig['common'] != undefined){
			langQualitycheckConfig = languagequalitycheckConfig['common'];
		}
		var dataContent = clonedXmlNodeData.textContent.trim().replace(/ | | | | |‍/g, ' ');
		data[inputNodes.getAttribute("id")] = dataContent;
		if(param && param.type && param.type == 'languageQualitycheck' ){
			$.ajax({
				url: '/api/languagequalitycheck',
				data: {
					'data': data,
					'languagequalitycheckConfig': langQualitycheckConfig,
					'customer' : kriya.config.content.customer,
					'fromfrontend': 'true'
				},
				method: "POST",
				xhr: function() {
					let xhr = new window.XMLHttpRequest();
			
					// Add the onprogress event handler to capture partial responses
					xhr.onprogress = function (e) {
					let partialResponse = e.currentTarget.responseText;
					console.log('Partial response received:', partialResponse);
					};
			
					return xhr;
				},
				success: function(res){
					res = JSON.parse(res);
					res = res["restructuredJSON"];
					if(res !== 'undefined' && res.length > 0 && $('#lqcDivNode #lqcUnResolvedDivNode') && $('#lqcDivNode #lqcUnResolvedDivNode').length > 0){
						$('div[id=lqcDivNode] .lqcTab').removeClass('hidden');
						$('div[id=lqcDivNode] .lqcUnResolvedDivNode').removeClass('hidden');
						if(('div[id=lqcDivNode] .lqcDivNodeBlock').length > 0){
							$('div[id=lqcDivNode] .lqcDivNodeBlock').remove()
						}
						eventHandler.lqc.action.createLQCDiv(res, data, langQualitycheckConfig);
						$('.nav-action-icon').removeClass('active');
						$('.lqc-icon').addClass('active');
					}
				},
				error : function(err){
				}
			})
		}
	}
}

function cleanUpXMLNode(inputXmlNode, param){
	// Remove unnecessary nodes (as done in the cheerio code)
	const nodesToRemove = [
		"*[class*='del ']", "*[data-track*='del ']", "*[data-track*='del']", "*[content-type*='del']", "*[data-class*='jrnlDeleted']", "*[data-class='hidden']", "*[hidden-track='track']", "*[data-class='jrnlQueryRef']", "span[class='label']", "span[class='jrnlSupplRef']","span[class='jrnlFootNoteRef']", "span[class='jrnlAppRef']", "sup", "sub"
	];
	nodesToRemove.forEach(xpath => {
		let nodes = $(inputXmlNode).find(xpath);
		if(nodes && nodes.length > 0){
			nodes.remove();
		}
	});

	return inputXmlNode;
}

function checkNodeAllowForLQC(inpuNode, param){
	let allowNodeForLQCheck = true;
	let spellIgnoreClass = '';
	let spellInlineEle = '';
	if(param && param.type && param.type == 'languageQualitycheck' ){
		var spellIgnoreNode = $('[id="lqcCheck_edit"][data-ignore-class-list][data-class-elements]');
	}
	else{
		var spellIgnoreNode = $('[id="spellCheck_edit"][data-ignore-class][data-class-elements]');
	}
	if(spellIgnoreNode.length>0){
		spellIgnoreClass = $(spellIgnoreNode)[0].getAttributeNode('data-ignore-class-list').value;
		spellInlineEle = $(spellIgnoreNode)[0].getAttributeNode('data-class-elements').value;
	}
	spellIgnoreClass = "|jrnlMsNumber|jrnlKwdGroup|del|jrnlAuthors|jrnlCitation|jrnlHistory|jrnlPermission|jrnlCopyrightStmt|jrnlCorrAff|jrnlDOI|jrnlReDate|jrnlRevDate|jrnlAcDate|jrnlPubDate|jrnlKwdGroup|jrnlKeywordPara|jrnlAff|jrnlHeaderInfo|jrnlLicense|jrnlRefText|label|jrnlFNPara|jrnlDeleted|jrnlTblFootText|jrnlTblFoot|SUP|SUB|";
	let parentTagClassNames = [];
    let attributes = [];
    if (inpuNode && inpuNode.attributes && inpuNode.attributes.length > 0){
        attributes = inpuNode.attributes;
    }
    for (let attr of attributes) {
        if (attr.name === 'content-type' || attr.name === 'data-tag-type' || attr.name === 'data-class' || attr.name === 'class' || attr.name === 'ref-type' || attr.name === 'data-track') {
            if(attr.value.split(' ').length > 1){
                let storeAttrs = attr.value.split(' ');
                for(let attr=0; attr < storeAttrs.length; attr++){
                    parentTagClassNames.push(storeAttrs[attr]);
                }
            }
            else{
                parentTagClassNames.push(attr.value);
            }
        }
    }
    if (inpuNode.nodeName) {
        parentTagClassNames.push(inpuNode.nodeName);
    }
    // Check if the node should be ignored
    if (parentTagClassNames.length > 0) {
        parentTagClassNames.forEach((className) => {
			let regex = new RegExp(`\\|${className.trim()}\\|`);
			let isMatch = regex.test(spellIgnoreClass);
            if (isMatch) {
                allowNodeForLQCheck = false;
            }
        });
    }
	return allowNodeForLQCheck;
}

function handleChanges(summary){

	if(kriyaEditor.settings){
		kriyaEditor.settings.summaryData = summary[0];
	}

	if (summary[0].removed.length > 0){
		setTimeout(function(){
			postal.publish({
				channel: 'menu',
				topic: 'observer' + '.' + 'click',
				data: {
					message: {
						'funcToCall': 'nodeChange',
						'param': {
							'node': summary[0].removed,
							'type': 'removed'
						}
					},
					event: 'click',
					target: ''
				}
			});
		},10)
	}

	//Added nodes used for saving when press enter
	//Used in keyup event
	if (summary[0].added.length > 0){

		//When enter key pressed after typing in content track change node was duplicate in new para
		//loop the added element if ele is a track node and text is null and more than one same cid is in content
		//then remove ele in added array and unwrap ele from  content
		for(i=0;i<summary[0].added.length;i++){
			var addedEle = summary[0].added[i];
			if(($(addedEle).hasClass('ins') || $(addedEle).hasClass('del')) && $(addedEle).text() == "" && addedEle.hasAttribute('data-cid')){
				var cid = $(addedEle).attr('data-cid');
				var eleClass = $(addedEle).attr('class').split(' ')[0];
				if($(kriya.config.containerElm + ' .'+eleClass+'[data-cid="' + cid + '"]').length > 1){
					$(addedEle).contents().unwrap();
					summary[0].added.splice(i, 1);
				}
			}
		}

		setTimeout(function(){
			postal.publish({
				channel: 'menu',
				topic: 'observer' + '.' + 'click',
				data: {
					message: {
						'funcToCall': 'nodeChange',
						'param': {
							'node': summary[0].added,
							'type': 'added'
						}
					},
					event: 'click',
					target: ''
				}
			});
		},10)
	}
}

function setupWatch(){
	var observer = new MutationSummary({
	  callback: handleChanges, // required
	  rootNode: document.getElementById('contentDivNode'), // optional, defaults to window.document
	  observeOwnChanges: false,// optional, defaults to false
	  oldPreviousSibling: false,// optional, defaults to false
		queries: [{
			all: true
		}]
	});
}
 /**
  * Added by ANuraja to display a split view like word on right panel
  */
 function updateContentViewNavigationPanel() {
 	var d = $('#contentContainer').clone()[0];
 	$('#contentviewDivNode #previewArea').html('');
 	$('#contentviewDivNode #previewArea').append(d);
	 $('#contentviewDivNode #previewArea .removeNode').remove();
	 $('#contentviewDivNode #previewArea #clonedContent').remove();	 //clonedContent in right panel making problems in find and replace. So removed that node - Jagan
	 $('#contentviewDivNode #previewArea .class-highlight').remove();
	 $('#contentviewDivNode #previewArea .nano').removeClass('nano has-scrollbar');
	 $('#contentviewDivNode #previewArea .nano-content').removeClass('nano-content');
	 $('#contentviewDivNode #previewArea .content-scrollbar').remove();
 	$('#contentviewDivNode #previewArea .contentBtn').remove();
	 $('#contentviewDivNode #previewArea .btn').remove();
 	$('#contentviewDivNode #previewArea #contentContainer').find('*').each(function (index, element) {
 		var attrs = this.attributes;
 		var toRemove = [];
 		var element = $(this);
 		for (attr in attrs) {
 			if (typeof attrs[attr] === 'object' &&
 				typeof attrs[attr].name === 'string' &&
 				(/^(data-|removeNode|contenteditable|id|article-key|article-)/).test(attrs[attr].name)) {
 				toRemove.push(attrs[attr].name);
 			}
 		}
 		for (var i = 0; i < toRemove.length; i++) {
 			element.removeAttr(toRemove[i]);
		 }
	 });
	 $('#contentviewDivNode #previewArea #contentContainer').each(function (index, element) {
		var attrs = this.attributes;
		var toRemove = [];
		var element = $(this);
		for (attr in attrs) {
			if (typeof attrs[attr] === 'object' &&
				typeof attrs[attr].name === 'string' &&
				(/^(data-|removeNode|contenteditable|id|article-key|article-)(?!role)/).test(attrs[attr].name)) {
				toRemove.push(attrs[attr].name);
			}
		}
 		for (var i = 0; i < toRemove.length; i++) {
 			element.removeAttr(toRemove[i]);
 		}
 	});
	 var contentviewDivNode = $('#contentviewDivNode');
  if (($(contentviewDivNode).length > 0) && $(contentviewDivNode).is(':visible')) {
   		$(contentviewDivNode).css('height', (window.innerHeight - $(contentviewDivNode).offset().top ) + 'px');
   		$(contentviewDivNode).parent('.nano').css('height', (window.innerHeight - $(contentviewDivNode).offset().top ) + 'px');
   		$(contentviewDivNode).parent('.nano').nanoScroller({
   			alwaysVisible: true,
   			sliderMinHeight: 5,
   			preventPageScrolling: true
   		});
   		$(contentviewDivNode).parent().find('.nano-pane').addClass('content-scrollbar');
 	}
 }

 function callPDFViewer() {
 	if ($("iframe[id='pdfViewer'], iframe[id='pdfViewers']").length > 0) {
		var proofAttrDetails = {
			'column': {
				'user': 'data-column-start',
				'system': 'data-system-column-start'
			},
			'position': {
				'user': 'data-position',
				'system': 'data-system-position'
			},
			'span': {
				'user': 'data-column-span',
				'system': 'data-system-column-span'
			},
			'orientation': {
				'user': 'data-orientation',
				'system': 'data-system-orientation'
			}
		}
		//console.log("ready!");
 		$("iframe[id='pdfViewer'], iframe[id='pdfViewers']").contents().find("#viewerContainer").on("pagerendered", "#viewer", function (event) {
 			//console.log('page rendered');
 			var allowInternalLinks = true;
 			var hyperlinks = $(this).find('a');
 			for (var i = 0; i < hyperlinks.length; i++) {
 				if (!allowInternalLinks || hyperlinks[i].className != 'internalLink') {
 					hyperlinks[i].onclick = function (e) {
 						$(e.target).attr('target', '_blank');
 					}
				} else {
 					var hrefid = $(hyperlinks[i]).attr('href').replace(/.*%3A(.*)%3A.*/, '$1');
 					$(hyperlinks[i]).attr('id', hrefid);
 					var page = parseInt($(event.target).closest('div[data-page-number]').attr('data-page-number')); //doing '-1' because index starts at '0'
 					$(hyperlinks[i]).closest('.linkAnnotation').wrap('<span id="' + hrefid + '" page="' + page + '" class="citation"></span>')
 					$(hyperlinks[i]).closest('.linkAnnotation').remove();
 					//$(hyperlinks[i]).closest('.linkAnnotation').addClass('citaion').removeClass('linkAnnotation');
 					//$(hyperlinks[i]).attr('href','');
				}
 			};
 		});
 		/**
 		 * hook onto the textlayerrendered event to add divs (for visual indication of editable area) using the coordinates obtained
 		 */
 		$("iframe[id='pdfViewer']").contents().find("#viewerContainer").on("textlayerrendered", "#viewer", function (event) {
			//Restore scroll position when load iframe - jagan
			var iframePDF = $('#pdfViewer')[0].contentDocument;
			if(kriya.config.iframeScrollLeft || kriya.config.iframeScrollLeft == 0){
				$(iframePDF).find('#viewerContainer').scrollLeft(kriya.config.iframeScrollLeft);
				kriya.config.iframeScrollLeft = undefined;
			}

			if(kriya.config.iframeScrollTop || kriya.config.iframeScrollTop == 0){
				$(iframePDF).find('#viewerContainer').scrollTop(kriya.config.iframeScrollTop);
				kriya.config.iframeScrollTop = undefined;
			}						

			var annoTation = $('<div class="markerLayer" style="' + $(event.target).attr('style') + '"></div>');
 			var page = parseInt($(event.target).closest('div[data-page-number]').attr('data-page-number') - 1); //doing '-1' because index starts at '0'
 			var ignoreProofControls = /author|editor/;
 			// get the scale factor of the current view port
 			// this is important as the actual page width and the rendered page width differs and the scale factor helps restore the difference
 			var viewportScale = window.frames["pdfIFrame"].PDFViewerApplication.pdfViewer._pages[page].viewport.scale;
 			var currPageObj = coordinate[page];
 			if (currPageObj) {
 				Object.keys(currPageObj).forEach(function (id) {
 					var currParaObj = currPageObj[id];
					// ignore jrnlAuthGroup class tocreate (div)
					var ignoreClass = ".jrnlAuthorsGroup"; // add classes like .class1, class2
					var ignoreTag = false;
 					id = id.replace(/__[0-9]+$/, '');
					
					var currentProofDetail = false;
					if (typeof(proofingfloatdetails) != 'undefined'){
						if (proofingfloatdetails[page] && proofingfloatdetails[page][id]){
							currentProofDetail = proofingfloatdetails[page][id];
						}else if (proofingfloatdetails[id]){
							currentProofDetail = proofingfloatdetails[id];
						}else if (proofingfloatdetails[id.replace('BLK_', '')]){
							currentProofDetail = proofingfloatdetails[id.replace('BLK_', '')];
						}
					}

					if ($('#contentDivNode #' + id).length > 0 && $('#contentDivNode #' + id).is(ignoreClass)) {
						ignoreTag = true;
					} else if ($('#contentDivNode [data-id=' + id + ']').length > 0 && $('#contentDivNode [data-id=' + id + ']').is(ignoreClass)) {
						ignoreTag = true;
					}
					// check if we have any proof controls for the paragraph and add class='proofControls' to show a border on the visual indicator (div)
					var proofControlAtt = '[data-word-spacing], [data-vj], [data-top-gap], .forceColBrk, .forceJustify';
 					proofControlIgnore = '.jrnlAuthor, .jrnlAuthorGroup, .jrnlArtTitle, .jrnlAff, .jrnlQueryRef'
 					var proofContTag = ' none';
 					if ($('#contentDivNode [data-id=' + id + ']').length > 0 && $('#contentDivNode [data-id=' + id + ']').is(proofControlIgnore)) {
 						proofContTag = '';
 					} else if($('#contentDivNode #' + id).length > 0 && $('#contentDivNode #' + id).is(proofControlIgnore)){
						proofContTag = '';
					 }else if ($('#contentDivNode #' + id).length > 0 && $('#contentDivNode #' + id).is(proofControlAtt)) {
 						proofContTag = ' updated';
					} else if ($('#contentDivNode [data-id=' + id + ']').length > 0 && $('#contentDivNode [data-id=' + id + ']').is(proofControlAtt)) {
 						proofContTag = ' updated';
					}
 					if (kriya.config.content.role.match(ignoreProofControls)) {
 						proofContTag = '';
 					}
					var styleString = "";
					styleString += "top: " + (currParaObj[0] * viewportScale) + "px !important; ";
 					var proofStyleString = styleString + "height: " + ((currParaObj[2] - currParaObj[0]) * viewportScale) + "px !important; width: " + (3 * viewportScale) + "px !important; left: " + ((currParaObj[1] * viewportScale) - 5) + "px !important; ";

 					var editBlocks = /(BLK_F|BLK_T)(\d+)/;
 					var proofcontrolsBlock = /^(BLK_F|BLK_T|R)(\d+)/;
 					if (id.match(editBlocks) && !kriya.config.content.role.match(ignoreProofControls)) {
 						proofContTag = '';
						var editStyleString = styleString + "height: 20px; width: 20px !important; left: " + ((currParaObj[1] * viewportScale) - (25 * viewportScale)) + "px !important; transform: scale(" + viewportScale + ")";
						//commenting below line to hide proofcontols from side by side component 
						// enabling for purpose of T&F demo - Jai
						if ($("#floatProofControls").length > 0){
							$('<div page="' + page + '" data-id=' + id + ' class="editMarker fa fa-gear" style="' + editStyleString + '">&nbsp;</div>').appendTo(annoTation);
						}
						// adding data from the proofing object back to the float
						if (currentProofDetail && $('#contentDivNode [data-id=' + id + ']').length > 0){
							Object.keys(proofAttrDetails).forEach(function (objKey) {
								if (typeof(currentProofDetail[objKey]) != 'undefined'){
									if (/^(column|span)$/.test(objKey)){
										currentProofDetail[objKey] = parseInt(currentProofDetail[objKey]) + 1;
									}
									if ($('#contentDivNode [data-id=' + id + ']')[0].hasAttribute(proofAttrDetails[objKey]['user'])){
										if ($('#contentDivNode [data-id=' + id + ']').attr(proofAttrDetails[objKey]['user']) != currentProofDetail[objKey]){
											$('#contentDivNode [data-id=' + id + ']').attr(proofAttrDetails[objKey]['system'], currentProofDetail[objKey]);
										}
									}else{
										$('#contentDivNode [data-id=' + id + ']').attr(proofAttrDetails[objKey]['system'], currentProofDetail[objKey]);
									}
								}
							})
						}
					}
 					if (!id.match(proofcontrolsBlock) && !kriya.config.content.role.match(ignoreProofControls)) {
						//commenting below line to hide proofcontols from side by side component 
						// enabling for purpose of T&F demo - Jai
						if ($("#pdfProofControls").length > 0){
							$('<div data-id=' + id + ' class="proofControls' + proofContTag + '" style="' + proofStyleString + '">&nbsp;</div>').appendTo(annoTation);
						}
 					}
					if (!ignoreTag) {
 						styleString += "height: " + ((currParaObj[2] - currParaObj[0]) * viewportScale) + "px !important; ";
 						styleString += "width: " + ((currParaObj[3] - currParaObj[1]) * viewportScale) + "px !important; ";
						styleString += "left: " + (currParaObj[1] * viewportScale) + "px !important; ";
						// check if we have any proof controls for the paragraph and add class='proofControls' to show a border on the visual indicator (div)
						$('<div data-id=' + id + ' class="pdfMarker" style="' + styleString + '">&nbsp;</div>').appendTo(annoTation);
					}
 				});
				$(annoTation).appendTo($(event.target).closest('.page'))
 			}
 		});
		$("iframe[id='pdfViewer']").contents().find("#viewerContainer").on("click", "#viewer > .page > .markerLayer > .pdfMarker", function (event) {
			$(event.target).closest('#viewerContainer').find('.pdfMarker').removeClass('active');
			$(event.target).addClass('active');
			var divID = $(event.target).attr('data-id');
			if ($('#contentDivNode #' + divID).length > 0) {
				$('#contentDivNode #' + divID)[0].scrollIntoView();
				$('#contentDivNode #' + divID).trigger("click");
			} else if ($('#contentDivNode [data-id=' + divID + ']').length > 0) {
 				$('#contentDivNode [data-id=' + divID + ']')[0].scrollIntoView();
				$('#contentDivNode [data-id=' + divID + ']').trigger("click");
			}
			kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;
			//Display edit option for Reference
 			if (divID.match(/R(\d+)/)) {
 				$('div[data-component="jrnlRefText"] > span:contains("Edit")').trigger('click')
			}
 		});
		$("iframe[id='pdfViewer']").contents().find("#viewerContainer").on("click", "#viewer > .page > .markerLayer > .proofControls", function (event) {
			var divID = $(event.target).attr('data-id');
 			var node;
			var wordSpaceArray = [0.23, 0.28, 0, 0.38, 0.43];
			if ($('#contentDivNode #' + divID).length > 0) {
				$('#contentDivNode #' + divID)[0].scrollIntoView();
				$('#contentDivNode #' + divID).trigger("click");
 				node = $('#contentDivNode #' + divID)[0];
 				//$('#contentDivNode #' + divID).dblclick()
 				//$('[data-name="proofControlLink"]').trigger("click");
			} else if ($('#contentDivNode [data-id=' + divID + ']').length > 0) {
 				$('#contentDivNode [data-id=' + divID + ']')[0].scrollIntoView();
				$('#contentDivNode [data-id=' + divID + ']').trigger("click");
 				//$('#contentDivNode [data-id=' + divID + ']').dblclick()
 				//$('[data-name="proofControlLink"]').trigger("click");
 				node = $('#contentDivNode [data-id=' + divID + ']');
			 }
			$(node).each(function () {
				$('#pdfProofControls #headerSpace').addClass('hidden');
				var proofHeaderSpace = '.jrnlHead1, .jrnlHead2, .jrnlHead3, .jrnlHead4, .jrnlHead5, .jrnlHead6, .jrnlRefHead';
				if($(node).is(proofHeaderSpace)){
					$('#pdfProofControls #headerSpace').removeClass('hidden');
				}
				var defaultWordSpacing = true
				$('#pdfProofControls').attr('data-id', divID);
 				$('#pdfProofControls input').prop('checked', false);
				$.each(this.attributes, function () {
					var proofControlAtt = '/data-word-spacing|data-vj|data-top-gap|forceColBrk|forceJustify/';
					if (this.specified) {
						var value = this.value.replace(/(w)$/,'')
						if(this.name.match(/data-word-spacing|data-vj|data-top-gap|forceColBrk|forceJustify/)){
							if($('#pdfProofControls input[value="' + value + '"][class=' + this.name + ']')>0){
								$('#pdfProofControls input[value="' + value + '"][class=' + this.name + ']').prop('checked', true)
							}else{
								var count = closestNumber (wordSpaceArray, value)
								defaultWordSpacing = false;
								$('#pdfProofControls input[value="' + count + '"][class=' + this.name + ']').prop('checked', true)
							}
							$('#pdfProofControls').attr(this.name, this.value);
						}
					}
				});
				//select normal word space by default if not set
				if (defaultWordSpacing){
					$('#pdfProofControls input[value="0"][class="data-word-spacing"]').prop('checked', true);
				}
			});
			document.querySelector('[id="pdfProofControls"]').classList.remove("hidden");
			TomloprodModal.openModal('pdfProofControls');
			kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;
 			//if (divID.match(/BLK/)) {}
 		});
 		$("iframe[id='pdfViewer']").contents().find("#viewerContainer").on("click", "#viewer > .page > .markerLayer > .editMarker", function (event) {
 			var divID = $(event.target).attr('data-id');
 			$('#floatProofControls').attr('data-id', divID);
 			$('#floatProofControls input[type="radio"]').prop('checked', false);
 			$('#floatProofControls input').removeAttr('disabled');
 			$('#floatProofControls input[type="checkbox"]').prop('checked', false);
 			$('#floatProofControls input[type="number"]').val('')
 			$('[id="floatProofControls"] #imageSpace, [id="floatProofControls"] #imageSize ').addClass('hidden');
 			var node;
 			if ($('#contentDivNode #' + divID).length > 0) {
 				$('#contentDivNode #' + divID)[0].scrollIntoView();
 				//$('#contentDivNode #' + divID).trigger("click");
 				node = $('#contentDivNode #' + divID)[0];
 				//$('#contentDivNode #' + divID).dblclick()
 				//$('[data-name="proofControlLink"]').trigger("click");
 			} else if ($('#contentDivNode [data-id=' + divID + ']').length > 0) {
 				$('#contentDivNode [data-id=' + divID + ']')[0].scrollIntoView();
 				//$('#contentDivNode [data-id=' + divID + ']').trigger("click");
 				//$('#contentDivNode [data-id=' + divID + ']').dblclick()
 				//$('[data-name="proofControlLink"]').trigger("click");
 				node = $('#contentDivNode [data-id=' + divID + ']');
			}
 			if (divID.match(/BLK_F(\d+)/)) {
 				$('[id="floatProofControls"] #floatName').text('Figure');
 				$('[id="floatProofControls"] #imageSpace').removeClass('hidden');
 				$(node).each(function () {
 					$.each(this.attributes, function () {
 						if (this.specified) {
 							//console.log(this.name, this.value);
						}
	   				});
 				});
			}
 			if (divID.match(/BLK_T(\d+)/)) {
 				$('[id="floatProofControls"] #floatName').text('Table');
 			}
 			//display if already proofcontrols applied
 			$(node).each(function () {
 				var cpage = parseInt($("iframe[id='pdfViewer']").contents().find('.page span.citation[id="' + divID + '"]').attr('page')) - 1;
 				if (cpage != '' && cpage != undefined && cpage != 'NaN') {
 					this.value = cpage + parseInt($(event.target).parents().closest('.page').attr('data-page-number'));
 					$('#floatProofControls #prevCitation').text(cpage);
 					$('#floatProofControls #cpage').attr('value', parseInt($(event.target).parents().closest('.page').attr('data-page-number'))).val(cpage);
 					$('#floatProofControls #npage').attr('value', parseInt($(event.target).parents().closest('.page').attr('data-page-number')) + 1).val(cpage + 1);
 					$('#floatProofControls #ppage').attr('value', parseInt($(event.target).parents().closest('.page').attr('data-page-number')) - 1).val(cpage - 1);
 				}
				$('#floatProofControls #spage').attr('value', parseInt($(node).attr('data-proof-page'))).val(parseInt($(node).attr('data-proof-page')));
				$('#floatProofControls #spage').attr('disabled', 'true');
				var placeFloatsAtt = /data-page-num|data-top|data-top-gap|data-bot-gap|data-orientation|data-bot-gap|data-float-position|data-position|data-float-percent|data-column-start|data-column-span|data-float-placement/;
				$.each(this.attributes, function () {
 					if (this.specified) {
 						if (this.name.match(placeFloatsAtt)) {
 							this.value = this.value.replace(/(pt|w)$/g, '')
							 var thisValue = this.value;
							if (/^(data-column-start|data-column-span)$/.test(this.name)){
								thisValue = parseInt(thisValue) + 1;
							}
 							$('#floatProofControls #prevCitation').text('');
 							$('#floatProofControls input[type="radio"][value="' + thisValue + '"][class=' + this.name + ']').prop('checked', true)
 							$('#floatProofControls input[type="number"][class=' + this.name + ']').val(thisValue)
 							$('#floatProofControls').attr(this.name, thisValue);
 							//$('#floatProofControls input.data-page-num[value="1"]').attr('disabled', '');
 							//$('#floatProofControls input.data-page-num[value="0"]').attr('disabled', '');
 							//$('#floatProofControls input.data-page-num[value="2"]').attr('disabled', '');
 						}
 					}
 				});
				Object.keys(proofAttrDetails).forEach(function (objKey) {
					if (!$(node)[0].hasAttribute(proofAttrDetails[objKey]['user']) && $(node)[0].hasAttribute(proofAttrDetails[objKey]['system'])){
						var systemValue = $(node)[0].getAttribute(proofAttrDetails[objKey]['system']).replace(/(pt|w)$/g, '')
						$('#floatProofControls #prevCitation').text('');
						$('#floatProofControls input[type="radio"][value="' + systemValue + '"][class=' + proofAttrDetails[objKey]['user'] + ']').prop('checked', true)
						$('#floatProofControls input[type="number"][class=' + proofAttrDetails[objKey]['user'] + ']').val(systemValue)
						$('#floatProofControls').attr(proofAttrDetails[objKey]['user'], systemValue);
					}
				}) 
 			});

 			document.querySelector('[id="floatProofControls"]').classList.remove("hidden");
			 TomloprodModal.openModal('floatProofControls');
			 kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;
 			/* document.querySelector('[id="floatProofControls"]').classList.remove("hidden");
 				TomloprodModal.openModal('floatProofControls');*/
 		});
 		$('#floatProofControls input[type="radio"]').on('click', function (e) {
 			var className = $(this).attr('class')
 			var selected = $(this).val();
 			$('input[class="' + className + '"]').prop('checked', false);
 			$('input[class="' + className + '"][value="' + selected + '"]').prop('checked', true);
 		})
 		$('#floatProofControls input[type="checkbox"]').on('click', function (e) {
 			var className = $(this).attr('class')
 			var selected = $(this).val();
 			var checked = $('input[class="' + className + '"][value="' + selected + '"]:checked').length
 			if ($(this).attr('display')) {
 				if (checked > 0) {
 					$('#floatProofControls [id="' + $(this).attr('display') + '"]').removeClass('hidden');
 				} else {
 					$('#floatProofControls [id="' + $(this).attr('display') + '"]').addClass('hidden');
 				}
 			}
 			$('input[class="' + className + '"]').prop('checked', false);
 			if (checked > 0) {
 				$('input[class="' + className + '"][value="' + selected + '"]').prop('checked', true);
 			}
 		})
 		$('input.data-word-spacing').on('click', function (e) {
 			var selected = $(this).val();
 			$('input.data-word-spacing').prop('checked', false);
 			$('input.data-word-spacing[value="' + selected + '"]').prop('checked', true);

 		});
 		$('input.data-top-gap').on('click', function (e) {
 			var selected = $(this).val();
 			$('input.data-top-gap').prop('checked', false);
 			$('input.data-top-gap[value="' + selected + '"]').prop('checked', true);

 		})
 	}
 }
 function callMainPDFViewer() {

 	/**
 	 * For main pdf
 	 */
 	/**
 	 * hook onto the textlayerrendered event to add divs (for visual indication of editable area) using the coordinates obtained
 	 */
 	if ($("iframe[id='pdfViewers']").length > 0) {
 		$("iframe[id='pdfViewers']").contents().find("#viewerContainer").on("textlayerrendered", "#viewer", function (event) {
 			var annoTation = $('<div class="markerLayer" style="' + $(event.target).attr('style') + '"></div>');
 			var page = parseInt($(event.target).closest('div[data-page-number]').attr('data-page-number') - 1); //doing '-1' because index starts at '0'
 			// get the scale factor of the current view port
 			// this is important as the actual page width and the rendered page width differs and the scale factor helps restore the difference
 			var viewportScale = window.frames["pdfIFrames"].PDFViewerApplication.pdfViewer._pages[page].viewport.scale;
 			var currPageObj = coordinate[page];
 			if (currPageObj) {
 				Object.keys(currPageObj).forEach(function (id) {
 					var currParaObj = currPageObj[id];
 					// apply jrnlQueryRef class tocreate (div)
 					var applyClass = ".jrnlQueryRef"; // add classes like .class1, class2
 					var applyTag = false;
 					var currentNodeClass = "";
 					id = id.replace(/__[0-9]+$/, '');
 					if ($('#contentDivNode #' + id).length > 0 && $('#contentDivNode #' + id).is(applyClass)) {
 						applyTag = true;
 						currentNodeClass = $('#contentDivNode #' + id).attr('class');
 					} else if ($('#contentDivNode [data-id=' + id + ']').length > 0 && $('#contentDivNode [data-id=' + id + ']').is(applyClass)) {
 						applyTag = true;
 						currentNodeClass = $('#contentDivNode [data-id=' + id + ']').attr('class');
 					}

 					// check if we have any proof controls for the paragraph and add class='proofControls' to show a border on the visual indicator (div)
 					var proofControlAtt = '[data-word-spacing], [data-vj], [data-top-gap], .forceColBrk, .forceJustify';
 					proofControlIgnore = '.jrnlAuthor, .jrnlAuthorGroup, .jrnlArtTitle, .jrnlAff'
 					var hoverString = '';
 					var styleString = "";
 					var extraClass = "";
 					var extraContent = "&nbsp;";
 					styleString += "top: " + (currParaObj[0] * viewportScale) + "px !important; ";
 					if (applyTag) {
 						if (currentNodeClass.match(/(jrnlQueryRef)/)) {
 							hoverString = $('#queryDivNode #' + $('#contentContainer #' + id).attr('data-rid')).find('.query-content').html();
 							extraClass = ' tooltip';
 							extraContent = '<span class="tooltiptext">' + hoverString + '</span>';
 						}
 						styleString += "height: " + ((currParaObj[2] - currParaObj[0]) * viewportScale) + "px !important; ";
 						styleString += "width: " + ((currParaObj[3] - currParaObj[1]) * viewportScale) + "px !important; ";
 						styleString += "left: " + (currParaObj[1] * viewportScale) + "px !important; ";
 						// check if we have any proof controls for the paragraph and add class='proofControls' to show a border on the visual indicator (div)

 						$('<div data-id=' + id + ' class="pdfMarker' + extraClass + '" style="' + styleString + '">' + extraContent + '</div>').appendTo(annoTation);
 					}
 				});
 				$(annoTation).appendTo($(event.target).closest('.page'))
 			}
 		});
 		$("iframe[id='pdfViewers']").contents().find("#viewerContainer").on("click", "#viewer > .page > .markerLayer > .pdfMarker", function (event) {
 			eventHandler.welcome.begin.startEditing()
 			var divID = $(event.target).attr('data-id');
 			if ($('#contentDivNode #' + divID).length > 0) {
 				$('#contentDivNode #' + divID)[0].scrollIntoView();
 				$('#contentDivNode #' + divID).trigger("click");
 			} else if ($('#contentDivNode [data-id=' + divID + ']').length > 0) {
 				$('#contentDivNode [data-id=' + divID + ']')[0].scrollIntoView();
 				$('#contentDivNode [data-id=' + divID + ']').trigger("click");
			}
			kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;
 		});
 	}
 }
function callInitializeEvents(){
	eventHandler.publishers.add();
	eventHandler.subscribers.add();
	eventHandler.components.general.healthCheck()
	//#509 - added wicket good xpath initializer - Rajasekar T(<EMAIL>)
	wgxpath.install();
	if ($('#contentContainer').attr('data-state') != 'read-only' && $('.time-notice').length > 0){
		timeTicker = new easytimer.Timer();
		timeTicker.start();
		timeTicker.addEventListener('secondsUpdated', function (e) {
			$('.time-notice').html(timeTicker.getTimeValues().toString());
		});
	}
	setTimeout(function(){
		kriyaEditor.init.trackArticle();
	}, 120000);
	//#509
	$.each(kriya.config.preventTyping.split(","), function (i, val) {
		$(val).attr('data-editable', "false")
	})
	if (($(contentDivNode).length > 0) && $(contentDivNode).is(':visible')) {
		$(contentDivNode).css('height', (window.innerHeight - $(contentDivNode).offset().top)+ 'px');
		$(contentDivNode).parent('.nano').css('height', (window.innerHeight - $(contentDivNode).offset().top)+ 'px');
		$(contentDivNode).parent('.nano').nanoScroller({
			alwaysVisible: true,
			sliderMinHeight: 5,
			preventPageScrolling: true
		});
		$(contentDivNode).parent().find('.nano-pane').addClass('content-scrollbar');
		//$(contentDivNode).width('90%');
	}
	$('#navContainer').css('height', ($(window).height() - $('#navContainer').offset().top) + 'px')
	$('#navContainer .navDivContent > div').css('height', $(window).height() - $('#navContainer #infoDivContent').offset().top - $('.helpIcon').height());
	
	if ($('#indexPanelContent .jstree-container-ul').length > 0){
		$('#indexPanelContent .jstree-container-ul li').removeAttr('class');
		tree = $("#indexPanelContent").jstree({
			core: {
				check_callback: true
			},
			plugins: ["dnd"]
		});
		tree.jstree("deselect_all").jstree('open_all');
		$('#indexPanelContent > ul').attr('id', 'indexMainEntry');
	}

	if ($('#conIndexPanelContent .jstree-container-ul').length > 0){
		$('#conIndexPanelContent .jstree-container-ul li').removeAttr('class');
		conTree = $("#conIndexPanelContent").jstree({
			core: {
				check_callback: true
			},
			plugins: ["dnd"]
		});
		conTree.jstree("deselect_all").jstree('open_all');
		$('#conIndexPanelContent > ul').attr('id', 'conIndexMainEntry');
	}

	//Initialize table row and index
	$(kriya.config.containerElm).find('.jrnlInlineTable, .jrnlTable').each(function(){
		setTableIndex($(this));
	});
	
	//Update alternate text character length - jagan
	$(kriya.config.containerElm).find('.jrnlFigMeta[data-content-type="Alt Text"]').each(function(){
		kriyaEditor.init.updateTextLength(this);
	});
	
	// remove the probe validation if data-skip-probevalidation attr prensent in content added by vijayakumar on 07-11-2019 
	if($(kriya.config.containerElm).find('*[data-skip-probevalidation]').length > 0){
		$('[data-type="popUp"][data-component="PROBE_VALIDATION_edit"]').remove()
	}

	window.addEventListener("resize", function(){
		//Update the height for contentdivenode and clonedContent division - jagan
		var contentHeight = window.innerHeight - $(contentDivNode).offset().top;
		$(contentDivNode).css('height', contentHeight + 'px');
		$(contentDivNode).parent('.nano').css('height', contentHeight + 'px');
		$('#clonedContent').css('height', contentHeight + 'px'); 
		$('#navContainer').css('height', ($(window).height() - $('#navContainer').offset().top) + 'px');		
		$('#navContainer .navDivContent > div').css('height', $(window).height() - $('#navContainer #infoDivContent').offset().top - $('.helpIcon').height());
		//it will refresh contentContainer scrollbar size when we resize the window.#2539
		$(contentDivNode).parent(".nano").nanoScroller();		
	});
	eventHandler.query.action.addBadge();
	$('.uncited-object-btn').each(function(){
		if ($(this).closest('div').find('[data-uncited="true"]').length > 0){
			$(this).append('<span class="notify-badge">'+ $(this).closest('div').find('[data-uncited="true"]').length + '</span>');
		}
	})
	$('#contentDivNode').after('<span class="class-highlight hidden" style="position: absolute;bottom: -1px;display: inline-block;height: 20px;/* width: 100px; */right: 14px;background: #eee;border: 1px solid #ddd;font-size: 12px;line-height: 110%;padding:5px;text-align: right;color:#424242;z-index: 999;opacity: 0.7;user-select: none;border-radius: 2px;"></span>');
	$('[data-type="popUp"]').find('input,textarea').attr("autocomplete","off").attr("autocorrect", "off").attr("autocapitalize", "off").attr("spellcheck", "false");
	$('[data-tooltip]').tooltip({
		delay: 50
	});

	//Initialize the content buttons
	if ($('#contentContainer').attr('data-state') != 'read-only'){
		var contentBtns = $('#compDivContent [data-component="contentButtons"] [data-xpath]');
		contentBtns.each(function(){
			var xpath = $(this).attr('data-xpath');
			var insertAfterXpath = $(this).attr('data-insert-after-xpath');		
			var dataxpath = $(this).attr('data-condition');
			var maxVal = $(this).attr('data-maximum');
			var btnContainer = kriya.xpath(xpath);
			var clonedNode = $(this).clone(true);
			var btnContainerCond = "";
			var availdata = 0;
			if($(btnContainer).length > 0){
				if(dataxpath!=undefined){
					btnContainerCond = kriya.xpath(dataxpath);
					if(maxVal!=undefined){
						if($(btnContainerCond).length>=maxVal){
							$(clonedNode).addClass("hidden");
						}
					} else if ($(btnContainerCond).length > 0) {
						$(btnContainerCond).each(function(){
							if($(this).attr('data-track')==undefined || $(this).attr('data-track')!="del"){
								availdata=1;
							}
						})
						if(availdata==1){
							$(clonedNode).addClass("hidden");
						}
					}
				}
				if(insertAfterXpath && kriya.xpath(insertAfterXpath).length > 0){
					var btnChildContainer = kriya.xpath(insertAfterXpath);
					$(btnChildContainer).after(clonedNode);
				}else{
					$(btnContainer).append(clonedNode);
				}
			}
		});

		//Initialize the filter options
		kriya.general.iniFilterOptions($('.filterOptions .dropdown-content:not([id$=-sort-filter])'), '');
		//Initialize the LQC card count
		kriya.general.iniCardCount($('.lqc-div:not([lqc-display-none="true"])'), '');

		var treeData = [
			{
			  'text' : 'All Floats', 
			  'children': []
			}
		];

		var figObj = {'text' : 'All Figures', 'id' : 'all-fig', 'children': []};
		var tblObj = {'text' : 'All Tables','id' : 'all-tbl', 'children': []};
		var vidObj = {'text' : 'All Videos','id' : 'all-vid', 'children': []};
		var boxObj = {'text' : 'All Boxes','id' : 'all-box', 'children': []};
		var supplObj = {'text' : 'All Supplements','id' : 'all-suppl', 'children': []};

		$('.findSection .findAction .dropdown-content').each(function(){
			var blockClass = $(this).attr('data-class');
			var that = this;
			if($(kriya.config.containerElm).find('.' + blockClass).not('.de, [data-track="del"]').length > 0){
				$(kriya.config.containerElm).find('.' + blockClass).not('.de, [data-track="del"]').each(function(){
					var dataID = $(this).attr('data-id');
					if($(this).find('.label').length > 0){
						var labelText = $(this).find('.label').clone(true).cleanTrackChanges().text();	
					}else if($(this).find('[class$="Caption"]:first').length > 0){
						var labelText = $(this).find('[class$="Caption"]:first').text();
						labelText = labelText.replace(/^\s+/g, '');
						labelText = labelText.substr(0, 10);
						labelText = labelText + '...';
					}
					$(that).append('<li data-value="' + dataID + '"><input class="findCheckButton" type="checkbox"/> ' + labelText + '</li>');
				});
			}else{
				$(this).parent().addClass('hidden');
			}	
		});

		$(kriya.config.containerElm).find('.jrnlFigBlock, .jrnlTblBlock, .jrnlSupplBlock, .jrnlBoxBlock, .jrnlSupplBlock, .jrnlVidBlock, .jrnlMapBlock').each(function(){
			var className = $(this).attr('class');
			var dataID    = $(this).attr('data-id');
			dataID = "jstree-" + dataID;
			if($(this).find('.label').length > 0){
				var labelText = $(this).find('.label').clone(true).cleanTrackChanges().text();	
			}else if($(this).find('[class$="Caption"]:first').length > 0){
				var labelText = $(this).find('[class$="Caption"]:first').text();
				labelText = labelText.replace(/^\s+/g, '');
				labelText = labelText.substr(0, 10);
				labelText = labelText + '...';
			}
			(className == "jrnlFigBlock")?(figObj.children.push({'text' : labelText, 'id' : dataID})):(className == "jrnlMapBlock")?(figObj.children.push({'text' : labelText, 'id' : dataID})):(className == "jrnlTblBlock")?tblObj.children.push({'text' : labelText, 'id' : dataID}):(className == "jrnlSupplBlock")?supplObj.children.push({'text' : labelText, 'id' : dataID}):(className == "jrnlBoxBlock")?boxObj.children.push({'text' : labelText, 'id' : dataID}):vidObj.children.push({'text' : labelText, 'id' : dataID});
		});
		if($(kriya.config.containerElm).find('.jrnlFigBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(figObj);
		}
		if($(kriya.config.containerElm).find('.jrnlTblBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(tblObj);
		}
		if($(kriya.config.containerElm).find('.jrnlVidBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(vidObj);
		}
		if($(kriya.config.containerElm).find('.jrnlBoxBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(boxObj);
		}
		if($(kriya.config.containerElm).find('.jrnlSupplBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(supplObj);
		}
		if($(kriya.config.containerElm).find('.jrnlMapBlock:has([class$="Caption"])').length > 0){
			treeData[0].children.push(supplObj);
		}
		
		$('.searchInObj').jstree({
			'core' : {
				'themes' : {'icons' : false},
				'data' : treeData
			},
			'plugins': ['checkbox']
		 });

	}

	setTimeout(function(){
		initialiseDragDrop();
		eventHandler.components.reference.getReferenceModal();
		eventHandler.components.citation.initCitationConfig();

		if(kriyaEditor.init.isEditorSaveEnabled != true && $('#contentContainer').attr('data-state') != 'read-only'){
			var screenNode = $('<div class="saveNotLoaded" id="lock-screen" style="background: rgb(245, 245, 245); height: 100%; left: 0px; overflow: auto; position: fixed; pointer-events: all; padding: 10px; top: 0px; width: 100%; z-index: 10000; display: block;"/>');
			screenNode.append('<div class="row" style="background-color: #f5b113;padding: 25px !important;text-align: center;font-size: 2rem;">It seems that the editor page didn\'t load correctly. Please refresh and try again.</div>');
			$('body').append(screenNode);	
			return false;
		}


		var resizeTimer;
		$('#contentDivNode > br').remove();
		$("#contentDivNode *[data-reordered]").removeAttr('data-reordered')
		
		createCloneContent();
		/*$('#contentDivNode').scroll(function(){
			$('#clonedContent').scrollTop($('#contentDivNode').scrollTop())
			$('.suggestions,.resolve-query').remove();
			setTimeout(function(){
				if ($('.query-div.hover-inline').find('.cancelReply').length > 0){
					$('.query-div.hover-inline').find('.cancelReply').trigger('click');
				}else if ($('.query-div.hover-inline').find('.com-close').length > 0){
					$('.query-div.hover-inline').find('.com-close').trigger('click');
				}else{
					$('.query-div.disabled').remove();
					$('.query-div.hover-inline').removeAttr('style').removeClass('hover-inline');
				}
			},100);
		});*/

		kriyaEditor.settings.startValue = kriyaEditor.settings.contentNode.innerHTML;

		var sel = rangy.getSelection();
		var blockNode = $(sel.anchorNode).closest('[id]:not(span)');
		if(blockNode.length > 0){
			kriyaEditor.settings.startCaretID = blockNode.attr('id');
			kriyaEditor.settings.startCaretPos = getCaretCharacterOffsetWithin(blockNode[0]);
		}else{
			kriyaEditor.settings.startCaretID  = "";
			kriyaEditor.settings.startCaretPos = "";
		}
		

		kriyaEditor.settings.cloneNode = document.getElementById('clonedContent');
		kriyaEditor.settings.cloneValue = kriyaEditor.settings.cloneNode.innerHTML;
		eventHandler.menu.navigation.sortCards('',$('div #changesDivNode ul[id="changes-sort-filter"] li[data-sort="createdtime"]'))
		// commented by jai - moving it inside initCitationConfig so we get the citeJS which is used in regenerateActionQuery - 18 Feb 2021
		/*eventHandler.query.action.regenerateActionQuery();
		//Order the query based on location
		eventHandler.query.action.orderQuery();*/

		// if ($('[data-component="eqnNew_edit"]').length == 0){
		// 	var mathMLConversion = false;
		// 	if(citeJS && citeJS.settings && citeJS.settings.mathMLConversion && citeJS.settings.mathMLConversion == 'true') {
		// 		mathMLConversion = true;
		// 	}
		// 	let eqnEditorUrl = '/equation_editor/?codeType=Latex&encloseAllFormula=false&style=aguas&localType=en_US&customer='+kriya.config.content.customer+'&project='+kriya.config.content.project+'&mathMLConversion='+mathMLConversion;
		// 	$('.templates').append(`<div data-type="popUp" data-component="eqnNew_edit" data-display="modal" class="hidden z-depth-2 bottom"><div data-input-editable="true" style="top: 3%;width: 90%;height: 90vh;"><div class="row" style="height:100%"><iframe id="equation-editor" style="height: 95%; width: 100%;" src="${eqnEditorUrl}"/></div></div></div>`);
		// }

	},2000);

	setupWatch();
	//$.autoCite(); //commented by jai
	if (/version\?/.test(window.location.href)){
		$.ajax({
			type: "GET",
			url: "/api/getdata?customer="+kriya.config.content.customer+"&project="+kriya.config.content.project+"&xpath=//workflow/stage[last()]&doi=" + kriya.config.content.doi,
			contentType: "application/xml; charset=utf-8",
			dataType: "xml",
			success: function (data) {
				if (data){
					dataNode = data.documentElement;
					if ($(dataNode).find('> name').text() == $('#contentContainer').attr('data-stage-name')){
						var versionBtn = $('<span data-name="Approve" class="btn btn-small action-btn" data-page="review_content" data-message="{\'click\':{\'funcToCall\': \'restoreVersion\',\'channel\':\'welcome\',\'topic\':\'signoff\'}}"><i class="material-icons" style="font-size:1.4rem">check_box</i>&nbsp;<span class="menu-text">Make this current version</span></span>')
						$('.kriyaSubMenuContainer.navDivContent').append(versionBtn);
					}
				}
			}
		})
	}
	window.onerror = function(msg, url, line, col, error) {
	   // Note that col & error are new to the HTML 5 spec and may not be
	   // supported in every browser.  It worked for me in Chrome.
	   var extra = !col ? '' : '\ncolumn: ' + col;
	   extra += !error ? '' : '\nerror: ' + error;
	   //it will give flow of function calling.
	   var flow = error.stack ? "\nFlow:"+error.stack : "";
	   var browser = '';
	   // Chrome 1+
	   //After updated chrome (!!window.chrome && !!window.chrome.webstore) condition failing in windows
		if((!!window.chrome && !!window.chrome.webstore) || (navigator.userAgent.search("Chrome") >= 0)){
			browser = "Chrome";
		}else if((!!window.opr && !!opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0){// Opera 8.0+
			browser = "Opera";
		}else if(typeof InstallTrigger !== 'undefined'){// Firefox 1.0+
			browser = "Firefox";
		}else if(/constructor/i.test(window.HTMLElement) || (function (p) { return p.toString() === "[object SafariRemoteNotification]"; })(!window['safari'] || (typeof safari !== 'undefined' && safari.pushNotification))){// Safari 3.0+ "[object HTMLElementConstructor]"
			browser = "Safari";
		}else if(/*@cc_on!@*/false || !!document.documentMode){// Internet Explorer 6-11
			browser = "Internet Explorer";
		}else if(!(/*@cc_on!@*/false || !!document.documentMode) && !!window.StyleMedia){// Edge 20+
			browser = "Edge";
		//for blink we need to check like this.
		}else if(((!!window.chrome && !!window.chrome.webstore) || ((!!window.opr && !!opr.addons) || !!window.opera || navigator.userAgent.indexOf(' OPR/') >= 0)) && !!window.CSS){// Blink engine detection
			browser = "Blink";
		}else{
			browser = "UnKnown";
		}
	   //it will give target ele id className and parent ele className
	   var target = (kriya.evt && kriya.evt.target)? "\nTarget id: "+kriya.evt.target.id+", class: "+kriya.evt.target.className+", parentClass: "+kriya.evt.target.parentNode.className : "";
	   //it will give what browser they are using.(if not chrome and mozilla it will print others)
	   //var browser = (!!window.chrome && !!window.chrome.webstore)?"chrome":(typeof InstallTrigger !== 'undefined')? "Mozilla":"other";
	   // You can view the information in an alert to see things working like this:
	   var errorMsg = "Error: " + msg + "\nurl: " + url + "\nline: " + line + extra + "\nUser:" + kriyaEditor.user.name + "\n" + window.location.href + target + flow +"\nBrowser: "+browser ;
	   console.log(errorMsg);
	   // TODO: Report this error via ajax so you can keep track
	   //       of what pages have JS issues
		var parameters = {
			'log': errorMsg
		};
	   kriya.general.sendAPIRequest('logerrors',parameters,function(res){
			console.log('Data Saved');
		})
	   var suppressErrorAlert = true;
	   // If you return true, then error alerts (like in older versions of
	   // Internet Explorer) will be suppressed.
	   return suppressErrorAlert;
	};
}

function createCloneContent() {
	$('#clonedContent').remove();
	var clonedContent = $('<div id="clonedContent" class="nano-content">');

	/*var cid = Math.floor(Date.now() / 1000);
	$(kriya.config.containerElm).find('div.front,div.body,div.back').each(function(){
		if (!$(this)[0].hasAttribute('id')){
			$(this).attr('id', 'cid_'+cid);
		}
		$(this).children().each(function(){
			if (!$(this)[0].hasAttribute('id')){
				$(this).attr('id', 'cid_'+cid);
			}
			if (/^(DIV|UL|OL)$/.test($(this)[0].nodeName)){
				$(this).children().each(function(){
					if (!$(this)[0].hasAttribute('id')){
						$(this).attr('id', 'cid_'+cid);
					}
					var subChild = $(this);
					if (subChild[0].nodeName == "TABLE"){
						$(this).find('tr').each(function(i,e){
							if (!$(this)[0].hasAttribute('id')){
								$(this).attr('id', 'cid_'+cid);
							}
							$(this).find('th, td').each(function(i,e){
								if (!$(this)[0].hasAttribute('id')){
									$(this).attr('id', 'cid_'+cid);
								}
							});
						});
					}
				});
			}
		});
	});*/

	$(kriya.config.containerElm).after(clonedContent);
	var cloneHeight = 0
	$(kriya.config.containerElm).children().each(function(){
		cloneHeight += $(this).height();
	});

	var lastNode = $(kriya.config.containerElm + ' *:visible').last();
	var dummyNodeTop = lastNode[0].getBoundingClientRect().top + $('#contentDivNode').scrollTop();
	$('#clonedContent').append($('<span class="dummyHighlight" style="top:' + dummyNodeTop + 'px; display:inline-block;opacity:0;width: 18px;height: 18.5px;position: absolute;z-index: 0;"/>'));

	$('#clonedContent').append($('<div style="position: absolute;left: 0px;width: 1px;height: 1px;display: inline-block;opacity: 0;top:'+cloneHeight+'px"/>'));
	//$('#clonedContent').height($('#contentDivNode').height());
	$('#clonedContent').css('height', $('#contentDivNode').css('height'));
}

function cloneChild(node, cid){
	var subChild = $(node).clone();
	$(subChild).html('');
	if (subChild[0].hasAttribute('id')){
		$(subChild).attr('clone-id', $(subChild).attr('id'));
		$(subChild).removeAttr('id');
	}else{
		$(node).attr('id', 'cid_'+cid);
		$(subChild).attr('clone-id', 'cid_' + cid++);
	}
	subChild.css({
		'position': 'relative'
	})
	if ($(node).css('display') == "none"){
		subChild.height('0');
		subChild.css({
			'display': 'none'
		});
	}else{
		subChild.height($(node).outerHeight());
		subChild.css({
			'display': 'block'
		});
	}
	$(subChild).css('margin', $(node).css('margin'));
	$(subChild).css('margin-top', $(node).css('marginTop'));
	$(subChild).css('margin-bottom', $(node).css('marginBottom'));
	$(subChild).css('padding-top', $(node).css('paddingTop'));
	$(subChild).css('padding-left', $(node).css('paddingLeft'));
	$(subChild).css('padding-bottom', $(node).css('paddingBottom'));
	$(subChild).css('padding-right', $(node).css('paddingRight'));
	return [subChild, cid];
}

function callUpdateArticleCitation(saveNodes,nodeToLookInto){
	// To update the article title in "cite this as" information while edit the article title.
	if(saveNodes && saveNodes.length < 1){
		return false;
	}
	var authorNameUpdated = false;
	$(saveNodes).each(function(i, obj){
		//If array value is undefined or empty then continue the each loop
		if(!obj){
			return;
		}
		var saveNodeClass = '';
		var currNode = $(this);
		if (/^(I|B|EM|STRONG|SUP|SUB|U)/.test(currNode.nodeName)){
			currNode = currNode.parent();
		}
		if($(currNode).hasClass('ins') || $(currNode).hasClass('del')){
			currNode = $(this).closest(':not(".ins,.del")');
		}
		if(currNode){
			var clonedNode = $(currNode).clone();
			if($(clonedNode).hasClass('activeElement')){
				$(clonedNode).removeClass('activeElement');
			}
			saveNodeClass = $(clonedNode).attr('class');
			if(saveNodeClass && saveNodeClass != ""){
				clonedNode.find('.del').each(function(){
					$(this).remove();
				});

				clonedNode.find('*').each(function(){
					$(this).cleanTrackChanges();
					$(this).cleanTrackAttributes();
				});


				var nodeValue = $(clonedNode).html();
				var updateNode = '';

				if(!authorNameUpdated && saveNodeClass.match(/^(jrnlAuthorGroup|jrnlAuthorsGroup|jrnlAuthors|jrnlAuthor)$/i)){
					var checkElement = '';
					var authorNodesInNodeToLookInto = $('#contentDivNode '+nodeToLookInto).find('.jrnlAuthorCite');
					if(authorNodesInNodeToLookInto.length > 0){
						checkElement = $(authorNodesInNodeToLookInto)[0];
					}
					if($('#contentDivNode '+nodeToLookInto).length > 0 && checkElement && checkElement!="" && checkElement.hasAttribute('data-maxauthors')){
						var maxAuthorCount = $(checkElement).attr('data-maxauthors');
					}else{
						// If data-maxauthors is not available, we have to find author's count - priya #105
						var maxAuthorCount = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlAuthor').length+1;
					}
					var authorNameNodes = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"]):lt('+maxAuthorCount+')').find('.jrnlAuthor');
					var totalAuthors = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlAuthor');
					var totalAuthorCount = $(totalAuthors).length;
					var currAuthorCount = $(authorNameNodes).length;
					var authorNameHTML = '';
					var andIndex = '';
					if($(authorNameNodes).length > 0){
						if($(totalAuthors).length > 1 && ($(totalAuthors).length <= maxAuthorCount)){
							andIndex = $(authorNameNodes).length;
						}

						if(checkElement && checkElement!="" && checkElement.hasAttribute('data-penultimatePunc') && $(checkElement).attr('data-penultimatePunc')!=''){
							penultimatePunc = $(checkElement).attr('data-penultimatePunc');
						}
						//removed data-maxauthors condition, bcoz its client specific #105 - priya
						if($('#contentDivNode '+nodeToLookInto).length > 0 && checkElement && checkElement!="" && $(checkElement).length > 0){
							for(var i=0; i<maxAuthorCount; i++){
								var etalAuthorCount = '';
								var authorNameNode = $(authorNameNodes)[i];
								if(authorNameNode){
									var surName = $(authorNameNode).find('.jrnlSurName').html();
									var givenName = $(authorNameNode).find('.jrnlGivenName').html();
									//updated by aravind on 26/06/18
									if(checkElement && checkElement!="" && checkElement.hasAttribute('data-regExp') && $(checkElement).attr('data-penultimatePunc')!=''){
										givenName = givenName.replace(new RegExp($(checkElement).attr('data-regExp')),'');
									}

									if(checkElement.hasAttribute('data-abbrv-name') && $(checkElement).attr('data-abbrv-name') == "true" && givenName && givenName != ""){
										//#10 - Adding special character in Given Name : To Cite. - Vimala.J(<EMAIL>)
                    					//#10 - To get the First letter of Givenname even after space with Uppercase - Vimala.J (<EMAIL>)
										var matches = givenName.match(/(\s|^)(\w|\S)/g);
										givenNameMatch = matches.join('');
										givenNameMatchUpper = givenNameMatch.toUpperCase();
										givenName = givenNameMatchUpper.replace(/\s+/g,'');
										givenName = givenName.replace(/([A-Z])/, ' $1').trim();
										// End of #10
									}

									var surNameHTML = '<span class="jrnlSurName">'+surName+'</span>';
									var givenNameHTML = '<span class="jrnlGivenName">'+givenName+'</span>';

									if(checkElement.hasAttribute('data-author-format')){
										var authorFormat = $(checkElement).attr('data-author-format');

										if(authorFormat == "SG"){
											authorNameHTML = authorNameHTML + '<span class="jrnlAuthor">' + surNameHTML + ' ' + givenNameHTML + '</span>';
										}else if(authorFormat == "GS"){
											authorNameHTML = authorNameHTML + '<span class="jrnlAuthor">' + givenNameHTML + ' ' + surNameHTML + '</span>';
										}else{
											authorNameHTML = authorNameHTML + '<span class="jrnlAuthor">' + surNameHTML + ' ' + givenNameHTML + '</span>';
										}
									} else {
										authorNameHTML = authorNameHTML + '<span class="jrnlAuthor">' + surNameHTML + ' ' + givenNameHTML + '</span>';
									}

									if(andIndex && andIndex!= '' && (andIndex - 2) == i){
										authorNameHTML = authorNameHTML + penultimatePunc;
									}else if(((i == (maxAuthorCount - 1) || (currAuthorCount && (i == (currAuthorCount - 1)))) && (andIndex != '')) || currAuthorCount == 1){
										if(checkElement.hasAttribute('data-endPunc') && $(checkElement).attr('data-endPunc') != ""){
											authorNameHTML = authorNameHTML + $(checkElement).attr('data-endPunc');
										}else{
											authorNameHTML = authorNameHTML + ' ';
										}
									}else{
										authorNameHTML = authorNameHTML + ', ';
									}

									if(checkElement.hasAttribute('data-etalauthors') && ($(totalAuthors).length > maxAuthorCount)){
										etalAuthorCount = $(checkElement).attr('data-etalauthors');
										if(totalAuthorCount && etalAuthorCount && etalAuthorCount!="" && totalAuthorCount > etalAuthorCount){
											etalAuthorCount = etalAuthorCount - 1;
											var etalText = '';
											if(checkElement.hasAttribute('data-etal-text')){
												etalText = $(checkElement).attr('data-etal-text');
											}else{
												etalText = 'et al ';
											}
											if(i == etalAuthorCount){
												authorNameHTML = authorNameHTML + etalText;
												break;
											}
										}
									}
								}
							}

							if(authorNodesInNodeToLookInto.length > 0 && authorNameHTML && authorNameHTML != ""){
								var firstNode = $(authorNodesInNodeToLookInto)[0];
								$(firstNode).html(authorNameHTML);
								authorNameUpdated = true;
							}
						}
					}
				} else if ($('#contentDivNode ' + nodeToLookInto).find('.' + saveNodeClass + 'Cite').length > 0) {
					updateNode = $('#contentDivNode '+nodeToLookInto).find('.'+saveNodeClass+'Cite')[0];
				}else if($('#contentDivNode '+nodeToLookInto).find('.'+saveNodeClass).length > 0){
					updateNode = $('#contentDivNode '+nodeToLookInto).find('.'+saveNodeClass)[0];
				}
				if(updateNode){
					$(updateNode).html(nodeValue);
				}else{//updated by Anuraja to update copyright year into citation tag for view purpose
					$(clonedNode).children().each(function(){ 
						var className = $(this).attr('class');
						if(className && $('#contentDivNode ' + nodeToLookInto).find('.' + className + 'CiteTmp').length > 0){
							$('#contentDivNode ' + nodeToLookInto).find('.' + className + 'CiteTmp').html($(this).html())
						}
					})
				}
			}
		}
	});
	callUpdateArticleAuthorCopyRights(saveNodes, '.jrnlPermission');
}
 function closestNumber(array,num){
    var i=0;
    var minDiff=1000;
    var ans;
    for(i in array){
         var m=Math.abs(num-array[i]);
         if(m<minDiff){
                minDiff=m;
                ans=array[i];
            }
      }
    return ans;
}
function callUpdateArticleAuthorCopyRights(saveNodes,nodeToLookInto){

	if(saveNodes && saveNodes.length < 1){
		return false;
	}
	var authorNameUpdated = false;
	$(saveNodes).each(function(i, obj){

		if(!obj){
			return;
		}
		var saveNodeClass = '';
		var currNode = $(this);
		if (/^(I|B|EM|STRONG|SUP|SUB|U)/.test(currNode.nodeName)){
			currNode = currNode.parent();
		}
		if($(currNode).hasClass('ins') || $(currNode).hasClass('del')){
			currNode = $(this).closest(':not(".ins,.del")');
		}
		if(currNode){
			var clonedNode = $(currNode).clone();
			if($(clonedNode).hasClass('activeElement')){
				$(clonedNode).removeClass('activeElement');
			}
			saveNodeClass = $(clonedNode).attr('class');
			if(saveNodeClass && saveNodeClass != ""){
				clonedNode.find('.del').each(function(){
					$(this).remove();
				});

				clonedNode.find('*').each(function(){
					$(this).cleanTrackChanges();
					$(this).cleanTrackAttributes();
				});

				var nodeValue = $(clonedNode).html();
				var updateNode = '';
				var copyrightConfigured = $('[data-component="jrnlPermission_edit"] *[data-copyright-config="true"]')
				if(!authorNameUpdated && saveNodeClass.match(/^(jrnlAuthorGroup|jrnlAuthors|jrnlAuthor)$/i) && copyrightConfigured.length == 0){
					var checkElement = '';
					var authorNodesInNodeToLookInto = $('#contentDivNode '+nodeToLookInto).find('.jrnlCopyrightHolder');
					if(authorNodesInNodeToLookInto.length > 0){
						checkElement = $(authorNodesInNodeToLookInto)[0];
					}
					if($('#contentDivNode '+nodeToLookInto).length > 0 && checkElement && checkElement!="" && checkElement.hasAttribute('data-maxauthors')){
						var maxAuthorCount = $(checkElement).attr('data-maxauthors');
					}else{
						// If data-maxauthors is not available, we have to find author's count - priya #105
						var maxAuthorCount = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlAuthor').length+1;
					}
					var authorNameNodes = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"]):lt('+maxAuthorCount+')').find('.jrnlAuthor');
					var totalAuthors = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlAuthor');
					var totalAuthorCount = $(totalAuthors).length;
					var currAuthorCount = $(authorNameNodes).length;
					var authorNameHTML = '';
					var andIndex = '';
					if($(authorNameNodes).length > 0){
						if($(totalAuthors).length > 1 && ($(totalAuthors).length <= maxAuthorCount)){
							andIndex = $(authorNameNodes).length;
						}
						var penultimatePunc = '';
						if(checkElement && checkElement!="" && checkElement.hasAttribute('data-penultimatePunc') && $(checkElement).attr('data-penultimatePunc')!=''){
							penultimatePunc = $(checkElement).attr('data-penultimatePunc');
						}
						//removed data-maxauthors condition, bcoz its client specific #105 - priya
						if($('#contentDivNode '+nodeToLookInto).length > 0 && checkElement && checkElement!="" && $(checkElement).length > 0){
							for(var i=0; i<maxAuthorCount; i++){
								var etalAuthorCount = '';
								var authorNameNode = $(authorNameNodes)[i];
								if(authorNameNode){
									var surName = $(authorNameNode).find('.jrnlSurName').html();
									var surNameHTML = '<span class="jrnlSurName">'+surName+'</span>';
									authorNameHTML = authorNameHTML + '<span class="jrnlAuthor">' + surNameHTML + '</span>';
									if(andIndex && andIndex!= '' && (andIndex - 2) == i){
										authorNameHTML = authorNameHTML + penultimatePunc;
									}else if(((i == (maxAuthorCount - 1) || (currAuthorCount && (i == (currAuthorCount - 1)))) && (andIndex != '')) || currAuthorCount == 1){
										if(checkElement.hasAttribute('data-endPunc') && $(checkElement).attr('data-endPunc') != ""){
											authorNameHTML = authorNameHTML + $(checkElement).attr('data-endPunc');
										}else{
											authorNameHTML = authorNameHTML + ' ';
										}
									}else{
										authorNameHTML = authorNameHTML + ', ';
									}

									if(checkElement.hasAttribute('data-etalauthors')){
										etalAuthorCount = $(checkElement).attr('data-etalauthors');
										if(totalAuthorCount && etalAuthorCount && etalAuthorCount!="" && totalAuthorCount > etalAuthorCount){
											etalAuthorCount = etalAuthorCount - 1;
											var etalText = '';
											if(checkElement.hasAttribute('data-etal-text')){
												etalText = $(checkElement).attr('data-etal-text');
											}else{
												etalText = 'et al ';
											}
											if(i == etalAuthorCount){
												authorNameHTML = authorNameHTML + etalText;
												break;
											}
										}
									}
								}
							}

							if(authorNodesInNodeToLookInto.length > 0 && authorNameHTML && authorNameHTML != ""){
								var firstNode = $(authorNodesInNodeToLookInto)[0];
								$(firstNode).html(authorNameHTML);
								authorNameUpdated = true;
							}
						}
					}
				}

				if(updateNode){
					$(updateNode).html(nodeValue);
				}
			}
		}
	});

	//update copyright holder name based on equal author
	var mapEqualConAuthor = $('[data-component="jrnlPermission_edit"] *[data-mapequalcon-authoretal="true"]')
	if(mapEqualConAuthor.length > 0 ){
		var copyHolder = ""
		var totalAuthors = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlAuthor, .jrnlCollaboration'); // include collaborator authorgroup also
		var equalAuthors = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').first().find('.jrnlEqContribRef');
		if(equalAuthors.length > 0 && totalAuthors.length > 0 && $(totalAuthors[0]).parent().find('.jrnlEqContribRef').length > 0){
			var maxAuthor = 2;
			if(mapEqualConAuthor.attr('data-max-authorsetal')){
				maxAuthor = mapEqualConAuthor.attr('data-max-authorsetal');
			}
      		var firstEqualAttribID = $('#contentDivNode').find('.front').find('.jrnlAuthorGroup:not([data-track="del"])').find('.jrnlEqContribRef').first().attr('data-rid');
			var equalCon = true;
			var equalConCount = 0
			totalAuthors.each(function(i,obj){
				if(equalCon &&  maxAuthor > i){
					var equalRef = $(this).parent().find('.jrnlEqContribRef[data-rid="'+firstEqualAttribID+'"]')
					if($(totalAuthors[0]).attr('class') == 'jrnlCollaboration'){ // If current node have jrnlCollaboration class name 
						var surName = $(this);
					}
					else{
						var surName = $(this).find('.jrnlSurName')
					}
					if(equalRef.length > 0 ){
						equalConCount++
						if(totalAuthors.length == 2){
							if(i==0){
								copyHolder = copyHolder  + surName.text()	
							}else{
								copyHolder = copyHolder +  ' and ' + surName.text()
							}
						}else{
							copyHolder = copyHolder + surName.text() + ', '
						}
					}else{
						equalCon = false;
					}
				}
			})
			copyHolder = copyHolder.replace(/[\,\s\.]*$/,'');
			if(equalConCount != totalAuthors.length){
				copyHolder = copyHolder + ' et al';
			}

		}else if(equalAuthors.length == 0 && totalAuthors.length > 0){
			if($(totalAuthors[0]).attr('class') == 'jrnlCollaboration'){ // If current node have jrnlCollaboration class name 
				var surName = $(totalAuthors[0]);
			}
			else{
				var surName = $(totalAuthors[0]).find('.jrnlSurName');
			}
			if(totalAuthors.length > 2){
				copyHolder = copyHolder + surName.text() + ' et al' 
			}else if(totalAuthors.length == 2){
				var secondSurName = $(totalAuthors[1]).find('.jrnlSurName')
				copyHolder = copyHolder + surName.text() + ' and ' + secondSurName.text()
			}else{
				copyHolder = copyHolder + surName.text();
			}
		}

		var permission = $('#contentDivNode .front .jrnlPermission')
		if(copyHolder !="" && permission.length > 0){
			var statement = permission.find('.jrnlCopyrightStmt')
			var copyrightHolder = permission.find('.jrnlCopyrightHolder')
			if(statement.length > 0 ){
				statementNode = copyHolder;
				if(/(\u{00A9}\s\d+)(,.*)$/ui.test(statement.text())){ // Based on __20760 ; Copyright holder name not captured when its contains unicode
					statementNode = statement.text().replace(/(\u{00A9}\s\d+)(,.*)$/ui,'$1') + ', ' + copyHolder; // Replace pattern changed as after the © 2023(year) contents replaced with © 2023. Replace to change regex pattern, avoid to add new charater in regex pattern // Updated on 05.09.2023 by Kavitha E
				}
				statement.html(statementNode)	
			}
			if(copyrightHolder.length > 0 ){
				copyrightHolder.html(copyHolder)
			}
			kriyaEditor.settings.undoStack.push(permission);
			kriyaEditor.init.addUndoLevel('update permission');
		}
	}
}

function initManuscriptPreview(){
	// check for manuscript and click it
	if ($('#fileListTabs select option[file_designation="manuscript"][file_extension=".doc"], #fileListTabs select option[file_designation="manuscript"][file_extension=".docx"]').length > 0){		
		$('#fileListTabs select option[file_designation!="manuscript"]').addClass('hidden');	
		$('#fileListTabs select option[file_designation="manuscript"][file_extension=".doc"], #fileListTabs select option[file_designation="manuscript"][file_extension=".docx"]').first().prop('selected', true).change();	
	}else if ($('#fileListTabs select option[file_extension=".docx"], #fileListTabs select option[file_extension=".doc"]').length > 0){
		$('#fileListTabs select option[file_extension=".docx"], #fileListTabs select option[file_extension=".doc"]').first().prop('selected', true).change();
	}
	else{
		$('#contentPreviewDiv').children('p').find('a').remove();
		$('#contentPreviewDiv .file-viewer').html('<span style="display: block;text-align: center;margin-top: 25%;font-size: 18px;color: #3a3a3a;">The manuscript could not be displayed due to an unsupported format.</span><span style="display: block;text-align: center;margin-top: 3%;font-size: 15px;color: grey;">Files formats supported: Doc., Docx."</span>')
	}
	$('#fileListTabs select option[file_extension!=".docx"][file_extension!=".doc"]:not(".hidden")').addClass('hidden')
	//Added by Anuraja to display dropdown for multiple manuscript files and hide if only one manuscript
	if($('#fileListTabs select option:not(.hidden)').length < 2){
		$('#fileListTabs').addClass('hidden');
	}else{
		$('#fileListTabs').removeClass('hidden');
	}
}

function constructPage(){
	let queryParams = '?customer=' + $('#contentContainer').attr('data-customer') + '&project=' + $('#contentContainer').attr('data-project') + '&doi=' + $('#contentContainer').attr('data-doi') + '&articleState=' + $('#contentContainer').attr('data-state') + '&articlesRole=' + $('#contentContainer').attr('data-role') + '&customerType=' + $('#contentContainer').attr('data-customer-type') + '&accessType=' + $('#contentContainer').attr('article-access-type') +'&offlineState=' + $('#contentContainer').attr('data-offline-workflow');
	$.ajax({
		url : '/api/getarticledata' + queryParams,
		method : "GET",
		cache : false,
		success: function(data){
			//$('body').append('<div style="background:blue;color: white;position: fixed;top: 5px;left: 46%;z-index: 10000;font-size: 10px;padding: 1px;">' + (new Date() - window.performance.timing.connectStart)/1000 + '</div>')
			$('#contentContainer').html($(data).find('article-html').html());
			let navChilds = $(data).find('navcontent navdata').children();
			for (var n = 0, nl = navChilds.length; n < nl; n++){
				$('#infoDivContent').append(navChilds[n]);
			}
			$('#infoDivContent').append($('#helpDivNode'));
			$('#helpDivNode').removeAttr('class');
			initPageLoad();
		},
		error: function(err){
			var screenNode = $('<div class="saveNotLoaded" id="lock-screen" style="background: rgb(245, 245, 245); height: 100%; left: 0px; overflow: auto; position: fixed; pointer-events: all; padding: 10px; top: 0px; width: 100%; z-index: 10000; display: block;"/>');
			screenNode.append('<div class="row" style="background-color: #f5b113;padding: 25px !important;text-align: center;font-size: 2rem;">It seems that the editor page didn\'t load correctly. Please refresh and try again.</div>');
			$('body').append(screenNode);	
			return false;
		}
	});
	$('.la-container').find('.la-container-status').remove();
	$('.la-container').append('<div class="la-container-status" style="width: 100%;top: 60%;position: absolute;"><span class="statusBackground" style="background: #e1e1e1;font-size:15px;">Loading components...</span></div>');
	$.ajax({
		url : '/api/getcomponents' + queryParams,
		method : "GET",
		cache : false,
		success: function(data){
			$('#compDivContent').html($(data).find('components').html());
			$('#compDivContent').attr('data-project', kriya.config.content.project)
      		if ($('[data-component="eqnNew_edit"]').length == 0){
				var mathMLConversion = false;
				if(citeJS && citeJS.settings && citeJS.settings.mathMLConversion && citeJS.settings.mathMLConversion == 'true') {
					mathMLConversion = true;
				}
				let eqnEditorUrl = '/equation_editor/?codeType=Latex&encloseAllFormula=false&style=aguas&localType=en_US&customer='+kriya.config.content.customer+'&project='+kriya.config.content.project+'&mathMLConversion='+mathMLConversion;
				$('.templates').append(`<div data-type="popUp" data-component="eqnNew_edit" data-display="modal" class="hidden z-depth-2 bottom"><div data-input-editable="true" style="top: 3%;width: 90%;height: 90vh;"><div class="row" style="height:100%"><iframe id="equation-editor" class="tmp-load" style="height: 95%; width: 100%;" src="${eqnEditorUrl}"/></div></div></div>`);
			}
			$('#headerContainer').html($(data).find('header').html());
			$('#headerContainer').before($(data).find('welcome').html());
			$('body').append($(data).find('custom-config-template'))
			$('.la-container').find('.la-container-status').remove();
			$('.la-container').append('<div class="la-container-status" style="width: 100%;top: 60%;position: absolute;"><span class="statusBackground" style="background: #e1e1e1;font-size:15px;">Loading content...</span></div>');
			var userProfile = $("#headerContainer .userProfile");
			if ($('#contentContainer').attr('data-state') == 'read-only' && userProfile.length > 0){
				var tmpNode = $('<span class="btn btn-medium pull-right blue" style="margin: 6px 20px 0px 0px;padding: 1px 15px;font-size: 1rem !important;line-height: 26px !important;background: #0d618b;">Read Only</span>');
				userProfile.before(tmpNode);
				if ($('#contentContainer').attr('data-version') == 'true'){
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">The article is a version from backup.</span>');
				}else if ($('#contentContainer').attr('data-kriya-article-version') == 'v1.0'){
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">The is a kriya version 1.0 article.</span>');
				}else if ($('#contentContainer').attr('data-assigned') == 'false'){
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">Access denied as article is not assigned to you.</span>');
				}else if ($('#contentContainer').attr('data-access-level') == 'false'){
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">Access denied as article is in <span style="text-decoration:underline;">' + $('#contentContainer').attr('data-stage-name') + '</span> stage.</span>');
				}else if ($('#contentContainer').attr('data-has-link') == 'true'){
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">Access denied as article link sent to <span style="text-decoration:underline;">' + $('#contentContainer').attr('data-role') + '</span>.</span>');
				}else{
					var tmpNode = $('<span style="padding: 12px;font-size: 15px;font-weight: bold;text-align: center;display: block;background: red;color: #fff;margin: 3px 15% 0px;">The article is LOCKED because <span style="text-decoration:underline;">' + $('#contentContainer').attr('data-current-user') + '</span> is viewing it.</span>');
				}
				userProfile.before(tmpNode);
			}
			//Offlne article read only mode;
			if($('#contentContainer').attr('data-offline-article-readonly') && $('#contentContainer').attr('data-offline-article-readonly') == "true"){
				$('#contentContainer').attr('data-state', 'read-only');
				$('.com-save, .save-author, .query-action').remove();
				$('.time-notice').attr('class', 'offlinearticle').html('This article was processed offline. You are viewing a read-only version and cannot make changes to the manuscript');
				$('.kriyaMenuContainer *[data-name^="Export"], .btnGroupContainer').remove()
			}
			initPageLoad()
		},
		error: function(err){
			var screenNode = $('<div class="saveNotLoaded" id="lock-screen" style="background: rgb(245, 245, 245); height: 100%; left: 0px; overflow: auto; position: fixed; pointer-events: all; padding: 10px; top: 0px; width: 100%; z-index: 10000; display: block;"/>');
			screenNode.append('<div class="row" style="background-color: #f5b113;padding: 25px !important;text-align: center;font-size: 2rem;">It seems that the editor page didn\'t load correctly. Please refresh and try again.</div>');
			$('body').append(screenNode);	
			return false;
		}
	})
}

function initPageLoad(){
	if ($('#contentDivNode').length == 0 || $('#compDivContent .templates').length == 0){
		return;
	}
	$('.la-container').find('.la-container-status').remove();
	if($('.inviteMessageContainer').attr('data-accept-article') == "true"){			
		eventHandler.welcome.signoff.acceptCopyeditor()
		return true;
	}else if($('.inviteMessageContainer').attr('data-reject-article') == "true"){
		eventHandler.welcome.signoff.rejectCopyeditor()
		return true;
	}
	$('.la-container').append('<div class="la-container-status" style="width: 100%;top: 60%;position: absolute;"><span class="statusBackground" style="background: #e1e1e1;font-size:15px;">Initialising editor...</span></div>');
	/** thing that has to be executed before iniitializing all events **/
	var navDummyNodes = $('#proofviewDivNode [data-name][data-replace-config]');
	for (var n = 0, nl = navDummyNodes.length; n < nl; n++){
		var dataName = $(navDummyNodes[n]).attr('data-name');
		var actualNodes = $('#headerContainer').find('span[data-name="' + dataName + '"][data-dummy-element], ul[data-name="' + dataName + '"][data-dummy-element]');
		navDummyNodes[n].removeAttribute('data-replace-config')
		if (actualNodes.length > 0){
			actualNodes[0].removeAttribute('data-dummy-element');
			var tempAttributes = actualNodes[0].attributes;
			for (var tempAttr of tempAttributes){
				var attrName = tempAttr.name;
				if(!attrName.match(/data-role-/g)){
					$(navDummyNodes[n]).attr(attrName, tempAttr.value);
				}
			}
			actualNodes[0].remove();
		}else{
			navDummyNodes[n].remove();
		}
	}

	if ($('[data-name="addConIndex"]').length == 0){
		$('[id="indexIcon"]').remove();
	}
	if ($('[data-name="addGlossary"]').length == 0){
		$('[id="glossaryIcon"]').remove();
	}

	var trimmedStageName = $('#contentContainer').attr('data-stage');
	var propertyNodes = $('custom-config-template > *');
	for (var n = 0, nl = propertyNodes.length; n < nl; n++){
		var propertyNode = $(propertyNodes[n]);
		var dataXpath = propertyNode.attr('data-xpath');
		var xpathNode = kriya.xpath(dataXpath);
		if (xpathNode.length > 0){
			if(xpathNode[0].hasAttribute('data-custom-node')){
				$(xpathNode[0]).removeAttr('data-custom-node');
			}									
			if (propertyNode[0].hasAttribute('data-remove-stage')){
				if (propertyNode.attr('data-remove-stage').split(',').indexOf(trimmedStageName) >= 0){
					xpathNode[0].remove();
				}
			}else if (propertyNode[0].hasAttribute('data-retain-stage')){
				if (propertyNode.attr('data-retain-stage').split(',').indexOf(trimmedStageName) < 0){
					xpathNode[0].remove();
				}
			}else if (propertyNode[0].hasAttribute('data-remove-node')){
				xpathNode[0].remove();
			}
		}								
	}
	$('custom-config-template').remove();
	$('div[id="navContainer"] *[data-custom-node]').remove();
	var role = kriya.config.content.role;
	if(role!=''){
		$('#pdfviewContainer a.unresolvedQuery').text($('#queryDivNode .query-div.'+role+':not([data-replied="true"]):not([data-answered="true"])').length);
	}
	$('#infoDivContent').attr('data-role', $('#contentContainer').attr('data-role')).attr('data-stage', $('#contentContainer').attr('data-stage'))
	$('#compDivContent').attr('data-role', $('#contentContainer').attr('data-role'))
	
	$(".dropdown-button").dropdown()
	$(".collapsible").collapsible()
	$("ul.tabs").tabs()
	$(".tooltipped").tooltip()
	/** END OF PRE-PROCESS **/
	
	contentDivNode = document.getElementById('contentDivNode');
	kriyaEditor.init.cleanupHTML();
	if (! contentDivNode.parentNode.hasAttribute('data-readonly')){
		editor = kriyaEditor.init.editor(contentDivNode);
		if (editor){
			editor.on("load", function() {
				kriyaEditor.init.undoRedo(contentDivNode);
			});

			tracker = kriyaEditor.init.tracker(contentDivNode);
			if (tracker){
				tracker.startTracking();
			}
			if (! contentDivNode.parentNode.hasAttribute('data-version')){
				eventHandler.tableSetter.action.initTablesetter();
			}
		}
		else{
			kriyaEditor.init.undoRedo(contentDivNode);
		}
	}else{
		kriyaEditor.init.undoRedo(contentDivNode);
	}

	kriyaEditor.user = {};
	kriyaEditor.user.name = $('#headerContainer .userProfile .username').text();
	if(kriya.config.content.role=='publisher'){
		kriyaEditor.user.name = kriyaEditor.user.name+' ('+kriya.config.content.customer.toUpperCase()+')';
	}else if (kriya.config.content.role){
		kriyaEditor.user.name = kriyaEditor.user.name+' ('+kriya.config.content.role.toUpperCase()+')';
	}else{
		kriyaEditor.user.name = kriyaEditor.user.name;
	}
	kriyaEditor.user.id   = $('#headerContainer .userProfile .username').attr('data-user-id');
	
	callInitializeEvents();
	kriya.components();
	kriya.events();
	kriyaEditor.init.loadImage();
	if ($('#proofviewDivNode').length > 0){
		updateContentViewNavigationPanel();
	}
	
	//To hide and show option enable
	if($('#contentContainer').attr('data-track-display') == "false"){
		$('[data-name="MenuBtnTrackChanges"]').find('.icon-eye').addClass('icon-eye-blocked').removeClass('icon-eye');
	}

	if ($('#proofviewDivNode').length > 0){
		callPDFViewer();
		callMainPDFViewer();
		$('#pdfViewer').on("load", function() {
			callPDFViewer();
		});
		$('#pdfViewers').on("load", function() {
			callMainPDFViewer();
		});
	}else if ($('.nav-action-icon.content-icon').length > 0){
		$('.nav-action-icon.content-icon').trigger('click');
	}
	if ($('.nav-action-icon.content-icon').length > 0){
		initManuscriptPreview();
	}
	$('.col.s8').scroll(function (evt) {
		$('.kriya-tags-suggestions').addClass("hidden");
	});
	$('.la-container').find('.la-container-status').remove();
	$('.la-container').fadeOut();
}