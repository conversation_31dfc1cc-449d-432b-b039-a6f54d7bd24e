(function (kriyaEditor) {
    kriyaEditor.init.isEditorSaveEnabled = true;
    kriyaEditor.init.save = function(saveNodes, actionType){
        //If action type is not come and if the role typesetter or preeditor then consider as invalid save request and block the screen. - JAGAN
        if(!actionType){
            if(kriya.config.content.role == "typesetter" || kriya.config.content.role == "preeditor"){
                $('.la-container').fadeOut();
                $('body .messageDiv .message').html('Invalid save request triggered. Please reload the article to move to support.');
                $('body .messageDiv').removeClass('hidden');
                $('body .messageDiv .feedBack').removeClass('hidden');
                return false;
            }
        }

        if ($('#contentContainer').attr('data-state') == "read-only") return false;
        //if (arguments.callee.caller == null) return false;
        if(saveNodes && saveNodes.length < 1){
            return false;
        }
        
        //The below line was commented because it consuming more time when typing - jagan
        //callUpdateArticleCitation(saveNodes,'.jrnlCitation');
        if(kriya.config.content.doi && kriya.config.content.customer && saveNodes){
            var content_element = document.createElement('content');
            $(saveNodes).each(function(){
                if($(this)[0].length == 0){
                    return true;
                }
                var saveNode = $(this);

                //If glossary comes to save then call update glossary function else do normal - jagan
                if(saveNode.closest('#glossaryPanelContent').length > 0){
                    eventHandler.components.index.updateConIndex(saveNode, 'glossary_terms', 'glossaryPanelContent');
                    return;
                }else if(saveNode.closest('#conIndexPanelContent').length > 0){
                    eventHandler.components.index.updateConIndex(saveNode, 'concordance_index_terms', 'conIndexPanelContent');
                    return;
                }else if(saveNode.closest('#indexPanelContent').length > 0){
                    eventHandler.components.index.updateConIndex(saveNode, 'index_terms', 'indexPanelContent');
                    return;
                }

                if($(this).closest('[data-save-node][id]').length > 0){
                    saveNode = $(this).closest('[data-save-node][id]');
                    saveNode.removeAttr('data-save-node');
                }

                var prevElement = $(saveNode)[0].previousSibling;
                var nextElement = $(saveNode)[0].nextSibling;

                //If next and prev sibling or next sibling is a white space then get the sibling sibling
                //If text node value is more than a space then take previous sibiling - priya
                if(prevElement && prevElement.nodeType == 3 && prevElement.nodeValue.trim() == ""){
                    prevElement = prevElement.previousSibling;
                }
                if(nextElement && nextElement.nodeType == 3 && nextElement.nodeValue.trim() == ""){
                    nextElement = nextElement.nextSibling;
                }

                //If sibling element is jrnlTblContainer then get the sibling as table inside that container - jagan
                if($(prevElement).attr('class') == "jrnlTblContainer" && $(prevElement).find('table').length > 0){
                    prevElement = $(prevElement).find('table')[0];
                }

                if($(nextElement).attr('class') == "jrnlTblContainer" && $(nextElement).find('table').length > 0){
                    nextElement = $(nextElement).find('table')[0];
                }

                //if the savenode match selector given in kriya.config.sameClassSiblingId
                //then previous and next element class should match the savenode class
                //ex: corresponding affiliation
                if($(saveNode).closest(kriya.config.sameClassSiblingId).length > 0 && $(saveNode).attr('class')){
                    var saveNodeClass = $(saveNode).attr('class').replace(/^\s+|\s+$/g, '').split(/\s/)[0];
                    if(prevElement && $(prevElement).attr('class')){
                        var prevNodeClass = $(prevElement).attr('class').replace(/^\s+|\s+$/g, '').split(/\s/)[0];
                        if(prevNodeClass != saveNodeClass){
                            prevElement = null;
                        }
                    }
                    if(nextElement && $(nextElement).attr('class')){
                        var nextNodeClass = $(nextElement).attr('class').replace(/^\s+|\s+$/g, '').split(/\s/)[0];
                        if(nextNodeClass != saveNodeClass){
                            nextElement = null;
                        }
                    }
                }

                //If sibling element doesn't have sinling and inserted node and parent has only two child, content will not save
                //So set sibling has nul, In the next set if sibling is not present then parent will go to save - jagan (11-06-2020)
                if(saveNode && saveNode[0].hasAttribute('data-inserted')){
                    if(nextElement && nextElement.nodeType != 3 && nextElement.hasAttribute('id') && nextElement.getAttribute('data-inserted') == "true" && !nextElement.nextSibling && $(nextElement).parent().children().length == 2){
                        nextElement = null;
                    }
                    if(prevElement && prevElement.nodeType != 3 && prevElement.hasAttribute('id') && prevElement.getAttribute('data-inserted') == "true" && !prevElement.previousSibling && $(prevElement).parent().children().length == 2){
                        prevElement = null;
                    }
                }

                saveNode = $(saveNode).clone(true);

                //remove the date picker class
                saveNode.removeClass('picker__input--target');
                saveNode.removeClass('activeElement');

                if(!$(saveNode).is(kriya.config.preventSiblingId)){
                    var siblingIdStatus = false;
                    //Add prev id if prev element is not text element and it has id
                    if(saveNode && $(prevElement).length > 0 && prevElement.nodeType != 3 && prevElement.hasAttribute('id')){
                        saveNode.attr('data-prevID', prevElement.getAttribute('id'));
                        siblingIdStatus = true;
                    }
                    //Add next id if next element is not text element and it has id
                    if(saveNode && $(nextElement).length > 0 && nextElement.nodeType != 3 && nextElement.hasAttribute('id')){
                        saveNode.attr('data-nextID', nextElement.getAttribute('id'));
                        siblingIdStatus = true;
                    }
                    //if the element is inserted and prev, next element is text node then save the closest node which has id
                    if(!siblingIdStatus && saveNode[0].hasAttribute('data-inserted')){
                        if($(this).parents('[id]:not(' + kriya.config.invalidSaveNodes + '):first').length > 0){
                            saveNode = $(this).parents('[id]:not(' + kriya.config.invalidSaveNodes + '):first');
                            saveNode = $(saveNode).clone(true);
                        }
                    }
                }

                //nextID OR prevID is mandatory for moved save nodes
                //Remove the data-moved attr if save node doesn't have sibling id
                if(saveNode && saveNode.attr('data-moved') == "true" && !saveNode.attr('data-nextID') && !saveNode.attr('data-prevID')){
                    saveNode.removeAttr('data-moved');
                }
                if(saveNode && saveNode.closest(kriya.config.preventDoubleSpacing).length == 0){
                   var contentData = saveNode[0].innerHTML;
                   contentData = contentData.replace(/[ ]*(&nbsp;)+|(&nbsp;)+ +/g, ' '); //modify by jagan
                   contentData = contentData.replace(/[ ]+/g, ' ');
                   saveNode[0].innerHTML = contentData
                }
                if(saveNode.length > 0){
                    content_element.appendChild(saveNode[0]);
                }
            });

            $(content_element).find('.activeElement').removeClass('activeElement'); //Remove the active element class
            $(content_element).find('.wysiwyg-tmp-selected-cell').removeClass('wysiwyg-tmp-selected-cell');
            $(content_element).find('.floatHeader,.jrnlFigMetaHead').remove();
            $(content_element).find(kriya.config.removeEmptyNodes).each(function(){
                if($(this).text() == ""){
                    $(this).remove();
                }
            });
            $(content_element).find('.jrnlHistory .jrnlPPrint, .jrnlHistory .jrnlPPrintRevised,.jrnlHistory .jrnlPPrintPosted').each(function(){
                $(this).parent().after(this)
            })
            $(content_element).find('.kriya-nbsp,.kriya-ensp,.kriya-emsp,.kriya-thin,.kriya-lnbreak, font').contents().unwrap();
            $(content_element).find('.kriya-nbsp:empty,.kriya-ensp:empty,.kriya-emsp:empty,.kriya-thin:empty,.kriya-lnbreak:empty, icepaste').remove();
            $(content_element).find('style,meta,link,script').remove();
            $(content_element).find('[data-hide-query]').removeAttr('data-hide-query');
            $(content_element).find('img[loading="lazy"]').removeAttr('loading');
            

            var contentHTML = content_element.outerHTML;
            //return false;
            if(content_element.innerHTML != ""){
                kriya.config.saveQueue.push({
                    'action' : actionType,
                    'content' : contentHTML
                });
            }
            // Added by Saravanan - When hitting the enter key multiple times data-inserted is not removed as it was getting removed in the success callback,
            // so the second enter para(same para) was considered as inserted bcuz of data-inserted
            // Inserting the inserted,removed and moved attribs before sending API hit
            // Only removing the save nodes attribs
            // $(saveNodes).each(function() {
            //     $(this).removeAttr('data-inserted');
            //     $(this).removeAttr('data-removed');
            //     $(this).removeAttr('data-moved');
            // });
            if(typeof(saveNodes) == "object"){
                for (let i = 0; i < saveNodes.length; i++) {
                    $(saveNodes[i]).removeAttr('data-inserted');
                    $(saveNodes[i]).removeAttr('data-removed');
                    $(saveNodes[i]).removeAttr('data-moved');
                }
            }
            if(kriya.config.saveQueue.length == 1){
                kriyaEditor.init.saveQueueData();
             }
             if (location.pathname.match(/(proof_review)/)){
                updateContentViewNavigationPanel();
            }
            return false;
        }
    }
    // Track the last five actions performed in lastsaveQueue
    kriyaEditor.init.addSaveData = function(item){
        kriya.config.lastsaveQueue.push({...item,timeStamp: new Date().getTime()});
        if (kriya.config.lastsaveQueue.length > 5) kriya.config.lastsaveQueue.shift();
    }
    kriyaEditor.init.saveQueueData = function(){
        if(!window.navigator.onLine){ // to prevent save if there is know internet connectivity
            return false;
        }
        if(kriya.config.saveQueue.length == 0){
            return false;
        }
        var customerType = $('#contentContainer').attr('data-customer-type');
        var formData = new FormData();
        formData.append('customer', kriya.config.content.customer);
        formData.append('customerType', customerType);
        formData.append('doi', kriya.config.content.doi);
        formData.append('project', kriya.config.content.project);
        formData.append('action', kriya.config.saveQueue[0]['action']);
        // When related article compoent is edited, there is a need to update the elastic. Elastic update is skkiped by default, so updating the req body to update the elastic
        if(kriya && kriya.popUp && kriya.popUp.component && kriya.popUp.component.name){
            var curPopUpComponent = $(`[data-component='${kriya.popUp.component.name}`);
            if(curPopUpComponent && curPopUpComponent.attr("data-skipElasticUpdate") != undefined){
                formData.append("skipElasticUpdate", curPopUpComponent.attr("data-skipElasticUpdate"));
            }
        }
        if(kriya.config.errorSaveQueue.indexOf(kriya.config.saveQueue[0]) < 0){
            formData.append('occurrence', '1');
            kriyaEditor.init.addSaveData(kriya.config.saveQueue[0]);
        }else{
            formData.append('occurrence', '2');
        }
        
        var blob = new Blob([kriya.config.saveQueue[0]['content']], {
            type: 'text/html'
        });
        //var fileToBeUp = new File([blob], 'save_data.html');
        //formData.append('file', fileToBeUp, fileToBeUp.name);
        formData.append('file', blob, 'save_data.html');
        $('.save-notice').text('Saving...');

        let notifyObj = null;
        ajaxWithRetry({
            url: '/api/save_content',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            }, 
            (retryData) => {
                if(!notifyObj || notifyObj.state != 'open') {
                    notifyObj = new PNotify({
                        title: 'Retrying Save...',
                        text: 'Your content didn’t save successfully the first time. Retrying now and this should take up to 30 seconds after which you can get back to work.',
                        hide: false,
                        width: '30rem',
                        buttons: { sticker: false },
                    })
                }
            }
            )
            .done(function(res, status, xhr){
                    if(notifyObj && notifyObj.state == 'open'){ 
                        notifyObj.remove(); // remove the retry notification
                        new PNotify({
                            title: 'Saved successfully',
                            text: 'Your content has been saved successfully. You can continue editing.',
                            hide: true,
                            type: 'success',
                            width: '30rem',
                            buttons: { sticker: false },
                            delay: 5000
                        });
                        notifyObj = null;
                    }
                    kriyaEditor.settings.save.classList.add("disabled");
                    var d = new Date();
                    var hours = d.getHours();
                    var minutes = d.getMinutes();
                    var ampm = hours >= 12 ? 'pm' : 'am';
                    hours = hours % 12;
                    hours = hours ? hours : 12; // the hour '0' should be '12'
                    minutes = minutes < 10 ? '0'+minutes : minutes;
                    var strTime = hours + ':' + minutes + ' ' + ampm;
                    $('.save-notice').text('All changes saved at ' + strTime);

                    if(kriya.config.exportOnsave && typeof(eventHandler.menu.export[kriya.config.exportOnsave]) == "function"){
                        eventHandler.menu.export[kriya.config.exportOnsave]('','',false);
                        kriya.config.exportOnsave = null;
                    }
                    kriya.config.saveQueue.splice(0,1);						
                
                kriyaEditor.htmlContent = kriyaEditor.settings.contentNode.innerHTML;
                kriyaEditor.init.saveQueueData();
            }).fail(function(err){
                    if(notifyObj && notifyObj.state == 'open') {
                        notifyObj.remove();
                    }
                    let inputData = kriya.config.saveQueue[0]['content']
                    kriya.config.saveQueue.splice(0,1);
                    
                    //Save failure create snapshot 
                    var content = $('div.WordSection1')[0].outerHTML
                    content += $('#navContainer #queryDivNode')[0].outerHTML
                    
                    if(err && err.responseJSON && err.responseJSON.status && err.responseJSON.status.error) {
                        content += `<div id="error-message">${err.responseJSON.status.error}</div>`
                    }else if(err && err.responseText) {
                        content += `<div id="error-message">${err.responseText}</div>`
                    }
                    // Construct save logs
                    let saveLogs = '<savelogs>';
                    kriya.config.lastsaveQueue.forEach((saveItem, index) => {
                        saveLogs += `<log index="${index + 1}"><type>${saveItem.action || ''}</type><timestamp>${saveItem.timeStamp}</timestamp>${saveItem.content || ''}</log>`;
                    });
                    saveLogs += '</savelogs>';

                    content += `<div id="input-request">${inputData}</div>`

                    // add the browser details for debugging
                    content += `<div id="user-agent">${navigator.userAgent}</div>`;

                    var settings = {
                        icon: '',
                        title: "Temporary saving issue",
                        text: "We're having a temporary issue saving your latest changes. Please refresh the page or contact support if it continues.",
                        size: 'pop-md',
                        closeMenu: 'false',
                        footHTML: "<span class='btn btn-success refreshPage' onclick='location.reload()'>Refresh page</span> " + '<span class=\'btn btn-success contactSupport\' data-message=\'{"click":{"funcToCall":"reportIssueModal","param":{"msg":" "},"channel":"welcome","topic":"signoff"}}\'>' + "Contact support</span>",
                        popupName: 'save_failure',
                    };


                    kriya.actions.confirmation(settings);
                    $('.save-notice').text('Content saving failed');

                    var parameters = {
                        'customer': kriya.config.content.customer,
                        'project': kriya.config.content.project,
                        'doi': kriya.config.content.doi,
                        'content': encodeURIComponent(content),
                        'saveLogs': encodeURIComponent(saveLogs),
                        'fileName' : 'save_failure'
                    };
                    kriya.general.sendAPIRequest('createsnapshots',parameters,function(res){
                        //console.log('Data Saved');
                    });
                
            })
    }
    return kriyaEditor;
})(kriyaEditor || {});
