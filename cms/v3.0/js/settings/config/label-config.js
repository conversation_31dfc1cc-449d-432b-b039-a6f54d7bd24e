var labelDetails = {
    systemmeta : {
        "pressdate":"Press date",
        "press-release-status":"Press release status",
        "strikingImage":"Striking image",
        "article-version":"Article version",
        "digest":"Digest",
        "no-digest-set-date":"No digest set date",
        "decisionLetter":"Decision letter",
        "digest-load-date":"Digest load date",
        "decision-letter-load-date":"Decision letter load date",
        "manuscript-type":"Manuscript type",
        "resupply":"Resupply",
        "mcq":"MCQ",
        "supplement-label":"Supplement",
        "video-label":"Video",
        "video-abstract-label": "Video Abstract",
        "days-overdue": "Days overdue",
        "fig-count": "Figures",
        "table-count": "Tables",
        "equation-count":"Equations",
        "word-count":"Word count",
        "word-count-without-ref":"Word count without reference",
        "volume":"Volume",
        "issue":"Issue",
        "articleType":"Article type",
        "related-article":"Related Article(s)",
        'doi': "DOI",
        "iteration": "Iteration",
        "ccc-payment-awaiting": "CCC Payment Status",
        "OASwitchBoard": "OASwitchBoard",
        "project": "Project Name",
        "orderStatus": "Order Status",
        "paymentStatus": "Payment Status",
        "PurchaseOrder": "Purchase Order",
        "page-count": "Page Count",
        "ccc_manuscript_status": "Manuscript status",
        "ccc-integration-status": "CCC integration status",
        "subject-areas": "Section head",
        "epubDate" : "Published date",
        "color-payment-awaiting": "Color Payment awaiting",
        "DS-PMC": "DS PMC",
        "DS-JISC": "DS JISC",
        "DS-Clarivate": "DS Clarivate",
        "DS-ValidationCheck": "Validation Check",
        "editors-pick": "Ed Pick",
        "DS-ePress": "ePress",
        "color-payment-released": "Color Payment released",
        "DS-Crossref": "DS Crossref",
        "DS-PUBMED": "DS PUBMED",
        "DS-Doaj": "DS Doaj",
        "DS-Dimension": "DS Dimension",
        "subscribed": "subscribed",
        "transferred-journal":"Transferred Journal",
        "transferred-journal-from":"Transferred Journal From",
        "transferred-count":"Transferred Count",
        "stub-id": "Stub ID",
        "stub-name": "Stub Name",
        "stub-topic": "Stub Topic",
        "days-in-stage": "Days in stage",
        "eligibility": "CCC Eligibility"
    },
    systemmetaedit : {
        "lay-summary": "Lay summary",
        "press-release-date":"Press release date",
        "license-type":"License type",
        "lang-attention-paper":"Language attention paper",
        "CELevel":"CE level",
        "special-issue":"Special issue",
        "color-in-print":"Color in print",
        "press-release":"Press release",
        "press-release-applied-date":"Press release applied date",
        "external-press-release":"External press release",
        "mcq-flag":"MCQ flag",
        "podcast":"Podcast",
        "co-publication": "Co-publication",
        "RetroOA" : "Retrospective Open Access",
        "CCCRequiredYes": "CCC required yes",
        "CCCRequiredNo": "CCC required no",
        "PapVolIssue": "PAP",
        "EmbargoDate": "Embargo Date",
        "epubDate" : "Publication Date"
    },
    systemmetaeditType : {
        "lay-summary": "Toggle",
        "press-release-date":"Date",
        "license-type":"Dropdown",
        "lang-attention-paper":"Toggle",
        "CELevel":"Dropdown",
        "special-issue":"Dropdown",
        "color-in-print":"Toggle",
        "press-release":"Toggle",
        "press-release-applied-date":"Date",
        "external-press-release":"Toggle",
        "mcq-flag":"Toggle",
        "podcast":"Toggle",
        "co-publication":"Toggle",
        "RetroOA" : "Toggle",
        "CCCRequiredYes": "Toggle",
        "CCCRequiredNo": "Toggle",
        "PapVolIssue": "Toggle",
        "EmbargoDate": "Embargo Date",
        "epubDate": "Date"
    },
    /*
    contains the list of dropdowns for which the values must be fetched from __basic_config
    The key will be the label source and value will be the key used in the __basic_config
    */
    dropdownfromconfig: { 
        "license-type" : "License",
        "CELevel": "CELevel"
    }
}

this.labelDetails = labelDetails;