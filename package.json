{"name": "kriya", "version": "2.0.0", "description": "kriya", "main": "app.js", "private": true, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "Exeter Premedia Services", "keywords": ["publishing"], "scripts": {"dev": "node app.js -e $ENV_FILE -p $PORT -s $SERVER_NAME", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "_moduleAliases": {"@core": "_core", "@utils": "cms/v3.0/api/utils", "@helpers": "cms/v3.0/api/helpers", "@getapi": "cms/v3.0/api/get", "@postapi": "cms/v3.0/api/post", "@apiconfig": "cms/v3.0/api/config", "@src": "src"}, "dependencies": {"@aws-sdk/client-s3": "^3.772.0", "@aws-sdk/client-scheduler": "^3.744.0", "@aws-sdk/client-sfn": "^3.772.0", "@aws-sdk/client-sqs": "^3.840.0", "@elastic/ecs-winston-format": "^1.5.3", "@kriyadocs/k-utils": "^1.0.22", "@opentelemetry/auto-instrumentations-node": "^0.62.0", "@opentelemetry/exporter-prometheus": "^0.203.0", "@opentelemetry/exporter-trace-otlp-http": "^0.203.0", "@opentelemetry/sdk-node": "^0.203.0", "@peculiar/webcrypto": "^1.5.0", "7zip-min": "^1.3.3", "ajv": "^8.17.1", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "axios": "^1.8.4", "bcrypt": "^6.0.0", "body-parser": "^1.12.3", "bullmq": "^5.52.2", "cheerio": "^0.22.0", "chokidar": "^1.7.0", "client-oauth2": "^4.1.0", "compression": "^1.6.2", "cookies": "^0.7.1", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-writer": "^1.6.0", "dotenv": "^8.2.0", "easy-pdf-merge": "^0.1.3", "elastic-apm-node": "^4.13.0", "encodeurl": "^1.0.1", "encoding": "^0.1.12", "entities": "^1.1.1", "escape-html": "^1.0.3", "express": "^4.12.3", "express-session": "^1.14.0", "express-status-monitor": "^0.1.7", "extract-zip": "^1.6.7", "file-matcher": "^1.1.0", "file-system": "^2.2.1", "follow-redirects": "^1.7.0", "formidable": "^1.0.17", "fs-extra": "^4.0.0", "googleapis": "^39.2.0", "helmet": "^3.1.0", "html-entities": "^1.2.1", "html-to-text": "^9.0.5", "image-thumbnail": "^1.0.7", "immutable": "^3.8.1", "inline-css": "^4.0.3", "inngest": "^3.37.0", "ip": "^1.1.5", "JSONPath": "^0.11.2", "jsonwebtoken": "^7.2.1", "libxmljs": "npm:libxmljs2@0.35.0", "luxon": "3.4.4", "luxon-business-days": "3.0.1", "mailcomposer": "^4.0.2", "marko": "^3.10.0", "memorystore": "^1.6.7", "merge-json": "0.1.0-b.3", "mkdirp": "^0.5.1", "module-alias": "^2.2.3", "moment-business-days": "^1.0.6", "moment-business-time": "^0.7.1", "multer": "^1.3.0", "nanoid": "^3.3.4", "nodemailer": "^3.1.4", "openai": "^5.8.2", "parseurl": "^1.3.2", "pm2": "^2.7.1", "q": "^1.5.0", "redis": "3.1.2", "request": "^2.81.0", "request-promise": "^4.2.0", "rethinkdb": "^2.3.3", "s3": "npm:s3-node@0.0.1", "sanitize-filename": "^1.6.3", "sanitize-html": "^2.13.0", "send": "^0.16.1", "simple-git": "^1.79.0", "strformat": "0.0.7", "striptags": "^3.1.1", "swagger-ui-express": "^5.0.1", "sync-request": "^3.0.1", "tablefilter": "^0.4.34", "ua-parser-js": "^0.7.17", "unirest": "^0.5.1", "url": "^0.11.0", "uuid": "^3.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "word-count": "^0.2.2", "xlsx": "^0.15.5", "xml-js": "^1.5.1", "xml-to-json-stream": "^1.1.0", "xmlbuilder": "^13.0.2", "xmldom": "^0.1.22", "xpath": "^0.0.23", "xregexp": "^4.2.4", "xsltproc": "0.0.4", "yargs": "^18.0.0", "zod": "3.23.8", "jszip": "^3.10.1"}, "devDependencies": {"jest": "^29.7.0", "jest-html-reporter": "^4.1.0", "supertest": "^7.1.0"}}